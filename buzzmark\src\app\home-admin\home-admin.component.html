<!--<div class="flex items-center justify-center">
      <button (click)="toggleDarkMode()" class="p-2 rounded-full hover:bg-blue-500 dark:hover:bg-blue-700">
        <svg *ngIf="isDarkMode" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path></svg>
        <svg *ngIf="!isDarkMode" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path></svg>
      </button>
    </div>-->
<div class="main" style="background-color: rgb(41, 38, 38); height: 100vh;z-index: 0; margin-left: 250px;">
  <div class="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
  <!-- Visitors Chart -->
  <div class="bg-white shadow rounded-xl p-4">
    <h2 class="text-lg font-bold mb-4">Visiteurs par jour</h2>
    <canvas baseChart
      [datasets]="[{ data: visitorData, label: 'Visiteurs' }]"
      [labels]="visitorLabels"
      [type]="'line'">
    </canvas>
  </div>

  <!-- Users Growth Chart -->
  <div class="bg-white shadow rounded-xl p-4">
    <h2 class="text-lg font-bold mb-4">Croissance des utilisateurs</h2>
    <canvas baseChart
      [datasets]="[{ data: userData, label: 'Utilisateurs cumulés' }]"
      [labels]="userLabels"
      [type]="'line'">
    </canvas>
  </div>

  <!-- Companies vs Influencers -->
  <div class="bg-white shadow rounded-xl p-4 col-span-2">
    <h2 class="text-lg font-bold mb-4">Répartition Companies vs Influencers</h2>
    <canvas baseChart
      [datasets]="[{ data: [companies, influencers], label: 'Répartition' }]"
      [labels]="['Companies', 'Influencers']"
      [type]="'doughnut'">
    </canvas>
  </div>
  <div class="bg-white shadow rounded-xl p-4 col-span-2">
  <h2 class="text-lg font-bold mb-4">Prévision des inscriptions (7 prochains jours)</h2>
  <canvas baseChart
    [datasets]="[{ data: predictionData, label: 'Prévision utilisateurs' }]"
    [labels]="predictionLabels"
    [chartType]="'line'">
  </canvas>
</div>
</div>

</div>
<div class="sidebar fixed top-0 left-0 h-full w-16 bg-black-800  dark:bg-blue-800 text-white p-6 flex flex-col justify-between transition-all duration-300 lg:w-64 md:w-16 lg:translate-x-0 -translate-x-full md:translate-x-0" [ngClass]="{'translate-x-0 w-64': isSidebarOpen}" style="background-color: black; font-size: 20px; border:5px solid #f97316; ">
  <!-- Logo et Slogan -->
  <div>
    <div class="flex items-center mb-8">
      <div>
        <h1 [ngClass]="{'block': isSidebarOpen, 'lg:block md:hidden hidden': !isSidebarOpen}" style="color: rgb(255, 102, 1); font-family: cursive; font-weight: bold; text-shadow: 4px 2px 4px rgba(5, 8, 166, 0.3); text-align: center; font-size: 40px;">Buzzmark</h1>
        <p [ngClass]="{'block': isSidebarOpen, 'lg:block md:hidden hidden': !isSidebarOpen}" style="color: white; font-family: futura; font-weight: bold; text-shadow: 4px 2px 4px rgba(5, 8, 166, 0.3); text-align: center; font-size: 14px;">Boost Your Brand, Amplify Your Reach.</p>
      </div>
    </div>

    <!-- Menu -->
    <nav class="space-y-2">
      <a (click)="navigateTo('campaigns')" class="flex items-center p-2 rounded-lg hover:bg-blue-500 dark:hover:bg-blue-700 cursor-pointer">
        <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2V9a2 2 0 00-2-2h-2a2 2 0 00-2 2v10"></path></svg>
        <span [ngClass]="{'block': isSidebarOpen, 'lg:block md:hidden hidden': !isSidebarOpen} ">Dashboard</span>
      </a>
      <!-- Repeat for other menu items -->
      <a (click)="navigateTo('offers')" class="flex items-center p-2 rounded-lg hover:bg-blue-500 dark:hover:bg-blue-700 cursor-pointer">
        <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
        <span [ngClass]="{'block': isSidebarOpen, 'lg:block md:hidden hidden': !isSidebarOpen} ">Companies</span>
      </a>
      <a (click)="navigateTo('collaborators')" class="flex items-center p-2 rounded-lg hover:bg-blue-500 dark:hover:bg-blue-700 cursor-pointer">
        <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v2h5m-2-2a3 3 0 005.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path></svg>
        <span [ngClass]="{'block': isSidebarOpen, 'lg:block md:hidden hidden': !isSidebarOpen}">Influencers</span>
      </a>
      <a (click)="navigateTo('messaging')" class="flex items-center p-2 rounded-lg hover:bg-blue-500 dark:hover:bg-blue-700 cursor-pointer">
        <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path></svg>
        <span [ngClass]="{'block': isSidebarOpen, 'lg:block md:hidden hidden': !isSidebarOpen}">Messaging</span>
      </a>
      <a (click)="navigateTo('notifications')" class="flex items-center p-2 rounded-lg hover:bg-blue-500 dark:hover:bg-blue-700 cursor-pointer">
        <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path></svg>
        <span [ngClass]="{'block': isSidebarOpen, 'lg:block md:hidden hidden': !isSidebarOpen}">Offers</span>
      </a>
     <a (click)="navigateTo('postulations')" 
   class="flex items-center p-2 rounded-lg hover:bg-blue-500 dark:hover:bg-blue-700 cursor-pointer">
  <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
          d="M9 12l2 2l4-4m1-5H7a2 2 0 00-2 2v12a2 
             2 0 002 2h10a2 2 0 002-2V7a2 
             2 0 00-2-2z" />
  </svg>
  <span [ngClass]="{'block': isSidebarOpen, 'lg:block md:hidden hidden': !isSidebarOpen}">
    Postulations
  </span>
</a>
<a (click)="navigateTo('configuration')" 
   class="flex items-center p-2 rounded-lg hover:bg-blue-500 dark:hover:bg-blue-700 cursor-pointer">
  <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
          d="M11.983 13.983a2 2 0 110-3.966 2 2 0 
             010 3.966zm8.334-1.4a7.965 7.965 0 000-1.166l2.07-1.61a.5.5 
             0 00.12-.64l-1.96-3.392a.5.5 
             0 00-.6-.22l-2.44.98a7.952 
             7.952 0 00-2.02-1.166l-.37-2.6a.5.5 
             0 00-.5-.42h-3.92a.5.5 
             0 00-.5.42l-.37 2.6a7.952 
             7.952 0 00-2.02 1.166l-2.44-.98a.5.5 
             0 00-.6.22l-1.96 3.392a.5.5 
             0 00.12.64l2.07 1.61a7.965 
             7.965 0 000 1.166l-2.07 1.61a.5.5 
             0 00-.12.64l1.96 3.392a.5.5 
             0 00.6.22l2.44-.98a7.952 
             7.952 0 002.02 1.166l.37 2.6a.5.5 
             0 00.5.42h3.92a.5.5 
             0 00.5-.42l.37-2.6a7.952 
             7.952 0 002.02-1.166l2.44.98a.5.5 
             0 00.6-.22l1.96-3.392a.5.5 
             0 00-.12-.64l-2.07-1.61z" />
  </svg>
  <span [ngClass]="{'block': isSidebarOpen, 'lg:block md:hidden hidden': !isSidebarOpen}">
    Configuration
  </span>
</a>

      
      
      <a (click)="logout()" class="flex items-center p-2 rounded-lg hover:bg-orange-500 dark:hover:bg-orange-600 cursor-pointer">
        <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path></svg>
        <span [ngClass]="{'block': isSidebarOpen, 'lg:block md:hidden hidden': !isSidebarOpen}">Logout</span>
      </a>
      <div class="flex items-center space-x-2 mt-2"style="position: absolute; bottom: 150px; left: 20px;">
        <span class="h-3 w-3 rounded-full bg-orange-500 animate-pulse" style="position: absolute; top: -545px; left: 1178px;" ></span>
        <span class=" font-medium " [ngClass]="{'block': isSidebarOpen, 'lg:block md:hidden hidden': !isSidebarOpen}" style="color: #f97316; font-family:monospace; font-weight: bold; text-shadow: 4px 2px 4px rgba(5, 8, 166, 0.3); text-align: left; font-size: 27px; width: 290px; position: absolute; top: -550px; left: 1190px; ">
          {{ adminName  }}
        </span>
      </div>
    </nav>
  </div>
</div>
    <!-- Mode sombre/clair -->
    


  <!-- Bouton Hamburger (mobile) -->
  <button class="lg:hidden fixed top-4 left-4 z-50 p-2 rounded-md bg-blue-600 dark:bg-blue-800 text-white" (click)="toggleSidebar()">
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg>
  </button>

  
      
    

