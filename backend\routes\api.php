<?php
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\OfferController;
use App\Http\Controllers\Api\StatsController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\MessagingController;
use App\Http\Controllers\ConversationController;


Route::middleware('auth:sanctum')->group(function () {
    // Conversations
    Route::get('/conversations', [MessagingController::class, 'index']);          // liste conversations de l'utilisateur
    Route::post('/conversations', [MessagingController::class, 'store']);        // créer conversation + participants
    Route::get('/conversations/{id}', [MessagingController::class, 'show']);     // messages d'une conversation

    // Messages
    Route::post('/messages', [MessagingController::class, 'sendMessage']);      // envoyer message
    Route::post('/messages/{id}/read', [MessagingController::class, 'markAsRead']); // marquer lus par conversation (ou message)
});
Route::post('/pre-register', [AuthController::class, 'preRegister']);
Route::post('/process-payment', [AuthController::class, 'processPayment']);
Route::post('/verify-email', [AuthController::class, 'verifyEmail']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/skip', [AuthController::class, 'skip']);
Route::get('/companies', function () {
    return Company::select('id', 'company_name', 'logo', 'description')->get();
});
ROute::post('/adminlogin', [AuthController::class, 'adminlogin']);
Route::post('send-code', [AuthController::class, 'sendCode']);
Route::post('verify-code', [AuthController::class, 'verifyCode']);
Route::post('reset-password', [AuthController::class, 'resetPassword']);
Route::post('contact',[AuthController::class, 'contact']);
Route::get('/stats', function () {
    // 1. Nombre de visiteurs par jour (table visits)
    $visitors = DB::table('visits')
        ->selectRaw('DATE(created_at) as date, COUNT(*) as total')
        ->groupBy('date')
        ->orderBy('date')
        ->get();

    // 2. Nombre d'utilisateurs cumulés par jour (table users)
    $usersDaily = DB::table('users')
        ->selectRaw('DATE(created_at) as date, COUNT(*) as total')
        ->groupBy('date')
        ->orderBy('date')
        ->get();

    // Calculer le cumul jour après jour
    $cumulativeUsers = [];
    $sum = 0;
    foreach ($usersDaily as $u) {
        $sum += $u->total;
        $cumulativeUsers[] = [
            "date" => $u->date,
            "total" => $sum
        ];
    }

    // 3. Répartition entre Companies et Influencers
    $companies = DB::table('companies')->count();
    $influencers = DB::table('influencers')->count();

    return response()->json([
        "visitors" => $visitors,
        "users_cumulative" => $cumulativeUsers,
        "companies" => $companies,
        "influencers" => $influencers
    ]);
});
// Routes protégées par Sanctum
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/profile-setup', [AuthController::class, 'profileSetup']);


Route::get('/auth/google/redirect', [AuthController::class, 'redirect']);
Route::get('/auth/google/callback', [AuthController::class, 'callback']);

    
    Route::get('/refresh-token', [AuthController::class, 'refreshToken']);
    Route::get('/offers', [OfferController::class, 'index']);
    Route::post('/offers', [OfferController::class, 'store']);
    Route::put('/offers/{id}', [OfferController::class, 'update']);
    Route::get('/user', [UserController::class, 'getUser']);
    Route::put('/user/update', [UserController::class, 'updateUser']);
    Route::delete('/user/delete', [UserController::class, 'deleteUser']);
    Route::put('/user/update-subscription', [UserController::class, 'updateSubscription']);
    Route::get('/company-info', [AuthController::class, 'getInfo']);

});
Route::post('/resend-verification', [AuthController::class, 'resendVerification']);
Route::post('/admin-info', [AuthController::class, 'admininfo']);
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/conversations', [ConversationController::class, 'index']);
    Route::post('/conversations', [ConversationController::class, 'store']);
    Route::get('/conversations/{id}', [ConversationController::class, 'show']);

    Route::post('/conversations/{conversationId}/messages', [MessagingController::class, 'store']);
    Route::delete('/conversations/{conversationId}/messages/{messageId}', [MessagingController::class, 'destroy']);
    Route::post('/conversations/{conversationId}/messages/{messageId}/forward', [MessagingController::class, 'forward']);
    Route::post('/conversations/{conversationId}/messages/{messageId}/read', [MessagingController::class, 'markAsRead']);
    Route::post('/conversations/{conversationId}/typing', [MessagingController::class, 'sendTyping']);

    Route::get('/users/search', [UserController::class, 'search']);
});
