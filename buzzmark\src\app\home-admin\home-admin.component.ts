import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { RouterOutlet } from '@angular/router';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import Swal from 'sweetalert2';
import { ActivatedRoute } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Route } from '@angular/router';
import { BaseChartDirective } from 'ng2-charts';
@Component({
  selector: 'app-home-admin',
  standalone: true,
  imports: [CommonModule, MatCardModule, RouterOutlet, BaseChartDirective],
  templateUrl: './home-admin.component.html',
  styleUrls: ['./home-admin.component.css']
})
export class HomeAdminComponent implements OnInit {
  isSidebarOpen = false;
  isDarkMode = false;
  adminName = 'Loading...';
   visitorData: number[] = [];
  visitorLabels: string[] = [];

  userData: number[] = [];
  userLabels: string[] = [];

  companies: number = 0;
  influencers: number = 0;
  predictionData: number[] = [];
predictionLabels: string[] = [];

  constructor(private router: Router, private http: HttpClient, private route: ActivatedRoute,
  private snackBar: MatSnackBar
  ) {
    this.initializeDarkMode();
    this.route.queryParams.subscribe(params => {
      this.adminName = params['usernameOrEmail'] || '';
    });
  }

  ngOnInit(): void {
    this.fetchAdminName();
    this.http.get<any>('http://localhost:8000/api/stats').subscribe(res => {
      // Visitors per day
      this.visitorData = res.visitors.map((v: any) => v.total);
      this.visitorLabels = res.visitors.map((v: any) => v.date);

      // Users cumulative
      this.userData = res.users_cumulative.map((u: any) => u.total);
      this.userLabels = res.users_cumulative.map((u: any) => u.date);

      // Companies & influencers
      this.companies = res.companies;
      this.influencers = res.influencers;
    });
    this.http.get<any>('http://localhost:8000/api/stats/prediction').subscribe(res => {
    // Prévisions
    this.predictionData = res.forecast.map((p: any) => p.expected);
    this.predictionLabels = res.forecast.map((p: any) => p.date);
  });

  }

  

  private initializeDarkMode(): void {
    const savedDarkMode = localStorage.getItem('darkMode');
    if (savedDarkMode !== null) {
      this.isDarkMode = savedDarkMode === 'true';
    } else {
      this.isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    this.applyDarkMode();
  }

  private applyDarkMode(): void {
    if (this.isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }

  navigateTo(route: string): void {
    this.router.navigate(['/dashboard/admin', route]).then(success => {
   
  })}

  toggleSidebar(): void {
    this.isSidebarOpen = !this.isSidebarOpen;
  }

  toggleDarkMode(): void {
    this.isDarkMode = !this.isDarkMode;
    localStorage.setItem('darkMode', this.isDarkMode.toString());
    this.applyDarkMode();
   
    };
  

  logout(): void {
    Swal.fire({
      title: 'Logout',
      text: 'Are you sure to Disconnect ?',
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Yes,Disconnect',
      cancelButtonText: 'Cancel',
      confirmButtonColor: '#ff661a',
      
      cancelButtonColor: '#6b7280',
      
    }).then((result) => {
      if (result.isConfirmed) {
        localStorage.removeItem('auth_token');
        this.router.navigate(['/home']).then(() => {
          Swal.fire({
            title: 'Disconnected',
            text: 'You have been disconnected successfully.',
            icon: 'success',
            timer: 2500,
            showConfirmButton: false,
            confirmButtonColor: '#ff661a',
            
          });
        });
      }
    });
  }

  fetchAdminName() {
    const usernameOrEmail = this.adminName;
    const token = localStorage.getItem('auth_token');
    console.log('usernameOrEmail:', usernameOrEmail);
    if (!token) {
      Swal.fire({
        title: 'Error',
        text: 'Error fetching admin name.',
        icon: 'error',
        confirmButtonColor: '#ff661a'
      });
      this.adminName = 'No Admin';
      this.router.navigate(['/home']);
      return;
    }
   
    const data = {
      usernameOrEmail: usernameOrEmail
    };
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`
    });
    this.http.post('http://localhost:8000/api/admin-info', data).subscribe({
      next: (res) => {
        this.adminName = (res as any).adminname || 'Unnamed Admin';
        console.log('Admin info response:', res); // Debug
        
     
      },
      error: (err) => {
        console.error('Error fetching admin name:', err);
        Swal.fire({
          title: 'Error',
          text: 'Error fetching admin name.',
          icon: 'error',
          confirmButtonColor: '#ff661a'
        });
        this.adminName = 'Error Loading Admin';
      }
    });
    
  }
}