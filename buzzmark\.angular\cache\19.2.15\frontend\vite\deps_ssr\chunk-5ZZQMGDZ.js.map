{"version": 3, "sources": ["../../../../../../node_modules/@angular/router/fesm2022/router-Dwfin5Au.mjs", "../../../../../../node_modules/@angular/router/fesm2022/router_module-DTJgGWLd.mjs", "../../../../../../node_modules/@angular/router/fesm2022/router.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { DOCUMENT, Location } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { ɵisPromise as _isPromise, ɵRuntimeError as _RuntimeError, Injectable, ɵisNgModule as _isNgModule, isStandalone, createEnvironmentInjector, InjectionToken, EventEmitter, input, inject, ViewContainerRef, ChangeDetectorRef, Output, Input, Directive, reflectComponentType, Component, ɵisInjectable as _isInjectable, runInInjectionContext, NgModuleFactory, Compiler, NgZone, afterNextRender, EnvironmentInjector, DestroyRef, ɵConsole as _Console, ɵPendingTasksInternal as _PendingTasksInternal } from '@angular/core';\nimport { isObservable, from, of, BehaviorSubject, combineLatest, EmptyError, concat, defer, pipe, throwError, EMPTY, ConnectableObservable, Subject, Subscription } from 'rxjs';\nimport { map, switchMap, take, startWith, filter, mergeMap, first, concatMap, tap, catchError, scan, defaultIfEmpty, last as last$1, takeLast, finalize, refCount, takeUntil } from 'rxjs/operators';\nimport * as i1 from '@angular/platform-browser';\n\n/**\n * The primary routing outlet.\n *\n * @publicApi\n */\nconst PRIMARY_OUTLET = 'primary';\n/**\n * A private symbol used to store the value of `Route.title` inside the `Route.data` if it is a\n * static string or `Route.resolve` if anything else. This allows us to reuse the existing route\n * data/resolvers to support the title feature without new instrumentation in the `Router` pipeline.\n */\nconst RouteTitleKey = /* @__PURE__ */Symbol('RouteTitle');\nclass ParamsAsMap {\n  params;\n  constructor(params) {\n    this.params = params || {};\n  }\n  has(name) {\n    return Object.prototype.hasOwnProperty.call(this.params, name);\n  }\n  get(name) {\n    if (this.has(name)) {\n      const v = this.params[name];\n      return Array.isArray(v) ? v[0] : v;\n    }\n    return null;\n  }\n  getAll(name) {\n    if (this.has(name)) {\n      const v = this.params[name];\n      return Array.isArray(v) ? v : [v];\n    }\n    return [];\n  }\n  get keys() {\n    return Object.keys(this.params);\n  }\n}\n/**\n * Converts a `Params` instance to a `ParamMap`.\n * @param params The instance to convert.\n * @returns The new map instance.\n *\n * @publicApi\n */\nfunction convertToParamMap(params) {\n  return new ParamsAsMap(params);\n}\n/**\n * Matches the route configuration (`route`) against the actual URL (`segments`).\n *\n * When no matcher is defined on a `Route`, this is the matcher used by the Router by default.\n *\n * @param segments The remaining unmatched segments in the current navigation\n * @param segmentGroup The current segment group being matched\n * @param route The `Route` to match against.\n *\n * @see {@link UrlMatchResult}\n * @see {@link Route}\n *\n * @returns The resulting match information or `null` if the `route` should not match.\n * @publicApi\n */\nfunction defaultUrlMatcher(segments, segmentGroup, route) {\n  const parts = route.path.split('/');\n  if (parts.length > segments.length) {\n    // The actual URL is shorter than the config, no match\n    return null;\n  }\n  if (route.pathMatch === 'full' && (segmentGroup.hasChildren() || parts.length < segments.length)) {\n    // The config is longer than the actual URL but we are looking for a full match, return null\n    return null;\n  }\n  const posParams = {};\n  // Check each config part against the actual URL\n  for (let index = 0; index < parts.length; index++) {\n    const part = parts[index];\n    const segment = segments[index];\n    const isParameter = part[0] === ':';\n    if (isParameter) {\n      posParams[part.substring(1)] = segment;\n    } else if (part !== segment.path) {\n      // The actual URL part does not match the config, no match\n      return null;\n    }\n  }\n  return {\n    consumed: segments.slice(0, parts.length),\n    posParams\n  };\n}\nfunction shallowEqualArrays(a, b) {\n  if (a.length !== b.length) return false;\n  for (let i = 0; i < a.length; ++i) {\n    if (!shallowEqual(a[i], b[i])) return false;\n  }\n  return true;\n}\nfunction shallowEqual(a, b) {\n  // While `undefined` should never be possible, it would sometimes be the case in IE 11\n  // and pre-chromium Edge. The check below accounts for this edge case.\n  const k1 = a ? getDataKeys(a) : undefined;\n  const k2 = b ? getDataKeys(b) : undefined;\n  if (!k1 || !k2 || k1.length != k2.length) {\n    return false;\n  }\n  let key;\n  for (let i = 0; i < k1.length; i++) {\n    key = k1[i];\n    if (!equalArraysOrString(a[key], b[key])) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * Gets the keys of an object, including `symbol` keys.\n */\nfunction getDataKeys(obj) {\n  return [...Object.keys(obj), ...Object.getOwnPropertySymbols(obj)];\n}\n/**\n * Test equality for arrays of strings or a string.\n */\nfunction equalArraysOrString(a, b) {\n  if (Array.isArray(a) && Array.isArray(b)) {\n    if (a.length !== b.length) return false;\n    const aSorted = [...a].sort();\n    const bSorted = [...b].sort();\n    return aSorted.every((val, index) => bSorted[index] === val);\n  } else {\n    return a === b;\n  }\n}\n/**\n * Return the last element of an array.\n */\nfunction last(a) {\n  return a.length > 0 ? a[a.length - 1] : null;\n}\nfunction wrapIntoObservable(value) {\n  if (isObservable(value)) {\n    return value;\n  }\n  if (_isPromise(value)) {\n    // Use `Promise.resolve()` to wrap promise-like instances.\n    // Required ie when a Resolver returns a AngularJS `$q` promise to correctly trigger the\n    // change detection.\n    return from(Promise.resolve(value));\n  }\n  return of(value);\n}\nconst pathCompareMap = {\n  'exact': equalSegmentGroups,\n  'subset': containsSegmentGroup\n};\nconst paramCompareMap = {\n  'exact': equalParams,\n  'subset': containsParams,\n  'ignored': () => true\n};\nfunction containsTree(container, containee, options) {\n  return pathCompareMap[options.paths](container.root, containee.root, options.matrixParams) && paramCompareMap[options.queryParams](container.queryParams, containee.queryParams) && !(options.fragment === 'exact' && container.fragment !== containee.fragment);\n}\nfunction equalParams(container, containee) {\n  // TODO: This does not handle array params correctly.\n  return shallowEqual(container, containee);\n}\nfunction equalSegmentGroups(container, containee, matrixParams) {\n  if (!equalPath(container.segments, containee.segments)) return false;\n  if (!matrixParamsMatch(container.segments, containee.segments, matrixParams)) {\n    return false;\n  }\n  if (container.numberOfChildren !== containee.numberOfChildren) return false;\n  for (const c in containee.children) {\n    if (!container.children[c]) return false;\n    if (!equalSegmentGroups(container.children[c], containee.children[c], matrixParams)) return false;\n  }\n  return true;\n}\nfunction containsParams(container, containee) {\n  return Object.keys(containee).length <= Object.keys(container).length && Object.keys(containee).every(key => equalArraysOrString(container[key], containee[key]));\n}\nfunction containsSegmentGroup(container, containee, matrixParams) {\n  return containsSegmentGroupHelper(container, containee, containee.segments, matrixParams);\n}\nfunction containsSegmentGroupHelper(container, containee, containeePaths, matrixParams) {\n  if (container.segments.length > containeePaths.length) {\n    const current = container.segments.slice(0, containeePaths.length);\n    if (!equalPath(current, containeePaths)) return false;\n    if (containee.hasChildren()) return false;\n    if (!matrixParamsMatch(current, containeePaths, matrixParams)) return false;\n    return true;\n  } else if (container.segments.length === containeePaths.length) {\n    if (!equalPath(container.segments, containeePaths)) return false;\n    if (!matrixParamsMatch(container.segments, containeePaths, matrixParams)) return false;\n    for (const c in containee.children) {\n      if (!container.children[c]) return false;\n      if (!containsSegmentGroup(container.children[c], containee.children[c], matrixParams)) {\n        return false;\n      }\n    }\n    return true;\n  } else {\n    const current = containeePaths.slice(0, container.segments.length);\n    const next = containeePaths.slice(container.segments.length);\n    if (!equalPath(container.segments, current)) return false;\n    if (!matrixParamsMatch(container.segments, current, matrixParams)) return false;\n    if (!container.children[PRIMARY_OUTLET]) return false;\n    return containsSegmentGroupHelper(container.children[PRIMARY_OUTLET], containee, next, matrixParams);\n  }\n}\nfunction matrixParamsMatch(containerPaths, containeePaths, options) {\n  return containeePaths.every((containeeSegment, i) => {\n    return paramCompareMap[options](containerPaths[i].parameters, containeeSegment.parameters);\n  });\n}\n/**\n * @description\n *\n * Represents the parsed URL.\n *\n * Since a router state is a tree, and the URL is nothing but a serialized state, the URL is a\n * serialized tree.\n * UrlTree is a data structure that provides a lot of affordances in dealing with URLs\n *\n * @usageNotes\n * ### Example\n *\n * ```ts\n * @Component({templateUrl:'template.html'})\n * class MyComponent {\n *   constructor(router: Router) {\n *     const tree: UrlTree =\n *       router.parseUrl('/team/33/(user/victor//support:help)?debug=true#fragment');\n *     const f = tree.fragment; // return 'fragment'\n *     const q = tree.queryParams; // returns {debug: 'true'}\n *     const g: UrlSegmentGroup = tree.root.children[PRIMARY_OUTLET];\n *     const s: UrlSegment[] = g.segments; // returns 2 segments 'team' and '33'\n *     g.children[PRIMARY_OUTLET].segments; // returns 2 segments 'user' and 'victor'\n *     g.children['support'].segments; // return 1 segment 'help'\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nclass UrlTree {\n  root;\n  queryParams;\n  fragment;\n  /** @internal */\n  _queryParamMap;\n  constructor(/** The root segment group of the URL tree */\n  root = new UrlSegmentGroup([], {}), /** The query params of the URL */\n  queryParams = {}, /** The fragment of the URL */\n  fragment = null) {\n    this.root = root;\n    this.queryParams = queryParams;\n    this.fragment = fragment;\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (root.segments.length > 0) {\n        throw new _RuntimeError(4015 /* RuntimeErrorCode.INVALID_ROOT_URL_SEGMENT */, 'The root `UrlSegmentGroup` should not contain `segments`. ' + 'Instead, these segments belong in the `children` so they can be associated with a named outlet.');\n      }\n    }\n  }\n  get queryParamMap() {\n    this._queryParamMap ??= convertToParamMap(this.queryParams);\n    return this._queryParamMap;\n  }\n  /** @docsNotRequired */\n  toString() {\n    return DEFAULT_SERIALIZER.serialize(this);\n  }\n}\n/**\n * @description\n *\n * Represents the parsed URL segment group.\n *\n * See `UrlTree` for more information.\n *\n * @publicApi\n */\nclass UrlSegmentGroup {\n  segments;\n  children;\n  /** The parent node in the url tree */\n  parent = null;\n  constructor(/** The URL segments of this group. See `UrlSegment` for more information */\n  segments, /** The list of children of this group */\n  children) {\n    this.segments = segments;\n    this.children = children;\n    Object.values(children).forEach(v => v.parent = this);\n  }\n  /** Whether the segment has child segments */\n  hasChildren() {\n    return this.numberOfChildren > 0;\n  }\n  /** Number of child segments */\n  get numberOfChildren() {\n    return Object.keys(this.children).length;\n  }\n  /** @docsNotRequired */\n  toString() {\n    return serializePaths(this);\n  }\n}\n/**\n * @description\n *\n * Represents a single URL segment.\n *\n * A UrlSegment is a part of a URL between the two slashes. It contains a path and the matrix\n * parameters associated with the segment.\n *\n * @usageNotes\n * ### Example\n *\n * ```ts\n * @Component({templateUrl:'template.html'})\n * class MyComponent {\n *   constructor(router: Router) {\n *     const tree: UrlTree = router.parseUrl('/team;id=33');\n *     const g: UrlSegmentGroup = tree.root.children[PRIMARY_OUTLET];\n *     const s: UrlSegment[] = g.segments;\n *     s[0].path; // returns 'team'\n *     s[0].parameters; // returns {id: 33}\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nclass UrlSegment {\n  path;\n  parameters;\n  /** @internal */\n  _parameterMap;\n  constructor(/** The path part of a URL segment */\n  path, /** The matrix parameters associated with a segment */\n  parameters) {\n    this.path = path;\n    this.parameters = parameters;\n  }\n  get parameterMap() {\n    this._parameterMap ??= convertToParamMap(this.parameters);\n    return this._parameterMap;\n  }\n  /** @docsNotRequired */\n  toString() {\n    return serializePath(this);\n  }\n}\nfunction equalSegments(as, bs) {\n  return equalPath(as, bs) && as.every((a, i) => shallowEqual(a.parameters, bs[i].parameters));\n}\nfunction equalPath(as, bs) {\n  if (as.length !== bs.length) return false;\n  return as.every((a, i) => a.path === bs[i].path);\n}\nfunction mapChildrenIntoArray(segment, fn) {\n  let res = [];\n  Object.entries(segment.children).forEach(([childOutlet, child]) => {\n    if (childOutlet === PRIMARY_OUTLET) {\n      res = res.concat(fn(child, childOutlet));\n    }\n  });\n  Object.entries(segment.children).forEach(([childOutlet, child]) => {\n    if (childOutlet !== PRIMARY_OUTLET) {\n      res = res.concat(fn(child, childOutlet));\n    }\n  });\n  return res;\n}\n/**\n * @description\n *\n * Serializes and deserializes a URL string into a URL tree.\n *\n * The url serialization strategy is customizable. You can\n * make all URLs case insensitive by providing a custom UrlSerializer.\n *\n * See `DefaultUrlSerializer` for an example of a URL serializer.\n *\n * @publicApi\n */\nclass UrlSerializer {\n  static ɵfac = function UrlSerializer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || UrlSerializer)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: UrlSerializer,\n    factory: () => (() => new DefaultUrlSerializer())(),\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UrlSerializer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: () => new DefaultUrlSerializer()\n    }]\n  }], null, null);\n})();\n/**\n * @description\n *\n * A default implementation of the `UrlSerializer`.\n *\n * Example URLs:\n *\n * ```\n * /inbox/33(popup:compose)\n * /inbox/33;open=true/messages/44\n * ```\n *\n * DefaultUrlSerializer uses parentheses to serialize secondary segments (e.g., popup:compose), the\n * colon syntax to specify the outlet, and the ';parameter=value' syntax (e.g., open=true) to\n * specify route specific parameters.\n *\n * @publicApi\n */\nclass DefaultUrlSerializer {\n  /** Parses a url into a `UrlTree` */\n  parse(url) {\n    const p = new UrlParser(url);\n    return new UrlTree(p.parseRootSegment(), p.parseQueryParams(), p.parseFragment());\n  }\n  /** Converts a `UrlTree` into a url */\n  serialize(tree) {\n    const segment = `/${serializeSegment(tree.root, true)}`;\n    const query = serializeQueryParams(tree.queryParams);\n    const fragment = typeof tree.fragment === `string` ? `#${encodeUriFragment(tree.fragment)}` : '';\n    return `${segment}${query}${fragment}`;\n  }\n}\nconst DEFAULT_SERIALIZER = new DefaultUrlSerializer();\nfunction serializePaths(segment) {\n  return segment.segments.map(p => serializePath(p)).join('/');\n}\nfunction serializeSegment(segment, root) {\n  if (!segment.hasChildren()) {\n    return serializePaths(segment);\n  }\n  if (root) {\n    const primary = segment.children[PRIMARY_OUTLET] ? serializeSegment(segment.children[PRIMARY_OUTLET], false) : '';\n    const children = [];\n    Object.entries(segment.children).forEach(([k, v]) => {\n      if (k !== PRIMARY_OUTLET) {\n        children.push(`${k}:${serializeSegment(v, false)}`);\n      }\n    });\n    return children.length > 0 ? `${primary}(${children.join('//')})` : primary;\n  } else {\n    const children = mapChildrenIntoArray(segment, (v, k) => {\n      if (k === PRIMARY_OUTLET) {\n        return [serializeSegment(segment.children[PRIMARY_OUTLET], false)];\n      }\n      return [`${k}:${serializeSegment(v, false)}`];\n    });\n    // use no parenthesis if the only child is a primary outlet route\n    if (Object.keys(segment.children).length === 1 && segment.children[PRIMARY_OUTLET] != null) {\n      return `${serializePaths(segment)}/${children[0]}`;\n    }\n    return `${serializePaths(segment)}/(${children.join('//')})`;\n  }\n}\n/**\n * Encodes a URI string with the default encoding. This function will only ever be called from\n * `encodeUriQuery` or `encodeUriSegment` as it's the base set of encodings to be used. We need\n * a custom encoding because encodeURIComponent is too aggressive and encodes stuff that doesn't\n * have to be encoded per https://url.spec.whatwg.org.\n */\nfunction encodeUriString(s) {\n  return encodeURIComponent(s).replace(/%40/g, '@').replace(/%3A/gi, ':').replace(/%24/g, '$').replace(/%2C/gi, ',');\n}\n/**\n * This function should be used to encode both keys and values in a query string key/value. In\n * the following URL, you need to call encodeUriQuery on \"k\" and \"v\":\n *\n * http://www.site.org/html;mk=mv?k=v#f\n */\nfunction encodeUriQuery(s) {\n  return encodeUriString(s).replace(/%3B/gi, ';');\n}\n/**\n * This function should be used to encode a URL fragment. In the following URL, you need to call\n * encodeUriFragment on \"f\":\n *\n * http://www.site.org/html;mk=mv?k=v#f\n */\nfunction encodeUriFragment(s) {\n  return encodeURI(s);\n}\n/**\n * This function should be run on any URI segment as well as the key and value in a key/value\n * pair for matrix params. In the following URL, you need to call encodeUriSegment on \"html\",\n * \"mk\", and \"mv\":\n *\n * http://www.site.org/html;mk=mv?k=v#f\n */\nfunction encodeUriSegment(s) {\n  return encodeUriString(s).replace(/\\(/g, '%28').replace(/\\)/g, '%29').replace(/%26/gi, '&');\n}\nfunction decode(s) {\n  return decodeURIComponent(s);\n}\n// Query keys/values should have the \"+\" replaced first, as \"+\" in a query string is \" \".\n// decodeURIComponent function will not decode \"+\" as a space.\nfunction decodeQuery(s) {\n  return decode(s.replace(/\\+/g, '%20'));\n}\nfunction serializePath(path) {\n  return `${encodeUriSegment(path.path)}${serializeMatrixParams(path.parameters)}`;\n}\nfunction serializeMatrixParams(params) {\n  return Object.entries(params).map(([key, value]) => `;${encodeUriSegment(key)}=${encodeUriSegment(value)}`).join('');\n}\nfunction serializeQueryParams(params) {\n  const strParams = Object.entries(params).map(([name, value]) => {\n    return Array.isArray(value) ? value.map(v => `${encodeUriQuery(name)}=${encodeUriQuery(v)}`).join('&') : `${encodeUriQuery(name)}=${encodeUriQuery(value)}`;\n  }).filter(s => s);\n  return strParams.length ? `?${strParams.join('&')}` : '';\n}\nconst SEGMENT_RE = /^[^\\/()?;#]+/;\nfunction matchSegments(str) {\n  const match = str.match(SEGMENT_RE);\n  return match ? match[0] : '';\n}\nconst MATRIX_PARAM_SEGMENT_RE = /^[^\\/()?;=#]+/;\nfunction matchMatrixKeySegments(str) {\n  const match = str.match(MATRIX_PARAM_SEGMENT_RE);\n  return match ? match[0] : '';\n}\nconst QUERY_PARAM_RE = /^[^=?&#]+/;\n// Return the name of the query param at the start of the string or an empty string\nfunction matchQueryParams(str) {\n  const match = str.match(QUERY_PARAM_RE);\n  return match ? match[0] : '';\n}\nconst QUERY_PARAM_VALUE_RE = /^[^&#]+/;\n// Return the value of the query param at the start of the string or an empty string\nfunction matchUrlQueryParamValue(str) {\n  const match = str.match(QUERY_PARAM_VALUE_RE);\n  return match ? match[0] : '';\n}\nclass UrlParser {\n  url;\n  remaining;\n  constructor(url) {\n    this.url = url;\n    this.remaining = url;\n  }\n  parseRootSegment() {\n    this.consumeOptional('/');\n    if (this.remaining === '' || this.peekStartsWith('?') || this.peekStartsWith('#')) {\n      return new UrlSegmentGroup([], {});\n    }\n    // The root segment group never has segments\n    return new UrlSegmentGroup([], this.parseChildren());\n  }\n  parseQueryParams() {\n    const params = {};\n    if (this.consumeOptional('?')) {\n      do {\n        this.parseQueryParam(params);\n      } while (this.consumeOptional('&'));\n    }\n    return params;\n  }\n  parseFragment() {\n    return this.consumeOptional('#') ? decodeURIComponent(this.remaining) : null;\n  }\n  parseChildren() {\n    if (this.remaining === '') {\n      return {};\n    }\n    this.consumeOptional('/');\n    const segments = [];\n    if (!this.peekStartsWith('(')) {\n      segments.push(this.parseSegment());\n    }\n    while (this.peekStartsWith('/') && !this.peekStartsWith('//') && !this.peekStartsWith('/(')) {\n      this.capture('/');\n      segments.push(this.parseSegment());\n    }\n    let children = {};\n    if (this.peekStartsWith('/(')) {\n      this.capture('/');\n      children = this.parseParens(true);\n    }\n    let res = {};\n    if (this.peekStartsWith('(')) {\n      res = this.parseParens(false);\n    }\n    if (segments.length > 0 || Object.keys(children).length > 0) {\n      res[PRIMARY_OUTLET] = new UrlSegmentGroup(segments, children);\n    }\n    return res;\n  }\n  // parse a segment with its matrix parameters\n  // ie `name;k1=v1;k2`\n  parseSegment() {\n    const path = matchSegments(this.remaining);\n    if (path === '' && this.peekStartsWith(';')) {\n      throw new _RuntimeError(4009 /* RuntimeErrorCode.EMPTY_PATH_WITH_PARAMS */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Empty path url segment cannot have parameters: '${this.remaining}'.`);\n    }\n    this.capture(path);\n    return new UrlSegment(decode(path), this.parseMatrixParams());\n  }\n  parseMatrixParams() {\n    const params = {};\n    while (this.consumeOptional(';')) {\n      this.parseParam(params);\n    }\n    return params;\n  }\n  parseParam(params) {\n    const key = matchMatrixKeySegments(this.remaining);\n    if (!key) {\n      return;\n    }\n    this.capture(key);\n    let value = '';\n    if (this.consumeOptional('=')) {\n      const valueMatch = matchSegments(this.remaining);\n      if (valueMatch) {\n        value = valueMatch;\n        this.capture(value);\n      }\n    }\n    params[decode(key)] = decode(value);\n  }\n  // Parse a single query parameter `name[=value]`\n  parseQueryParam(params) {\n    const key = matchQueryParams(this.remaining);\n    if (!key) {\n      return;\n    }\n    this.capture(key);\n    let value = '';\n    if (this.consumeOptional('=')) {\n      const valueMatch = matchUrlQueryParamValue(this.remaining);\n      if (valueMatch) {\n        value = valueMatch;\n        this.capture(value);\n      }\n    }\n    const decodedKey = decodeQuery(key);\n    const decodedVal = decodeQuery(value);\n    if (params.hasOwnProperty(decodedKey)) {\n      // Append to existing values\n      let currentVal = params[decodedKey];\n      if (!Array.isArray(currentVal)) {\n        currentVal = [currentVal];\n        params[decodedKey] = currentVal;\n      }\n      currentVal.push(decodedVal);\n    } else {\n      // Create a new value\n      params[decodedKey] = decodedVal;\n    }\n  }\n  // parse `(a/b//outlet_name:c/d)`\n  parseParens(allowPrimary) {\n    const segments = {};\n    this.capture('(');\n    while (!this.consumeOptional(')') && this.remaining.length > 0) {\n      const path = matchSegments(this.remaining);\n      const next = this.remaining[path.length];\n      // if is is not one of these characters, then the segment was unescaped\n      // or the group was not closed\n      if (next !== '/' && next !== ')' && next !== ';') {\n        throw new _RuntimeError(4010 /* RuntimeErrorCode.UNPARSABLE_URL */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Cannot parse url '${this.url}'`);\n      }\n      let outletName = undefined;\n      if (path.indexOf(':') > -1) {\n        outletName = path.slice(0, path.indexOf(':'));\n        this.capture(outletName);\n        this.capture(':');\n      } else if (allowPrimary) {\n        outletName = PRIMARY_OUTLET;\n      }\n      const children = this.parseChildren();\n      segments[outletName] = Object.keys(children).length === 1 ? children[PRIMARY_OUTLET] : new UrlSegmentGroup([], children);\n      this.consumeOptional('//');\n    }\n    return segments;\n  }\n  peekStartsWith(str) {\n    return this.remaining.startsWith(str);\n  }\n  // Consumes the prefix when it is present and returns whether it has been consumed\n  consumeOptional(str) {\n    if (this.peekStartsWith(str)) {\n      this.remaining = this.remaining.substring(str.length);\n      return true;\n    }\n    return false;\n  }\n  capture(str) {\n    if (!this.consumeOptional(str)) {\n      throw new _RuntimeError(4011 /* RuntimeErrorCode.UNEXPECTED_VALUE_IN_URL */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Expected \"${str}\".`);\n    }\n  }\n}\nfunction createRoot(rootCandidate) {\n  return rootCandidate.segments.length > 0 ? new UrlSegmentGroup([], {\n    [PRIMARY_OUTLET]: rootCandidate\n  }) : rootCandidate;\n}\n/**\n * Recursively\n * - merges primary segment children into their parents\n * - drops empty children (those which have no segments and no children themselves). This latter\n * prevents serializing a group into something like `/a(aux:)`, where `aux` is an empty child\n * segment.\n * - merges named outlets without a primary segment sibling into the children. This prevents\n * serializing a URL like `//(a:a)(b:b) instead of `/(a:a//b:b)` when the aux b route lives on the\n * root but the `a` route lives under an empty path primary route.\n */\nfunction squashSegmentGroup(segmentGroup) {\n  const newChildren = {};\n  for (const [childOutlet, child] of Object.entries(segmentGroup.children)) {\n    const childCandidate = squashSegmentGroup(child);\n    // moves named children in an empty path primary child into this group\n    if (childOutlet === PRIMARY_OUTLET && childCandidate.segments.length === 0 && childCandidate.hasChildren()) {\n      for (const [grandChildOutlet, grandChild] of Object.entries(childCandidate.children)) {\n        newChildren[grandChildOutlet] = grandChild;\n      }\n    } // don't add empty children\n    else if (childCandidate.segments.length > 0 || childCandidate.hasChildren()) {\n      newChildren[childOutlet] = childCandidate;\n    }\n  }\n  const s = new UrlSegmentGroup(segmentGroup.segments, newChildren);\n  return mergeTrivialChildren(s);\n}\n/**\n * When possible, merges the primary outlet child into the parent `UrlSegmentGroup`.\n *\n * When a segment group has only one child which is a primary outlet, merges that child into the\n * parent. That is, the child segment group's segments are merged into the `s` and the child's\n * children become the children of `s`. Think of this like a 'squash', merging the child segment\n * group into the parent.\n */\nfunction mergeTrivialChildren(s) {\n  if (s.numberOfChildren === 1 && s.children[PRIMARY_OUTLET]) {\n    const c = s.children[PRIMARY_OUTLET];\n    return new UrlSegmentGroup(s.segments.concat(c.segments), c.children);\n  }\n  return s;\n}\nfunction isUrlTree(v) {\n  return v instanceof UrlTree;\n}\n\n/**\n * Creates a `UrlTree` relative to an `ActivatedRouteSnapshot`.\n *\n * @publicApi\n *\n *\n * @param relativeTo The `ActivatedRouteSnapshot` to apply the commands to\n * @param commands An array of URL fragments with which to construct the new URL tree.\n * If the path is static, can be the literal URL string. For a dynamic path, pass an array of path\n * segments, followed by the parameters for each segment.\n * The fragments are applied to the one provided in the `relativeTo` parameter.\n * @param queryParams The query parameters for the `UrlTree`. `null` if the `UrlTree` does not have\n *     any query parameters.\n * @param fragment The fragment for the `UrlTree`. `null` if the `UrlTree` does not have a fragment.\n *\n * @usageNotes\n *\n * ```ts\n * // create /team/33/user/11\n * createUrlTreeFromSnapshot(snapshot, ['/team', 33, 'user', 11]);\n *\n * // create /team/33;expand=true/user/11\n * createUrlTreeFromSnapshot(snapshot, ['/team', 33, {expand: true}, 'user', 11]);\n *\n * // you can collapse static segments like this (this works only with the first passed-in value):\n * createUrlTreeFromSnapshot(snapshot, ['/team/33/user', userId]);\n *\n * // If the first segment can contain slashes, and you do not want the router to split it,\n * // you can do the following:\n * createUrlTreeFromSnapshot(snapshot, [{segmentPath: '/one/two'}]);\n *\n * // create /team/33/(user/11//right:chat)\n * createUrlTreeFromSnapshot(snapshot, ['/team', 33, {outlets: {primary: 'user/11', right:\n * 'chat'}}], null, null);\n *\n * // remove the right secondary node\n * createUrlTreeFromSnapshot(snapshot, ['/team', 33, {outlets: {primary: 'user/11', right: null}}]);\n *\n * // For the examples below, assume the current URL is for the `/team/33/user/11` and the\n * `ActivatedRouteSnapshot` points to `user/11`:\n *\n * // navigate to /team/33/user/11/details\n * createUrlTreeFromSnapshot(snapshot, ['details']);\n *\n * // navigate to /team/33/user/22\n * createUrlTreeFromSnapshot(snapshot, ['../22']);\n *\n * // navigate to /team/44/user/22\n * createUrlTreeFromSnapshot(snapshot, ['../../team/44/user/22']);\n * ```\n */\nfunction createUrlTreeFromSnapshot(relativeTo, commands, queryParams = null, fragment = null) {\n  const relativeToUrlSegmentGroup = createSegmentGroupFromRoute(relativeTo);\n  return createUrlTreeFromSegmentGroup(relativeToUrlSegmentGroup, commands, queryParams, fragment);\n}\nfunction createSegmentGroupFromRoute(route) {\n  let targetGroup;\n  function createSegmentGroupFromRouteRecursive(currentRoute) {\n    const childOutlets = {};\n    for (const childSnapshot of currentRoute.children) {\n      const root = createSegmentGroupFromRouteRecursive(childSnapshot);\n      childOutlets[childSnapshot.outlet] = root;\n    }\n    const segmentGroup = new UrlSegmentGroup(currentRoute.url, childOutlets);\n    if (currentRoute === route) {\n      targetGroup = segmentGroup;\n    }\n    return segmentGroup;\n  }\n  const rootCandidate = createSegmentGroupFromRouteRecursive(route.root);\n  const rootSegmentGroup = createRoot(rootCandidate);\n  return targetGroup ?? rootSegmentGroup;\n}\nfunction createUrlTreeFromSegmentGroup(relativeTo, commands, queryParams, fragment) {\n  let root = relativeTo;\n  while (root.parent) {\n    root = root.parent;\n  }\n  // There are no commands so the `UrlTree` goes to the same path as the one created from the\n  // `UrlSegmentGroup`. All we need to do is update the `queryParams` and `fragment` without\n  // applying any other logic.\n  if (commands.length === 0) {\n    return tree(root, root, root, queryParams, fragment);\n  }\n  const nav = computeNavigation(commands);\n  if (nav.toRoot()) {\n    return tree(root, root, new UrlSegmentGroup([], {}), queryParams, fragment);\n  }\n  const position = findStartingPositionForTargetGroup(nav, root, relativeTo);\n  const newSegmentGroup = position.processChildren ? updateSegmentGroupChildren(position.segmentGroup, position.index, nav.commands) : updateSegmentGroup(position.segmentGroup, position.index, nav.commands);\n  return tree(root, position.segmentGroup, newSegmentGroup, queryParams, fragment);\n}\nfunction isMatrixParams(command) {\n  return typeof command === 'object' && command != null && !command.outlets && !command.segmentPath;\n}\n/**\n * Determines if a given command has an `outlets` map. When we encounter a command\n * with an outlets k/v map, we need to apply each outlet individually to the existing segment.\n */\nfunction isCommandWithOutlets(command) {\n  return typeof command === 'object' && command != null && command.outlets;\n}\nfunction tree(oldRoot, oldSegmentGroup, newSegmentGroup, queryParams, fragment) {\n  let qp = {};\n  if (queryParams) {\n    Object.entries(queryParams).forEach(([name, value]) => {\n      qp[name] = Array.isArray(value) ? value.map(v => `${v}`) : `${value}`;\n    });\n  }\n  let rootCandidate;\n  if (oldRoot === oldSegmentGroup) {\n    rootCandidate = newSegmentGroup;\n  } else {\n    rootCandidate = replaceSegment(oldRoot, oldSegmentGroup, newSegmentGroup);\n  }\n  const newRoot = createRoot(squashSegmentGroup(rootCandidate));\n  return new UrlTree(newRoot, qp, fragment);\n}\n/**\n * Replaces the `oldSegment` which is located in some child of the `current` with the `newSegment`.\n * This also has the effect of creating new `UrlSegmentGroup` copies to update references. This\n * shouldn't be necessary but the fallback logic for an invalid ActivatedRoute in the creation uses\n * the Router's current url tree. If we don't create new segment groups, we end up modifying that\n * value.\n */\nfunction replaceSegment(current, oldSegment, newSegment) {\n  const children = {};\n  Object.entries(current.children).forEach(([outletName, c]) => {\n    if (c === oldSegment) {\n      children[outletName] = newSegment;\n    } else {\n      children[outletName] = replaceSegment(c, oldSegment, newSegment);\n    }\n  });\n  return new UrlSegmentGroup(current.segments, children);\n}\nclass Navigation {\n  isAbsolute;\n  numberOfDoubleDots;\n  commands;\n  constructor(isAbsolute, numberOfDoubleDots, commands) {\n    this.isAbsolute = isAbsolute;\n    this.numberOfDoubleDots = numberOfDoubleDots;\n    this.commands = commands;\n    if (isAbsolute && commands.length > 0 && isMatrixParams(commands[0])) {\n      throw new _RuntimeError(4003 /* RuntimeErrorCode.ROOT_SEGMENT_MATRIX_PARAMS */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'Root segment cannot have matrix parameters');\n    }\n    const cmdWithOutlet = commands.find(isCommandWithOutlets);\n    if (cmdWithOutlet && cmdWithOutlet !== last(commands)) {\n      throw new _RuntimeError(4004 /* RuntimeErrorCode.MISPLACED_OUTLETS_COMMAND */, (typeof ngDevMode === 'undefined' || ngDevMode) && '{outlets:{}} has to be the last command');\n    }\n  }\n  toRoot() {\n    return this.isAbsolute && this.commands.length === 1 && this.commands[0] == '/';\n  }\n}\n/** Transforms commands to a normalized `Navigation` */\nfunction computeNavigation(commands) {\n  if (typeof commands[0] === 'string' && commands.length === 1 && commands[0] === '/') {\n    return new Navigation(true, 0, commands);\n  }\n  let numberOfDoubleDots = 0;\n  let isAbsolute = false;\n  const res = commands.reduce((res, cmd, cmdIdx) => {\n    if (typeof cmd === 'object' && cmd != null) {\n      if (cmd.outlets) {\n        const outlets = {};\n        Object.entries(cmd.outlets).forEach(([name, commands]) => {\n          outlets[name] = typeof commands === 'string' ? commands.split('/') : commands;\n        });\n        return [...res, {\n          outlets\n        }];\n      }\n      if (cmd.segmentPath) {\n        return [...res, cmd.segmentPath];\n      }\n    }\n    if (!(typeof cmd === 'string')) {\n      return [...res, cmd];\n    }\n    if (cmdIdx === 0) {\n      cmd.split('/').forEach((urlPart, partIndex) => {\n        if (partIndex == 0 && urlPart === '.') ;else if (partIndex == 0 && urlPart === '') {\n          //  '/a'\n          isAbsolute = true;\n        } else if (urlPart === '..') {\n          //  '../a'\n          numberOfDoubleDots++;\n        } else if (urlPart != '') {\n          res.push(urlPart);\n        }\n      });\n      return res;\n    }\n    return [...res, cmd];\n  }, []);\n  return new Navigation(isAbsolute, numberOfDoubleDots, res);\n}\nclass Position {\n  segmentGroup;\n  processChildren;\n  index;\n  constructor(segmentGroup, processChildren, index) {\n    this.segmentGroup = segmentGroup;\n    this.processChildren = processChildren;\n    this.index = index;\n  }\n}\nfunction findStartingPositionForTargetGroup(nav, root, target) {\n  if (nav.isAbsolute) {\n    return new Position(root, true, 0);\n  }\n  if (!target) {\n    // `NaN` is used only to maintain backwards compatibility with incorrectly mocked\n    // `ActivatedRouteSnapshot` in tests. In prior versions of this code, the position here was\n    // determined based on an internal property that was rarely mocked, resulting in `NaN`. In\n    // reality, this code path should _never_ be touched since `target` is not allowed to be falsey.\n    return new Position(root, false, NaN);\n  }\n  if (target.parent === null) {\n    return new Position(target, true, 0);\n  }\n  const modifier = isMatrixParams(nav.commands[0]) ? 0 : 1;\n  const index = target.segments.length - 1 + modifier;\n  return createPositionApplyingDoubleDots(target, index, nav.numberOfDoubleDots);\n}\nfunction createPositionApplyingDoubleDots(group, index, numberOfDoubleDots) {\n  let g = group;\n  let ci = index;\n  let dd = numberOfDoubleDots;\n  while (dd > ci) {\n    dd -= ci;\n    g = g.parent;\n    if (!g) {\n      throw new _RuntimeError(4005 /* RuntimeErrorCode.INVALID_DOUBLE_DOTS */, (typeof ngDevMode === 'undefined' || ngDevMode) && \"Invalid number of '../'\");\n    }\n    ci = g.segments.length;\n  }\n  return new Position(g, false, ci - dd);\n}\nfunction getOutlets(commands) {\n  if (isCommandWithOutlets(commands[0])) {\n    return commands[0].outlets;\n  }\n  return {\n    [PRIMARY_OUTLET]: commands\n  };\n}\nfunction updateSegmentGroup(segmentGroup, startIndex, commands) {\n  segmentGroup ??= new UrlSegmentGroup([], {});\n  if (segmentGroup.segments.length === 0 && segmentGroup.hasChildren()) {\n    return updateSegmentGroupChildren(segmentGroup, startIndex, commands);\n  }\n  const m = prefixedWith(segmentGroup, startIndex, commands);\n  const slicedCommands = commands.slice(m.commandIndex);\n  if (m.match && m.pathIndex < segmentGroup.segments.length) {\n    const g = new UrlSegmentGroup(segmentGroup.segments.slice(0, m.pathIndex), {});\n    g.children[PRIMARY_OUTLET] = new UrlSegmentGroup(segmentGroup.segments.slice(m.pathIndex), segmentGroup.children);\n    return updateSegmentGroupChildren(g, 0, slicedCommands);\n  } else if (m.match && slicedCommands.length === 0) {\n    return new UrlSegmentGroup(segmentGroup.segments, {});\n  } else if (m.match && !segmentGroup.hasChildren()) {\n    return createNewSegmentGroup(segmentGroup, startIndex, commands);\n  } else if (m.match) {\n    return updateSegmentGroupChildren(segmentGroup, 0, slicedCommands);\n  } else {\n    return createNewSegmentGroup(segmentGroup, startIndex, commands);\n  }\n}\nfunction updateSegmentGroupChildren(segmentGroup, startIndex, commands) {\n  if (commands.length === 0) {\n    return new UrlSegmentGroup(segmentGroup.segments, {});\n  } else {\n    const outlets = getOutlets(commands);\n    const children = {};\n    // If the set of commands applies to anything other than the primary outlet and the child\n    // segment is an empty path primary segment on its own, we want to apply the commands to the\n    // empty child path rather than here. The outcome is that the empty primary child is effectively\n    // removed from the final output UrlTree. Imagine the following config:\n    //\n    // {path: '', children: [{path: '**', outlet: 'popup'}]}.\n    //\n    // Navigation to /(popup:a) will activate the child outlet correctly Given a follow-up\n    // navigation with commands\n    // ['/', {outlets: {'popup': 'b'}}], we _would not_ want to apply the outlet commands to the\n    // root segment because that would result in\n    // //(popup:a)(popup:b) since the outlet command got applied one level above where it appears in\n    // the `ActivatedRoute` rather than updating the existing one.\n    //\n    // Because empty paths do not appear in the URL segments and the fact that the segments used in\n    // the output `UrlTree` are squashed to eliminate these empty paths where possible\n    // https://github.com/angular/angular/blob/13f10de40e25c6900ca55bd83b36bd533dacfa9e/packages/router/src/url_tree.ts#L755\n    // it can be hard to determine what is the right thing to do when applying commands to a\n    // `UrlSegmentGroup` that is created from an \"unsquashed\"/expanded `ActivatedRoute` tree.\n    // This code effectively \"squashes\" empty path primary routes when they have no siblings on\n    // the same level of the tree.\n    if (Object.keys(outlets).some(o => o !== PRIMARY_OUTLET) && segmentGroup.children[PRIMARY_OUTLET] && segmentGroup.numberOfChildren === 1 && segmentGroup.children[PRIMARY_OUTLET].segments.length === 0) {\n      const childrenOfEmptyChild = updateSegmentGroupChildren(segmentGroup.children[PRIMARY_OUTLET], startIndex, commands);\n      return new UrlSegmentGroup(segmentGroup.segments, childrenOfEmptyChild.children);\n    }\n    Object.entries(outlets).forEach(([outlet, commands]) => {\n      if (typeof commands === 'string') {\n        commands = [commands];\n      }\n      if (commands !== null) {\n        children[outlet] = updateSegmentGroup(segmentGroup.children[outlet], startIndex, commands);\n      }\n    });\n    Object.entries(segmentGroup.children).forEach(([childOutlet, child]) => {\n      if (outlets[childOutlet] === undefined) {\n        children[childOutlet] = child;\n      }\n    });\n    return new UrlSegmentGroup(segmentGroup.segments, children);\n  }\n}\nfunction prefixedWith(segmentGroup, startIndex, commands) {\n  let currentCommandIndex = 0;\n  let currentPathIndex = startIndex;\n  const noMatch = {\n    match: false,\n    pathIndex: 0,\n    commandIndex: 0\n  };\n  while (currentPathIndex < segmentGroup.segments.length) {\n    if (currentCommandIndex >= commands.length) return noMatch;\n    const path = segmentGroup.segments[currentPathIndex];\n    const command = commands[currentCommandIndex];\n    // Do not try to consume command as part of the prefixing if it has outlets because it can\n    // contain outlets other than the one being processed. Consuming the outlets command would\n    // result in other outlets being ignored.\n    if (isCommandWithOutlets(command)) {\n      break;\n    }\n    const curr = `${command}`;\n    const next = currentCommandIndex < commands.length - 1 ? commands[currentCommandIndex + 1] : null;\n    if (currentPathIndex > 0 && curr === undefined) break;\n    if (curr && next && typeof next === 'object' && next.outlets === undefined) {\n      if (!compare(curr, next, path)) return noMatch;\n      currentCommandIndex += 2;\n    } else {\n      if (!compare(curr, {}, path)) return noMatch;\n      currentCommandIndex++;\n    }\n    currentPathIndex++;\n  }\n  return {\n    match: true,\n    pathIndex: currentPathIndex,\n    commandIndex: currentCommandIndex\n  };\n}\nfunction createNewSegmentGroup(segmentGroup, startIndex, commands) {\n  const paths = segmentGroup.segments.slice(0, startIndex);\n  let i = 0;\n  while (i < commands.length) {\n    const command = commands[i];\n    if (isCommandWithOutlets(command)) {\n      const children = createNewSegmentChildren(command.outlets);\n      return new UrlSegmentGroup(paths, children);\n    }\n    // if we start with an object literal, we need to reuse the path part from the segment\n    if (i === 0 && isMatrixParams(commands[0])) {\n      const p = segmentGroup.segments[startIndex];\n      paths.push(new UrlSegment(p.path, stringify(commands[0])));\n      i++;\n      continue;\n    }\n    const curr = isCommandWithOutlets(command) ? command.outlets[PRIMARY_OUTLET] : `${command}`;\n    const next = i < commands.length - 1 ? commands[i + 1] : null;\n    if (curr && next && isMatrixParams(next)) {\n      paths.push(new UrlSegment(curr, stringify(next)));\n      i += 2;\n    } else {\n      paths.push(new UrlSegment(curr, {}));\n      i++;\n    }\n  }\n  return new UrlSegmentGroup(paths, {});\n}\nfunction createNewSegmentChildren(outlets) {\n  const children = {};\n  Object.entries(outlets).forEach(([outlet, commands]) => {\n    if (typeof commands === 'string') {\n      commands = [commands];\n    }\n    if (commands !== null) {\n      children[outlet] = createNewSegmentGroup(new UrlSegmentGroup([], {}), 0, commands);\n    }\n  });\n  return children;\n}\nfunction stringify(params) {\n  const res = {};\n  Object.entries(params).forEach(([k, v]) => res[k] = `${v}`);\n  return res;\n}\nfunction compare(path, params, segment) {\n  return path == segment.path && shallowEqual(params, segment.parameters);\n}\nconst IMPERATIVE_NAVIGATION = 'imperative';\n/**\n * Identifies the type of a router event.\n *\n * @publicApi\n */\nvar EventType;\n(function (EventType) {\n  EventType[EventType[\"NavigationStart\"] = 0] = \"NavigationStart\";\n  EventType[EventType[\"NavigationEnd\"] = 1] = \"NavigationEnd\";\n  EventType[EventType[\"NavigationCancel\"] = 2] = \"NavigationCancel\";\n  EventType[EventType[\"NavigationError\"] = 3] = \"NavigationError\";\n  EventType[EventType[\"RoutesRecognized\"] = 4] = \"RoutesRecognized\";\n  EventType[EventType[\"ResolveStart\"] = 5] = \"ResolveStart\";\n  EventType[EventType[\"ResolveEnd\"] = 6] = \"ResolveEnd\";\n  EventType[EventType[\"GuardsCheckStart\"] = 7] = \"GuardsCheckStart\";\n  EventType[EventType[\"GuardsCheckEnd\"] = 8] = \"GuardsCheckEnd\";\n  EventType[EventType[\"RouteConfigLoadStart\"] = 9] = \"RouteConfigLoadStart\";\n  EventType[EventType[\"RouteConfigLoadEnd\"] = 10] = \"RouteConfigLoadEnd\";\n  EventType[EventType[\"ChildActivationStart\"] = 11] = \"ChildActivationStart\";\n  EventType[EventType[\"ChildActivationEnd\"] = 12] = \"ChildActivationEnd\";\n  EventType[EventType[\"ActivationStart\"] = 13] = \"ActivationStart\";\n  EventType[EventType[\"ActivationEnd\"] = 14] = \"ActivationEnd\";\n  EventType[EventType[\"Scroll\"] = 15] = \"Scroll\";\n  EventType[EventType[\"NavigationSkipped\"] = 16] = \"NavigationSkipped\";\n})(EventType || (EventType = {}));\n/**\n * Base for events the router goes through, as opposed to events tied to a specific\n * route. Fired one time for any given navigation.\n *\n * The following code shows how a class subscribes to router events.\n *\n * ```ts\n * import {Event, RouterEvent, Router} from '@angular/router';\n *\n * class MyService {\n *   constructor(public router: Router) {\n *     router.events.pipe(\n *        filter((e: Event | RouterEvent): e is RouterEvent => e instanceof RouterEvent)\n *     ).subscribe((e: RouterEvent) => {\n *       // Do something\n *     });\n *   }\n * }\n * ```\n *\n * @see {@link Event}\n * @see [Router events summary](guide/routing/router-reference#router-events)\n * @publicApi\n */\nclass RouterEvent {\n  id;\n  url;\n  constructor(/** A unique ID that the router assigns to every router navigation. */\n  id, /** The URL that is the destination for this navigation. */\n  url) {\n    this.id = id;\n    this.url = url;\n  }\n}\n/**\n * An event triggered when a navigation starts.\n *\n * @publicApi\n */\nclass NavigationStart extends RouterEvent {\n  type = EventType.NavigationStart;\n  /**\n   * Identifies the call or event that triggered the navigation.\n   * An `imperative` trigger is a call to `router.navigateByUrl()` or `router.navigate()`.\n   *\n   * @see {@link NavigationEnd}\n   * @see {@link NavigationCancel}\n   * @see {@link NavigationError}\n   */\n  navigationTrigger;\n  /**\n   * The navigation state that was previously supplied to the `pushState` call,\n   * when the navigation is triggered by a `popstate` event. Otherwise null.\n   *\n   * The state object is defined by `NavigationExtras`, and contains any\n   * developer-defined state value, as well as a unique ID that\n   * the router assigns to every router transition/navigation.\n   *\n   * From the perspective of the router, the router never \"goes back\".\n   * When the user clicks on the back button in the browser,\n   * a new navigation ID is created.\n   *\n   * Use the ID in this previous-state object to differentiate between a newly created\n   * state and one returned to by a `popstate` event, so that you can restore some\n   * remembered state, such as scroll position.\n   *\n   */\n  restoredState;\n  constructor(/** @docsNotRequired */\n  id, /** @docsNotRequired */\n  url, /** @docsNotRequired */\n  navigationTrigger = 'imperative', /** @docsNotRequired */\n  restoredState = null) {\n    super(id, url);\n    this.navigationTrigger = navigationTrigger;\n    this.restoredState = restoredState;\n  }\n  /** @docsNotRequired */\n  toString() {\n    return `NavigationStart(id: ${this.id}, url: '${this.url}')`;\n  }\n}\n/**\n * An event triggered when a navigation ends successfully.\n *\n * @see {@link NavigationStart}\n * @see {@link NavigationCancel}\n * @see {@link NavigationError}\n *\n * @publicApi\n */\nclass NavigationEnd extends RouterEvent {\n  urlAfterRedirects;\n  type = EventType.NavigationEnd;\n  constructor(/** @docsNotRequired */\n  id, /** @docsNotRequired */\n  url, /** @docsNotRequired */\n  urlAfterRedirects) {\n    super(id, url);\n    this.urlAfterRedirects = urlAfterRedirects;\n  }\n  /** @docsNotRequired */\n  toString() {\n    return `NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`;\n  }\n}\n/**\n * A code for the `NavigationCancel` event of the `Router` to indicate the\n * reason a navigation failed.\n *\n * @publicApi\n */\nvar NavigationCancellationCode;\n(function (NavigationCancellationCode) {\n  /**\n   * A navigation failed because a guard returned a `UrlTree` to redirect.\n   */\n  NavigationCancellationCode[NavigationCancellationCode[\"Redirect\"] = 0] = \"Redirect\";\n  /**\n   * A navigation failed because a more recent navigation started.\n   */\n  NavigationCancellationCode[NavigationCancellationCode[\"SupersededByNewNavigation\"] = 1] = \"SupersededByNewNavigation\";\n  /**\n   * A navigation failed because one of the resolvers completed without emitting a value.\n   */\n  NavigationCancellationCode[NavigationCancellationCode[\"NoDataFromResolver\"] = 2] = \"NoDataFromResolver\";\n  /**\n   * A navigation failed because a guard returned `false`.\n   */\n  NavigationCancellationCode[NavigationCancellationCode[\"GuardRejected\"] = 3] = \"GuardRejected\";\n})(NavigationCancellationCode || (NavigationCancellationCode = {}));\n/**\n * A code for the `NavigationSkipped` event of the `Router` to indicate the\n * reason a navigation was skipped.\n *\n * @publicApi\n */\nvar NavigationSkippedCode;\n(function (NavigationSkippedCode) {\n  /**\n   * A navigation was skipped because the navigation URL was the same as the current Router URL.\n   */\n  NavigationSkippedCode[NavigationSkippedCode[\"IgnoredSameUrlNavigation\"] = 0] = \"IgnoredSameUrlNavigation\";\n  /**\n   * A navigation was skipped because the configured `UrlHandlingStrategy` return `false` for both\n   * the current Router URL and the target of the navigation.\n   *\n   * @see {@link UrlHandlingStrategy}\n   */\n  NavigationSkippedCode[NavigationSkippedCode[\"IgnoredByUrlHandlingStrategy\"] = 1] = \"IgnoredByUrlHandlingStrategy\";\n})(NavigationSkippedCode || (NavigationSkippedCode = {}));\n/**\n * An event triggered when a navigation is canceled, directly or indirectly.\n * This can happen for several reasons including when a route guard\n * returns `false` or initiates a redirect by returning a `UrlTree`.\n *\n * @see {@link NavigationStart}\n * @see {@link NavigationEnd}\n * @see {@link NavigationError}\n *\n * @publicApi\n */\nclass NavigationCancel extends RouterEvent {\n  reason;\n  code;\n  type = EventType.NavigationCancel;\n  constructor(/** @docsNotRequired */\n  id, /** @docsNotRequired */\n  url,\n  /**\n   * A description of why the navigation was cancelled. For debug purposes only. Use `code`\n   * instead for a stable cancellation reason that can be used in production.\n   */\n  reason,\n  /**\n   * A code to indicate why the navigation was canceled. This cancellation code is stable for\n   * the reason and can be relied on whereas the `reason` string could change and should not be\n   * used in production.\n   */\n  code) {\n    super(id, url);\n    this.reason = reason;\n    this.code = code;\n  }\n  /** @docsNotRequired */\n  toString() {\n    return `NavigationCancel(id: ${this.id}, url: '${this.url}')`;\n  }\n}\n/**\n * An event triggered when a navigation is skipped.\n * This can happen for a couple reasons including onSameUrlHandling\n * is set to `ignore` and the navigation URL is not different than the\n * current state.\n *\n * @publicApi\n */\nclass NavigationSkipped extends RouterEvent {\n  reason;\n  code;\n  type = EventType.NavigationSkipped;\n  constructor(/** @docsNotRequired */\n  id, /** @docsNotRequired */\n  url,\n  /**\n   * A description of why the navigation was skipped. For debug purposes only. Use `code`\n   * instead for a stable skipped reason that can be used in production.\n   */\n  reason,\n  /**\n   * A code to indicate why the navigation was skipped. This code is stable for\n   * the reason and can be relied on whereas the `reason` string could change and should not be\n   * used in production.\n   */\n  code) {\n    super(id, url);\n    this.reason = reason;\n    this.code = code;\n  }\n}\n/**\n * An event triggered when a navigation fails due to an unexpected error.\n *\n * @see {@link NavigationStart}\n * @see {@link NavigationEnd}\n * @see {@link NavigationCancel}\n *\n * @publicApi\n */\nclass NavigationError extends RouterEvent {\n  error;\n  target;\n  type = EventType.NavigationError;\n  constructor(/** @docsNotRequired */\n  id, /** @docsNotRequired */\n  url, /** @docsNotRequired */\n  error,\n  /**\n   * The target of the navigation when the error occurred.\n   *\n   * Note that this can be `undefined` because an error could have occurred before the\n   * `RouterStateSnapshot` was created for the navigation.\n   */\n  target) {\n    super(id, url);\n    this.error = error;\n    this.target = target;\n  }\n  /** @docsNotRequired */\n  toString() {\n    return `NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`;\n  }\n}\n/**\n * An event triggered when routes are recognized.\n *\n * @publicApi\n */\nclass RoutesRecognized extends RouterEvent {\n  urlAfterRedirects;\n  state;\n  type = EventType.RoutesRecognized;\n  constructor(/** @docsNotRequired */\n  id, /** @docsNotRequired */\n  url, /** @docsNotRequired */\n  urlAfterRedirects, /** @docsNotRequired */\n  state) {\n    super(id, url);\n    this.urlAfterRedirects = urlAfterRedirects;\n    this.state = state;\n  }\n  /** @docsNotRequired */\n  toString() {\n    return `RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`;\n  }\n}\n/**\n * An event triggered at the start of the Guard phase of routing.\n *\n * @see {@link GuardsCheckEnd}\n *\n * @publicApi\n */\nclass GuardsCheckStart extends RouterEvent {\n  urlAfterRedirects;\n  state;\n  type = EventType.GuardsCheckStart;\n  constructor(/** @docsNotRequired */\n  id, /** @docsNotRequired */\n  url, /** @docsNotRequired */\n  urlAfterRedirects, /** @docsNotRequired */\n  state) {\n    super(id, url);\n    this.urlAfterRedirects = urlAfterRedirects;\n    this.state = state;\n  }\n  toString() {\n    return `GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`;\n  }\n}\n/**\n * An event triggered at the end of the Guard phase of routing.\n *\n * @see {@link GuardsCheckStart}\n *\n * @publicApi\n */\nclass GuardsCheckEnd extends RouterEvent {\n  urlAfterRedirects;\n  state;\n  shouldActivate;\n  type = EventType.GuardsCheckEnd;\n  constructor(/** @docsNotRequired */\n  id, /** @docsNotRequired */\n  url, /** @docsNotRequired */\n  urlAfterRedirects, /** @docsNotRequired */\n  state, /** @docsNotRequired */\n  shouldActivate) {\n    super(id, url);\n    this.urlAfterRedirects = urlAfterRedirects;\n    this.state = state;\n    this.shouldActivate = shouldActivate;\n  }\n  toString() {\n    return `GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`;\n  }\n}\n/**\n * An event triggered at the start of the Resolve phase of routing.\n *\n * Runs in the \"resolve\" phase whether or not there is anything to resolve.\n * In future, may change to only run when there are things to be resolved.\n *\n * @see {@link ResolveEnd}\n *\n * @publicApi\n */\nclass ResolveStart extends RouterEvent {\n  urlAfterRedirects;\n  state;\n  type = EventType.ResolveStart;\n  constructor(/** @docsNotRequired */\n  id, /** @docsNotRequired */\n  url, /** @docsNotRequired */\n  urlAfterRedirects, /** @docsNotRequired */\n  state) {\n    super(id, url);\n    this.urlAfterRedirects = urlAfterRedirects;\n    this.state = state;\n  }\n  toString() {\n    return `ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`;\n  }\n}\n/**\n * An event triggered at the end of the Resolve phase of routing.\n * @see {@link ResolveStart}\n *\n * @publicApi\n */\nclass ResolveEnd extends RouterEvent {\n  urlAfterRedirects;\n  state;\n  type = EventType.ResolveEnd;\n  constructor(/** @docsNotRequired */\n  id, /** @docsNotRequired */\n  url, /** @docsNotRequired */\n  urlAfterRedirects, /** @docsNotRequired */\n  state) {\n    super(id, url);\n    this.urlAfterRedirects = urlAfterRedirects;\n    this.state = state;\n  }\n  toString() {\n    return `ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`;\n  }\n}\n/**\n * An event triggered before lazy loading a route configuration.\n *\n * @see {@link RouteConfigLoadEnd}\n *\n * @publicApi\n */\nclass RouteConfigLoadStart {\n  route;\n  type = EventType.RouteConfigLoadStart;\n  constructor(/** @docsNotRequired */\n  route) {\n    this.route = route;\n  }\n  toString() {\n    return `RouteConfigLoadStart(path: ${this.route.path})`;\n  }\n}\n/**\n * An event triggered when a route has been lazy loaded.\n *\n * @see {@link RouteConfigLoadStart}\n *\n * @publicApi\n */\nclass RouteConfigLoadEnd {\n  route;\n  type = EventType.RouteConfigLoadEnd;\n  constructor(/** @docsNotRequired */\n  route) {\n    this.route = route;\n  }\n  toString() {\n    return `RouteConfigLoadEnd(path: ${this.route.path})`;\n  }\n}\n/**\n * An event triggered at the start of the child-activation\n * part of the Resolve phase of routing.\n * @see {@link ChildActivationEnd}\n * @see {@link ResolveStart}\n *\n * @publicApi\n */\nclass ChildActivationStart {\n  snapshot;\n  type = EventType.ChildActivationStart;\n  constructor(/** @docsNotRequired */\n  snapshot) {\n    this.snapshot = snapshot;\n  }\n  toString() {\n    const path = this.snapshot.routeConfig && this.snapshot.routeConfig.path || '';\n    return `ChildActivationStart(path: '${path}')`;\n  }\n}\n/**\n * An event triggered at the end of the child-activation part\n * of the Resolve phase of routing.\n * @see {@link ChildActivationStart}\n * @see {@link ResolveStart}\n * @publicApi\n */\nclass ChildActivationEnd {\n  snapshot;\n  type = EventType.ChildActivationEnd;\n  constructor(/** @docsNotRequired */\n  snapshot) {\n    this.snapshot = snapshot;\n  }\n  toString() {\n    const path = this.snapshot.routeConfig && this.snapshot.routeConfig.path || '';\n    return `ChildActivationEnd(path: '${path}')`;\n  }\n}\n/**\n * An event triggered at the start of the activation part\n * of the Resolve phase of routing.\n * @see {@link ActivationEnd}\n * @see {@link ResolveStart}\n *\n * @publicApi\n */\nclass ActivationStart {\n  snapshot;\n  type = EventType.ActivationStart;\n  constructor(/** @docsNotRequired */\n  snapshot) {\n    this.snapshot = snapshot;\n  }\n  toString() {\n    const path = this.snapshot.routeConfig && this.snapshot.routeConfig.path || '';\n    return `ActivationStart(path: '${path}')`;\n  }\n}\n/**\n * An event triggered at the end of the activation part\n * of the Resolve phase of routing.\n * @see {@link ActivationStart}\n * @see {@link ResolveStart}\n *\n * @publicApi\n */\nclass ActivationEnd {\n  snapshot;\n  type = EventType.ActivationEnd;\n  constructor(/** @docsNotRequired */\n  snapshot) {\n    this.snapshot = snapshot;\n  }\n  toString() {\n    const path = this.snapshot.routeConfig && this.snapshot.routeConfig.path || '';\n    return `ActivationEnd(path: '${path}')`;\n  }\n}\n/**\n * An event triggered by scrolling.\n *\n * @publicApi\n */\nclass Scroll {\n  routerEvent;\n  position;\n  anchor;\n  type = EventType.Scroll;\n  constructor(/** @docsNotRequired */\n  routerEvent, /** @docsNotRequired */\n  position, /** @docsNotRequired */\n  anchor) {\n    this.routerEvent = routerEvent;\n    this.position = position;\n    this.anchor = anchor;\n  }\n  toString() {\n    const pos = this.position ? `${this.position[0]}, ${this.position[1]}` : null;\n    return `Scroll(anchor: '${this.anchor}', position: '${pos}')`;\n  }\n}\nclass BeforeActivateRoutes {}\nclass RedirectRequest {\n  url;\n  navigationBehaviorOptions;\n  constructor(url, navigationBehaviorOptions) {\n    this.url = url;\n    this.navigationBehaviorOptions = navigationBehaviorOptions;\n  }\n}\nfunction stringifyEvent(routerEvent) {\n  switch (routerEvent.type) {\n    case EventType.ActivationEnd:\n      return `ActivationEnd(path: '${routerEvent.snapshot.routeConfig?.path || ''}')`;\n    case EventType.ActivationStart:\n      return `ActivationStart(path: '${routerEvent.snapshot.routeConfig?.path || ''}')`;\n    case EventType.ChildActivationEnd:\n      return `ChildActivationEnd(path: '${routerEvent.snapshot.routeConfig?.path || ''}')`;\n    case EventType.ChildActivationStart:\n      return `ChildActivationStart(path: '${routerEvent.snapshot.routeConfig?.path || ''}')`;\n    case EventType.GuardsCheckEnd:\n      return `GuardsCheckEnd(id: ${routerEvent.id}, url: '${routerEvent.url}', urlAfterRedirects: '${routerEvent.urlAfterRedirects}', state: ${routerEvent.state}, shouldActivate: ${routerEvent.shouldActivate})`;\n    case EventType.GuardsCheckStart:\n      return `GuardsCheckStart(id: ${routerEvent.id}, url: '${routerEvent.url}', urlAfterRedirects: '${routerEvent.urlAfterRedirects}', state: ${routerEvent.state})`;\n    case EventType.NavigationCancel:\n      return `NavigationCancel(id: ${routerEvent.id}, url: '${routerEvent.url}')`;\n    case EventType.NavigationSkipped:\n      return `NavigationSkipped(id: ${routerEvent.id}, url: '${routerEvent.url}')`;\n    case EventType.NavigationEnd:\n      return `NavigationEnd(id: ${routerEvent.id}, url: '${routerEvent.url}', urlAfterRedirects: '${routerEvent.urlAfterRedirects}')`;\n    case EventType.NavigationError:\n      return `NavigationError(id: ${routerEvent.id}, url: '${routerEvent.url}', error: ${routerEvent.error})`;\n    case EventType.NavigationStart:\n      return `NavigationStart(id: ${routerEvent.id}, url: '${routerEvent.url}')`;\n    case EventType.ResolveEnd:\n      return `ResolveEnd(id: ${routerEvent.id}, url: '${routerEvent.url}', urlAfterRedirects: '${routerEvent.urlAfterRedirects}', state: ${routerEvent.state})`;\n    case EventType.ResolveStart:\n      return `ResolveStart(id: ${routerEvent.id}, url: '${routerEvent.url}', urlAfterRedirects: '${routerEvent.urlAfterRedirects}', state: ${routerEvent.state})`;\n    case EventType.RouteConfigLoadEnd:\n      return `RouteConfigLoadEnd(path: ${routerEvent.route.path})`;\n    case EventType.RouteConfigLoadStart:\n      return `RouteConfigLoadStart(path: ${routerEvent.route.path})`;\n    case EventType.RoutesRecognized:\n      return `RoutesRecognized(id: ${routerEvent.id}, url: '${routerEvent.url}', urlAfterRedirects: '${routerEvent.urlAfterRedirects}', state: ${routerEvent.state})`;\n    case EventType.Scroll:\n      const pos = routerEvent.position ? `${routerEvent.position[0]}, ${routerEvent.position[1]}` : null;\n      return `Scroll(anchor: '${routerEvent.anchor}', position: '${pos}')`;\n  }\n}\n\n/**\n * Creates an `EnvironmentInjector` if the `Route` has providers and one does not already exist\n * and returns the injector. Otherwise, if the `Route` does not have `providers`, returns the\n * `currentInjector`.\n *\n * @param route The route that might have providers\n * @param currentInjector The parent injector of the `Route`\n */\nfunction getOrCreateRouteInjectorIfNeeded(route, currentInjector) {\n  if (route.providers && !route._injector) {\n    route._injector = createEnvironmentInjector(route.providers, currentInjector, `Route: ${route.path}`);\n  }\n  return route._injector ?? currentInjector;\n}\nfunction validateConfig(config, parentPath = '', requireStandaloneComponents = false) {\n  // forEach doesn't iterate undefined values\n  for (let i = 0; i < config.length; i++) {\n    const route = config[i];\n    const fullPath = getFullPath(parentPath, route);\n    validateNode(route, fullPath, requireStandaloneComponents);\n  }\n}\nfunction assertStandalone(fullPath, component) {\n  if (component && _isNgModule(component)) {\n    throw new _RuntimeError(4014 /* RuntimeErrorCode.INVALID_ROUTE_CONFIG */, `Invalid configuration of route '${fullPath}'. You are using 'loadComponent' with a module, ` + `but it must be used with standalone components. Use 'loadChildren' instead.`);\n  } else if (component && !isStandalone(component)) {\n    throw new _RuntimeError(4014 /* RuntimeErrorCode.INVALID_ROUTE_CONFIG */, `Invalid configuration of route '${fullPath}'. The component must be standalone.`);\n  }\n}\nfunction validateNode(route, fullPath, requireStandaloneComponents) {\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    if (!route) {\n      throw new _RuntimeError(4014 /* RuntimeErrorCode.INVALID_ROUTE_CONFIG */, `\n      Invalid configuration of route '${fullPath}': Encountered undefined route.\n      The reason might be an extra comma.\n\n      Example:\n      const routes: Routes = [\n        { path: '', redirectTo: '/dashboard', pathMatch: 'full' },\n        { path: 'dashboard',  component: DashboardComponent },, << two commas\n        { path: 'detail/:id', component: HeroDetailComponent }\n      ];\n    `);\n    }\n    if (Array.isArray(route)) {\n      throw new _RuntimeError(4014 /* RuntimeErrorCode.INVALID_ROUTE_CONFIG */, `Invalid configuration of route '${fullPath}': Array cannot be specified`);\n    }\n    if (!route.redirectTo && !route.component && !route.loadComponent && !route.children && !route.loadChildren && route.outlet && route.outlet !== PRIMARY_OUTLET) {\n      throw new _RuntimeError(4014 /* RuntimeErrorCode.INVALID_ROUTE_CONFIG */, `Invalid configuration of route '${fullPath}': a componentless route without children or loadChildren cannot have a named outlet set`);\n    }\n    if (route.redirectTo && route.children) {\n      throw new _RuntimeError(4014 /* RuntimeErrorCode.INVALID_ROUTE_CONFIG */, `Invalid configuration of route '${fullPath}': redirectTo and children cannot be used together`);\n    }\n    if (route.redirectTo && route.loadChildren) {\n      throw new _RuntimeError(4014 /* RuntimeErrorCode.INVALID_ROUTE_CONFIG */, `Invalid configuration of route '${fullPath}': redirectTo and loadChildren cannot be used together`);\n    }\n    if (route.children && route.loadChildren) {\n      throw new _RuntimeError(4014 /* RuntimeErrorCode.INVALID_ROUTE_CONFIG */, `Invalid configuration of route '${fullPath}': children and loadChildren cannot be used together`);\n    }\n    if (route.redirectTo && (route.component || route.loadComponent)) {\n      throw new _RuntimeError(4014 /* RuntimeErrorCode.INVALID_ROUTE_CONFIG */, `Invalid configuration of route '${fullPath}': redirectTo and component/loadComponent cannot be used together`);\n    }\n    if (route.component && route.loadComponent) {\n      throw new _RuntimeError(4014 /* RuntimeErrorCode.INVALID_ROUTE_CONFIG */, `Invalid configuration of route '${fullPath}': component and loadComponent cannot be used together`);\n    }\n    if (route.redirectTo && route.canActivate) {\n      throw new _RuntimeError(4014 /* RuntimeErrorCode.INVALID_ROUTE_CONFIG */, `Invalid configuration of route '${fullPath}': redirectTo and canActivate cannot be used together. Redirects happen before activation ` + `so canActivate will never be executed.`);\n    }\n    if (route.path && route.matcher) {\n      throw new _RuntimeError(4014 /* RuntimeErrorCode.INVALID_ROUTE_CONFIG */, `Invalid configuration of route '${fullPath}': path and matcher cannot be used together`);\n    }\n    if (route.redirectTo === void 0 && !route.component && !route.loadComponent && !route.children && !route.loadChildren) {\n      throw new _RuntimeError(4014 /* RuntimeErrorCode.INVALID_ROUTE_CONFIG */, `Invalid configuration of route '${fullPath}'. One of the following must be provided: component, loadComponent, redirectTo, children or loadChildren`);\n    }\n    if (route.path === void 0 && route.matcher === void 0) {\n      throw new _RuntimeError(4014 /* RuntimeErrorCode.INVALID_ROUTE_CONFIG */, `Invalid configuration of route '${fullPath}': routes must have either a path or a matcher specified`);\n    }\n    if (typeof route.path === 'string' && route.path.charAt(0) === '/') {\n      throw new _RuntimeError(4014 /* RuntimeErrorCode.INVALID_ROUTE_CONFIG */, `Invalid configuration of route '${fullPath}': path cannot start with a slash`);\n    }\n    if (route.path === '' && route.redirectTo !== void 0 && route.pathMatch === void 0) {\n      const exp = `The default value of 'pathMatch' is 'prefix', but often the intent is to use 'full'.`;\n      throw new _RuntimeError(4014 /* RuntimeErrorCode.INVALID_ROUTE_CONFIG */, `Invalid configuration of route '{path: \"${fullPath}\", redirectTo: \"${route.redirectTo}\"}': please provide 'pathMatch'. ${exp}`);\n    }\n    if (requireStandaloneComponents) {\n      assertStandalone(fullPath, route.component);\n    }\n  }\n  if (route.children) {\n    validateConfig(route.children, fullPath, requireStandaloneComponents);\n  }\n}\nfunction getFullPath(parentPath, currentRoute) {\n  if (!currentRoute) {\n    return parentPath;\n  }\n  if (!parentPath && !currentRoute.path) {\n    return '';\n  } else if (parentPath && !currentRoute.path) {\n    return `${parentPath}/`;\n  } else if (!parentPath && currentRoute.path) {\n    return currentRoute.path;\n  } else {\n    return `${parentPath}/${currentRoute.path}`;\n  }\n}\n/** Returns the `route.outlet` or PRIMARY_OUTLET if none exists. */\nfunction getOutlet(route) {\n  return route.outlet || PRIMARY_OUTLET;\n}\n/**\n * Sorts the `routes` such that the ones with an outlet matching `outletName` come first.\n * The order of the configs is otherwise preserved.\n */\nfunction sortByMatchingOutlets(routes, outletName) {\n  const sortedConfig = routes.filter(r => getOutlet(r) === outletName);\n  sortedConfig.push(...routes.filter(r => getOutlet(r) !== outletName));\n  return sortedConfig;\n}\n/**\n * Gets the first injector in the snapshot's parent tree.\n *\n * If the `Route` has a static list of providers, the returned injector will be the one created from\n * those. If it does not exist, the returned injector may come from the parents, which may be from a\n * loaded config or their static providers.\n *\n * Returns `null` if there is neither this nor any parents have a stored injector.\n *\n * Generally used for retrieving the injector to use for getting tokens for guards/resolvers and\n * also used for getting the correct injector to use for creating components.\n */\nfunction getClosestRouteInjector(snapshot) {\n  if (!snapshot) return null;\n  // If the current route has its own injector, which is created from the static providers on the\n  // route itself, we should use that. Otherwise, we start at the parent since we do not want to\n  // include the lazy loaded injector from this route.\n  if (snapshot.routeConfig?._injector) {\n    return snapshot.routeConfig._injector;\n  }\n  for (let s = snapshot.parent; s; s = s.parent) {\n    const route = s.routeConfig;\n    // Note that the order here is important. `_loadedInjector` stored on the route with\n    // `loadChildren: () => NgModule` so it applies to child routes with priority. The `_injector`\n    // is created from the static providers on that parent route, so it applies to the children as\n    // well, but only if there is no lazy loaded NgModuleRef injector.\n    if (route?._loadedInjector) return route._loadedInjector;\n    if (route?._injector) return route._injector;\n  }\n  return null;\n}\n\n/**\n * Store contextual information about a `RouterOutlet`\n *\n * @publicApi\n */\nclass OutletContext {\n  rootInjector;\n  outlet = null;\n  route = null;\n  children;\n  attachRef = null;\n  get injector() {\n    return getClosestRouteInjector(this.route?.snapshot) ?? this.rootInjector;\n  }\n  constructor(rootInjector) {\n    this.rootInjector = rootInjector;\n    this.children = new ChildrenOutletContexts(this.rootInjector);\n  }\n}\n/**\n * Store contextual information about the children (= nested) `RouterOutlet`\n *\n * @publicApi\n */\nclass ChildrenOutletContexts {\n  rootInjector;\n  // contexts for child outlets, by name.\n  contexts = new Map();\n  /** @docs-private */\n  constructor(rootInjector) {\n    this.rootInjector = rootInjector;\n  }\n  /** Called when a `RouterOutlet` directive is instantiated */\n  onChildOutletCreated(childName, outlet) {\n    const context = this.getOrCreateContext(childName);\n    context.outlet = outlet;\n    this.contexts.set(childName, context);\n  }\n  /**\n   * Called when a `RouterOutlet` directive is destroyed.\n   * We need to keep the context as the outlet could be destroyed inside a NgIf and might be\n   * re-created later.\n   */\n  onChildOutletDestroyed(childName) {\n    const context = this.getContext(childName);\n    if (context) {\n      context.outlet = null;\n      context.attachRef = null;\n    }\n  }\n  /**\n   * Called when the corresponding route is deactivated during navigation.\n   * Because the component get destroyed, all children outlet are destroyed.\n   */\n  onOutletDeactivated() {\n    const contexts = this.contexts;\n    this.contexts = new Map();\n    return contexts;\n  }\n  onOutletReAttached(contexts) {\n    this.contexts = contexts;\n  }\n  getOrCreateContext(childName) {\n    let context = this.getContext(childName);\n    if (!context) {\n      context = new OutletContext(this.rootInjector);\n      this.contexts.set(childName, context);\n    }\n    return context;\n  }\n  getContext(childName) {\n    return this.contexts.get(childName) || null;\n  }\n  static ɵfac = function ChildrenOutletContexts_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ChildrenOutletContexts)(i0.ɵɵinject(i0.EnvironmentInjector));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ChildrenOutletContexts,\n    factory: ChildrenOutletContexts.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ChildrenOutletContexts, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.EnvironmentInjector\n  }], null);\n})();\nclass Tree {\n  /** @internal */\n  _root;\n  constructor(root) {\n    this._root = root;\n  }\n  get root() {\n    return this._root.value;\n  }\n  /**\n   * @internal\n   */\n  parent(t) {\n    const p = this.pathFromRoot(t);\n    return p.length > 1 ? p[p.length - 2] : null;\n  }\n  /**\n   * @internal\n   */\n  children(t) {\n    const n = findNode(t, this._root);\n    return n ? n.children.map(t => t.value) : [];\n  }\n  /**\n   * @internal\n   */\n  firstChild(t) {\n    const n = findNode(t, this._root);\n    return n && n.children.length > 0 ? n.children[0].value : null;\n  }\n  /**\n   * @internal\n   */\n  siblings(t) {\n    const p = findPath(t, this._root);\n    if (p.length < 2) return [];\n    const c = p[p.length - 2].children.map(c => c.value);\n    return c.filter(cc => cc !== t);\n  }\n  /**\n   * @internal\n   */\n  pathFromRoot(t) {\n    return findPath(t, this._root).map(s => s.value);\n  }\n}\n// DFS for the node matching the value\nfunction findNode(value, node) {\n  if (value === node.value) return node;\n  for (const child of node.children) {\n    const node = findNode(value, child);\n    if (node) return node;\n  }\n  return null;\n}\n// Return the path to the node with the given value using DFS\nfunction findPath(value, node) {\n  if (value === node.value) return [node];\n  for (const child of node.children) {\n    const path = findPath(value, child);\n    if (path.length) {\n      path.unshift(node);\n      return path;\n    }\n  }\n  return [];\n}\nclass TreeNode {\n  value;\n  children;\n  constructor(value, children) {\n    this.value = value;\n    this.children = children;\n  }\n  toString() {\n    return `TreeNode(${this.value})`;\n  }\n}\n// Return the list of T indexed by outlet name\nfunction nodeChildrenAsMap(node) {\n  const map = {};\n  if (node) {\n    node.children.forEach(child => map[child.value.outlet] = child);\n  }\n  return map;\n}\n\n/**\n * Represents the state of the router as a tree of activated routes.\n *\n * @usageNotes\n *\n * Every node in the route tree is an `ActivatedRoute` instance\n * that knows about the \"consumed\" URL segments, the extracted parameters,\n * and the resolved data.\n * Use the `ActivatedRoute` properties to traverse the tree from any node.\n *\n * The following fragment shows how a component gets the root node\n * of the current state to establish its own route tree:\n *\n * ```ts\n * @Component({templateUrl:'template.html'})\n * class MyComponent {\n *   constructor(router: Router) {\n *     const state: RouterState = router.routerState;\n *     const root: ActivatedRoute = state.root;\n *     const child = root.firstChild;\n *     const id: Observable<string> = child.params.map(p => p.id);\n *     //...\n *   }\n * }\n * ```\n *\n * @see {@link ActivatedRoute}\n * @see [Getting route information](guide/routing/common-router-tasks#getting-route-information)\n *\n * @publicApi\n */\nclass RouterState extends Tree {\n  snapshot;\n  /** @internal */\n  constructor(root, /** The current snapshot of the router state */\n  snapshot) {\n    super(root);\n    this.snapshot = snapshot;\n    setRouterState(this, root);\n  }\n  toString() {\n    return this.snapshot.toString();\n  }\n}\nfunction createEmptyState(rootComponent) {\n  const snapshot = createEmptyStateSnapshot(rootComponent);\n  const emptyUrl = new BehaviorSubject([new UrlSegment('', {})]);\n  const emptyParams = new BehaviorSubject({});\n  const emptyData = new BehaviorSubject({});\n  const emptyQueryParams = new BehaviorSubject({});\n  const fragment = new BehaviorSubject('');\n  const activated = new ActivatedRoute(emptyUrl, emptyParams, emptyQueryParams, fragment, emptyData, PRIMARY_OUTLET, rootComponent, snapshot.root);\n  activated.snapshot = snapshot.root;\n  return new RouterState(new TreeNode(activated, []), snapshot);\n}\nfunction createEmptyStateSnapshot(rootComponent) {\n  const emptyParams = {};\n  const emptyData = {};\n  const emptyQueryParams = {};\n  const fragment = '';\n  const activated = new ActivatedRouteSnapshot([], emptyParams, emptyQueryParams, fragment, emptyData, PRIMARY_OUTLET, rootComponent, null, {});\n  return new RouterStateSnapshot('', new TreeNode(activated, []));\n}\n/**\n * Provides access to information about a route associated with a component\n * that is loaded in an outlet.\n * Use to traverse the `RouterState` tree and extract information from nodes.\n *\n * The following example shows how to construct a component using information from a\n * currently activated route.\n *\n * Note: the observables in this class only emit when the current and previous values differ based\n * on shallow equality. For example, changing deeply nested properties in resolved `data` will not\n * cause the `ActivatedRoute.data` `Observable` to emit a new value.\n *\n * {@example router/activated-route/module.ts region=\"activated-route\"\n *     header=\"activated-route.component.ts\"}\n *\n * @see [Getting route information](guide/routing/common-router-tasks#getting-route-information)\n *\n * @publicApi\n */\nclass ActivatedRoute {\n  urlSubject;\n  paramsSubject;\n  queryParamsSubject;\n  fragmentSubject;\n  dataSubject;\n  outlet;\n  component;\n  /** The current snapshot of this route */\n  snapshot;\n  /** @internal */\n  _futureSnapshot;\n  /** @internal */\n  _routerState;\n  /** @internal */\n  _paramMap;\n  /** @internal */\n  _queryParamMap;\n  /** An Observable of the resolved route title */\n  title;\n  /** An observable of the URL segments matched by this route. */\n  url;\n  /** An observable of the matrix parameters scoped to this route. */\n  params;\n  /** An observable of the query parameters shared by all the routes. */\n  queryParams;\n  /** An observable of the URL fragment shared by all the routes. */\n  fragment;\n  /** An observable of the static and resolved data of this route. */\n  data;\n  /** @internal */\n  constructor(/** @internal */\n  urlSubject, /** @internal */\n  paramsSubject, /** @internal */\n  queryParamsSubject, /** @internal */\n  fragmentSubject, /** @internal */\n  dataSubject, /** The outlet name of the route, a constant. */\n  outlet, /** The component of the route, a constant. */\n  component, futureSnapshot) {\n    this.urlSubject = urlSubject;\n    this.paramsSubject = paramsSubject;\n    this.queryParamsSubject = queryParamsSubject;\n    this.fragmentSubject = fragmentSubject;\n    this.dataSubject = dataSubject;\n    this.outlet = outlet;\n    this.component = component;\n    this._futureSnapshot = futureSnapshot;\n    this.title = this.dataSubject?.pipe(map(d => d[RouteTitleKey])) ?? of(undefined);\n    // TODO(atscott): Verify that these can be changed to `.asObservable()` with TGP.\n    this.url = urlSubject;\n    this.params = paramsSubject;\n    this.queryParams = queryParamsSubject;\n    this.fragment = fragmentSubject;\n    this.data = dataSubject;\n  }\n  /** The configuration used to match this route. */\n  get routeConfig() {\n    return this._futureSnapshot.routeConfig;\n  }\n  /** The root of the router state. */\n  get root() {\n    return this._routerState.root;\n  }\n  /** The parent of this route in the router state tree. */\n  get parent() {\n    return this._routerState.parent(this);\n  }\n  /** The first child of this route in the router state tree. */\n  get firstChild() {\n    return this._routerState.firstChild(this);\n  }\n  /** The children of this route in the router state tree. */\n  get children() {\n    return this._routerState.children(this);\n  }\n  /** The path from the root of the router state tree to this route. */\n  get pathFromRoot() {\n    return this._routerState.pathFromRoot(this);\n  }\n  /**\n   * An Observable that contains a map of the required and optional parameters\n   * specific to the route.\n   * The map supports retrieving single and multiple values from the same parameter.\n   */\n  get paramMap() {\n    this._paramMap ??= this.params.pipe(map(p => convertToParamMap(p)));\n    return this._paramMap;\n  }\n  /**\n   * An Observable that contains a map of the query parameters available to all routes.\n   * The map supports retrieving single and multiple values from the query parameter.\n   */\n  get queryParamMap() {\n    this._queryParamMap ??= this.queryParams.pipe(map(p => convertToParamMap(p)));\n    return this._queryParamMap;\n  }\n  toString() {\n    return this.snapshot ? this.snapshot.toString() : `Future(${this._futureSnapshot})`;\n  }\n}\n/**\n * Returns the inherited params, data, and resolve for a given route.\n *\n * By default, we do not inherit parent data unless the current route is path-less or the parent\n * route is component-less.\n */\nfunction getInherited(route, parent, paramsInheritanceStrategy = 'emptyOnly') {\n  let inherited;\n  const {\n    routeConfig\n  } = route;\n  if (parent !== null && (paramsInheritanceStrategy === 'always' ||\n  // inherit parent data if route is empty path\n  routeConfig?.path === '' ||\n  // inherit parent data if parent was componentless\n  !parent.component && !parent.routeConfig?.loadComponent)) {\n    inherited = {\n      params: {\n        ...parent.params,\n        ...route.params\n      },\n      data: {\n        ...parent.data,\n        ...route.data\n      },\n      resolve: {\n        // Snapshots are created with data inherited from parent and guards (i.e. canActivate) can\n        // change data because it's not frozen...\n        // This first line could be deleted chose to break/disallow mutating the `data` object in\n        // guards.\n        // Note that data from parents still override this mutated data so anyone relying on this\n        // might be surprised that it doesn't work if parent data is inherited but otherwise does.\n        ...route.data,\n        // Ensure inherited resolved data overrides inherited static data\n        ...parent.data,\n        // static data from the current route overrides any inherited data\n        ...routeConfig?.data,\n        // resolved data from current route overrides everything\n        ...route._resolvedData\n      }\n    };\n  } else {\n    inherited = {\n      params: {\n        ...route.params\n      },\n      data: {\n        ...route.data\n      },\n      resolve: {\n        ...route.data,\n        ...(route._resolvedData ?? {})\n      }\n    };\n  }\n  if (routeConfig && hasStaticTitle(routeConfig)) {\n    inherited.resolve[RouteTitleKey] = routeConfig.title;\n  }\n  return inherited;\n}\n/**\n * @description\n *\n * Contains the information about a route associated with a component loaded in an\n * outlet at a particular moment in time. ActivatedRouteSnapshot can also be used to\n * traverse the router state tree.\n *\n * The following example initializes a component with route information extracted\n * from the snapshot of the root node at the time of creation.\n *\n * ```ts\n * @Component({templateUrl:'./my-component.html'})\n * class MyComponent {\n *   constructor(route: ActivatedRoute) {\n *     const id: string = route.snapshot.params.id;\n *     const url: string = route.snapshot.url.join('');\n *     const user = route.snapshot.data.user;\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nclass ActivatedRouteSnapshot {\n  url;\n  params;\n  queryParams;\n  fragment;\n  data;\n  outlet;\n  component;\n  /** The configuration used to match this route **/\n  routeConfig;\n  /** @internal */\n  _resolve;\n  /** @internal */\n  _resolvedData;\n  /** @internal */\n  _routerState;\n  /** @internal */\n  _paramMap;\n  /** @internal */\n  _queryParamMap;\n  /** The resolved route title */\n  get title() {\n    // Note: This _must_ be a getter because the data is mutated in the resolvers. Title will not be\n    // available at the time of class instantiation.\n    return this.data?.[RouteTitleKey];\n  }\n  /** @internal */\n  constructor(/** The URL segments matched by this route */\n  url,\n  /**\n   *  The matrix parameters scoped to this route.\n   *\n   *  You can compute all params (or data) in the router state or to get params outside\n   *  of an activated component by traversing the `RouterState` tree as in the following\n   *  example:\n   *  ```ts\n   *  collectRouteParams(router: Router) {\n   *    let params = {};\n   *    let stack: ActivatedRouteSnapshot[] = [router.routerState.snapshot.root];\n   *    while (stack.length > 0) {\n   *      const route = stack.pop()!;\n   *      params = {...params, ...route.params};\n   *      stack.push(...route.children);\n   *    }\n   *    return params;\n   *  }\n   *  ```\n   */\n  params, /** The query parameters shared by all the routes */\n  queryParams, /** The URL fragment shared by all the routes */\n  fragment, /** The static and resolved data of this route */\n  data, /** The outlet name of the route */\n  outlet, /** The component of the route */\n  component, routeConfig, resolve) {\n    this.url = url;\n    this.params = params;\n    this.queryParams = queryParams;\n    this.fragment = fragment;\n    this.data = data;\n    this.outlet = outlet;\n    this.component = component;\n    this.routeConfig = routeConfig;\n    this._resolve = resolve;\n  }\n  /** The root of the router state */\n  get root() {\n    return this._routerState.root;\n  }\n  /** The parent of this route in the router state tree */\n  get parent() {\n    return this._routerState.parent(this);\n  }\n  /** The first child of this route in the router state tree */\n  get firstChild() {\n    return this._routerState.firstChild(this);\n  }\n  /** The children of this route in the router state tree */\n  get children() {\n    return this._routerState.children(this);\n  }\n  /** The path from the root of the router state tree to this route */\n  get pathFromRoot() {\n    return this._routerState.pathFromRoot(this);\n  }\n  get paramMap() {\n    this._paramMap ??= convertToParamMap(this.params);\n    return this._paramMap;\n  }\n  get queryParamMap() {\n    this._queryParamMap ??= convertToParamMap(this.queryParams);\n    return this._queryParamMap;\n  }\n  toString() {\n    const url = this.url.map(segment => segment.toString()).join('/');\n    const matched = this.routeConfig ? this.routeConfig.path : '';\n    return `Route(url:'${url}', path:'${matched}')`;\n  }\n}\n/**\n * @description\n *\n * Represents the state of the router at a moment in time.\n *\n * This is a tree of activated route snapshots. Every node in this tree knows about\n * the \"consumed\" URL segments, the extracted parameters, and the resolved data.\n *\n * The following example shows how a component is initialized with information\n * from the snapshot of the root node's state at the time of creation.\n *\n * ```ts\n * @Component({templateUrl:'template.html'})\n * class MyComponent {\n *   constructor(router: Router) {\n *     const state: RouterState = router.routerState;\n *     const snapshot: RouterStateSnapshot = state.snapshot;\n *     const root: ActivatedRouteSnapshot = snapshot.root;\n *     const child = root.firstChild;\n *     const id: Observable<string> = child.params.map(p => p.id);\n *     //...\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nclass RouterStateSnapshot extends Tree {\n  url;\n  /** @internal */\n  constructor(/** The url from which this snapshot was created */\n  url, root) {\n    super(root);\n    this.url = url;\n    setRouterState(this, root);\n  }\n  toString() {\n    return serializeNode(this._root);\n  }\n}\nfunction setRouterState(state, node) {\n  node.value._routerState = state;\n  node.children.forEach(c => setRouterState(state, c));\n}\nfunction serializeNode(node) {\n  const c = node.children.length > 0 ? ` { ${node.children.map(serializeNode).join(', ')} } ` : '';\n  return `${node.value}${c}`;\n}\n/**\n * The expectation is that the activate route is created with the right set of parameters.\n * So we push new values into the observables only when they are not the initial values.\n * And we detect that by checking if the snapshot field is set.\n */\nfunction advanceActivatedRoute(route) {\n  if (route.snapshot) {\n    const currentSnapshot = route.snapshot;\n    const nextSnapshot = route._futureSnapshot;\n    route.snapshot = nextSnapshot;\n    if (!shallowEqual(currentSnapshot.queryParams, nextSnapshot.queryParams)) {\n      route.queryParamsSubject.next(nextSnapshot.queryParams);\n    }\n    if (currentSnapshot.fragment !== nextSnapshot.fragment) {\n      route.fragmentSubject.next(nextSnapshot.fragment);\n    }\n    if (!shallowEqual(currentSnapshot.params, nextSnapshot.params)) {\n      route.paramsSubject.next(nextSnapshot.params);\n    }\n    if (!shallowEqualArrays(currentSnapshot.url, nextSnapshot.url)) {\n      route.urlSubject.next(nextSnapshot.url);\n    }\n    if (!shallowEqual(currentSnapshot.data, nextSnapshot.data)) {\n      route.dataSubject.next(nextSnapshot.data);\n    }\n  } else {\n    route.snapshot = route._futureSnapshot;\n    // this is for resolved data\n    route.dataSubject.next(route._futureSnapshot.data);\n  }\n}\nfunction equalParamsAndUrlSegments(a, b) {\n  const equalUrlParams = shallowEqual(a.params, b.params) && equalSegments(a.url, b.url);\n  const parentsMismatch = !a.parent !== !b.parent;\n  return equalUrlParams && !parentsMismatch && (!a.parent || equalParamsAndUrlSegments(a.parent, b.parent));\n}\nfunction hasStaticTitle(config) {\n  return typeof config.title === 'string' || config.title === null;\n}\n\n/**\n * An `InjectionToken` provided by the `RouterOutlet` and can be set using the `routerOutletData`\n * input.\n *\n * When unset, this value is `null` by default.\n *\n * @usageNotes\n *\n * To set the data from the template of the component with `router-outlet`:\n * ```html\n * <router-outlet [routerOutletData]=\"{name: 'Angular'}\" />\n * ```\n *\n * To read the data in the routed component:\n * ```ts\n * data = inject(ROUTER_OUTLET_DATA) as Signal<{name: string}>;\n * ```\n *\n * @publicApi\n */\nconst ROUTER_OUTLET_DATA = new InjectionToken(ngDevMode ? 'RouterOutlet data' : '');\n/**\n * @description\n *\n * Acts as a placeholder that Angular dynamically fills based on the current router state.\n *\n * Each outlet can have a unique name, determined by the optional `name` attribute.\n * The name cannot be set or changed dynamically. If not set, default value is \"primary\".\n *\n * ```html\n * <router-outlet></router-outlet>\n * <router-outlet name='left'></router-outlet>\n * <router-outlet name='right'></router-outlet>\n * ```\n *\n * Named outlets can be the targets of secondary routes.\n * The `Route` object for a secondary route has an `outlet` property to identify the target outlet:\n *\n * `{path: <base-path>, component: <component>, outlet: <target_outlet_name>}`\n *\n * Using named outlets and secondary routes, you can target multiple outlets in\n * the same `RouterLink` directive.\n *\n * The router keeps track of separate branches in a navigation tree for each named outlet and\n * generates a representation of that tree in the URL.\n * The URL for a secondary route uses the following syntax to specify both the primary and secondary\n * routes at the same time:\n *\n * `http://base-path/primary-route-path(outlet-name:route-path)`\n *\n * A router outlet emits an activate event when a new component is instantiated,\n * deactivate event when a component is destroyed.\n * An attached event emits when the `RouteReuseStrategy` instructs the outlet to reattach the\n * subtree, and the detached event emits when the `RouteReuseStrategy` instructs the outlet to\n * detach the subtree.\n *\n * ```html\n * <router-outlet\n *   (activate)='onActivate($event)'\n *   (deactivate)='onDeactivate($event)'\n *   (attach)='onAttach($event)'\n *   (detach)='onDetach($event)'></router-outlet>\n * ```\n *\n * @see {@link RouterLink}\n * @see {@link Route}\n * @ngModule RouterModule\n *\n * @publicApi\n */\nclass RouterOutlet {\n  activated = null;\n  /** @internal */\n  get activatedComponentRef() {\n    return this.activated;\n  }\n  _activatedRoute = null;\n  /**\n   * The name of the outlet\n   *\n   */\n  name = PRIMARY_OUTLET;\n  activateEvents = new EventEmitter();\n  deactivateEvents = new EventEmitter();\n  /**\n   * Emits an attached component instance when the `RouteReuseStrategy` instructs to re-attach a\n   * previously detached subtree.\n   **/\n  attachEvents = new EventEmitter();\n  /**\n   * Emits a detached component instance when the `RouteReuseStrategy` instructs to detach the\n   * subtree.\n   */\n  detachEvents = new EventEmitter();\n  /**\n   * Data that will be provided to the child injector through the `ROUTER_OUTLET_DATA` token.\n   *\n   * When unset, the value of the token is `undefined` by default.\n   */\n  routerOutletData = input(undefined);\n  parentContexts = inject(ChildrenOutletContexts);\n  location = inject(ViewContainerRef);\n  changeDetector = inject(ChangeDetectorRef);\n  inputBinder = inject(INPUT_BINDER, {\n    optional: true\n  });\n  /** @docs-private */\n  supportsBindingToComponentInputs = true;\n  /** @docs-private */\n  ngOnChanges(changes) {\n    if (changes['name']) {\n      const {\n        firstChange,\n        previousValue\n      } = changes['name'];\n      if (firstChange) {\n        // The first change is handled by ngOnInit. Because ngOnChanges doesn't get called when no\n        // input is set at all, we need to centrally handle the first change there.\n        return;\n      }\n      // unregister with the old name\n      if (this.isTrackedInParentContexts(previousValue)) {\n        this.deactivate();\n        this.parentContexts.onChildOutletDestroyed(previousValue);\n      }\n      // register the new name\n      this.initializeOutletWithName();\n    }\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    // Ensure that the registered outlet is this one before removing it on the context.\n    if (this.isTrackedInParentContexts(this.name)) {\n      this.parentContexts.onChildOutletDestroyed(this.name);\n    }\n    this.inputBinder?.unsubscribeFromRouteData(this);\n  }\n  isTrackedInParentContexts(outletName) {\n    return this.parentContexts.getContext(outletName)?.outlet === this;\n  }\n  /** @docs-private */\n  ngOnInit() {\n    this.initializeOutletWithName();\n  }\n  initializeOutletWithName() {\n    this.parentContexts.onChildOutletCreated(this.name, this);\n    if (this.activated) {\n      return;\n    }\n    // If the outlet was not instantiated at the time the route got activated we need to populate\n    // the outlet when it is initialized (ie inside a NgIf)\n    const context = this.parentContexts.getContext(this.name);\n    if (context?.route) {\n      if (context.attachRef) {\n        // `attachRef` is populated when there is an existing component to mount\n        this.attach(context.attachRef, context.route);\n      } else {\n        // otherwise the component defined in the configuration is created\n        this.activateWith(context.route, context.injector);\n      }\n    }\n  }\n  get isActivated() {\n    return !!this.activated;\n  }\n  /**\n   * @returns The currently activated component instance.\n   * @throws An error if the outlet is not activated.\n   */\n  get component() {\n    if (!this.activated) throw new _RuntimeError(4012 /* RuntimeErrorCode.OUTLET_NOT_ACTIVATED */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'Outlet is not activated');\n    return this.activated.instance;\n  }\n  get activatedRoute() {\n    if (!this.activated) throw new _RuntimeError(4012 /* RuntimeErrorCode.OUTLET_NOT_ACTIVATED */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'Outlet is not activated');\n    return this._activatedRoute;\n  }\n  get activatedRouteData() {\n    if (this._activatedRoute) {\n      return this._activatedRoute.snapshot.data;\n    }\n    return {};\n  }\n  /**\n   * Called when the `RouteReuseStrategy` instructs to detach the subtree\n   */\n  detach() {\n    if (!this.activated) throw new _RuntimeError(4012 /* RuntimeErrorCode.OUTLET_NOT_ACTIVATED */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'Outlet is not activated');\n    this.location.detach();\n    const cmp = this.activated;\n    this.activated = null;\n    this._activatedRoute = null;\n    this.detachEvents.emit(cmp.instance);\n    return cmp;\n  }\n  /**\n   * Called when the `RouteReuseStrategy` instructs to re-attach a previously detached subtree\n   */\n  attach(ref, activatedRoute) {\n    this.activated = ref;\n    this._activatedRoute = activatedRoute;\n    this.location.insert(ref.hostView);\n    this.inputBinder?.bindActivatedRouteToOutletComponent(this);\n    this.attachEvents.emit(ref.instance);\n  }\n  deactivate() {\n    if (this.activated) {\n      const c = this.component;\n      this.activated.destroy();\n      this.activated = null;\n      this._activatedRoute = null;\n      this.deactivateEvents.emit(c);\n    }\n  }\n  activateWith(activatedRoute, environmentInjector) {\n    if (this.isActivated) {\n      throw new _RuntimeError(4013 /* RuntimeErrorCode.OUTLET_ALREADY_ACTIVATED */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'Cannot activate an already activated outlet');\n    }\n    this._activatedRoute = activatedRoute;\n    const location = this.location;\n    const snapshot = activatedRoute.snapshot;\n    const component = snapshot.component;\n    const childContexts = this.parentContexts.getOrCreateContext(this.name).children;\n    const injector = new OutletInjector(activatedRoute, childContexts, location.injector, this.routerOutletData);\n    this.activated = location.createComponent(component, {\n      index: location.length,\n      injector,\n      environmentInjector: environmentInjector\n    });\n    // Calling `markForCheck` to make sure we will run the change detection when the\n    // `RouterOutlet` is inside a `ChangeDetectionStrategy.OnPush` component.\n    this.changeDetector.markForCheck();\n    this.inputBinder?.bindActivatedRouteToOutletComponent(this);\n    this.activateEvents.emit(this.activated.instance);\n  }\n  static ɵfac = function RouterOutlet_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RouterOutlet)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RouterOutlet,\n    selectors: [[\"router-outlet\"]],\n    inputs: {\n      name: \"name\",\n      routerOutletData: [1, \"routerOutletData\"]\n    },\n    outputs: {\n      activateEvents: \"activate\",\n      deactivateEvents: \"deactivate\",\n      attachEvents: \"attach\",\n      detachEvents: \"detach\"\n    },\n    exportAs: [\"outlet\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterOutlet, [{\n    type: Directive,\n    args: [{\n      selector: 'router-outlet',\n      exportAs: 'outlet'\n    }]\n  }], null, {\n    name: [{\n      type: Input\n    }],\n    activateEvents: [{\n      type: Output,\n      args: ['activate']\n    }],\n    deactivateEvents: [{\n      type: Output,\n      args: ['deactivate']\n    }],\n    attachEvents: [{\n      type: Output,\n      args: ['attach']\n    }],\n    detachEvents: [{\n      type: Output,\n      args: ['detach']\n    }]\n  });\n})();\nclass OutletInjector {\n  route;\n  childContexts;\n  parent;\n  outletData;\n  constructor(route, childContexts, parent, outletData) {\n    this.route = route;\n    this.childContexts = childContexts;\n    this.parent = parent;\n    this.outletData = outletData;\n  }\n  get(token, notFoundValue) {\n    if (token === ActivatedRoute) {\n      return this.route;\n    }\n    if (token === ChildrenOutletContexts) {\n      return this.childContexts;\n    }\n    if (token === ROUTER_OUTLET_DATA) {\n      return this.outletData;\n    }\n    return this.parent.get(token, notFoundValue);\n  }\n}\nconst INPUT_BINDER = new InjectionToken('');\n/**\n * Injectable used as a tree-shakable provider for opting in to binding router data to component\n * inputs.\n *\n * The RouterOutlet registers itself with this service when an `ActivatedRoute` is attached or\n * activated. When this happens, the service subscribes to the `ActivatedRoute` observables (params,\n * queryParams, data) and sets the inputs of the component using `ComponentRef.setInput`.\n * Importantly, when an input does not have an item in the route data with a matching key, this\n * input is set to `undefined`. If it were not done this way, the previous information would be\n * retained if the data got removed from the route (i.e. if a query parameter is removed).\n *\n * The `RouterOutlet` should unregister itself when destroyed via `unsubscribeFromRouteData` so that\n * the subscriptions are cleaned up.\n */\nclass RoutedComponentInputBinder {\n  outletDataSubscriptions = new Map();\n  bindActivatedRouteToOutletComponent(outlet) {\n    this.unsubscribeFromRouteData(outlet);\n    this.subscribeToRouteData(outlet);\n  }\n  unsubscribeFromRouteData(outlet) {\n    this.outletDataSubscriptions.get(outlet)?.unsubscribe();\n    this.outletDataSubscriptions.delete(outlet);\n  }\n  subscribeToRouteData(outlet) {\n    const {\n      activatedRoute\n    } = outlet;\n    const dataSubscription = combineLatest([activatedRoute.queryParams, activatedRoute.params, activatedRoute.data]).pipe(switchMap(([queryParams, params, data], index) => {\n      data = {\n        ...queryParams,\n        ...params,\n        ...data\n      };\n      // Get the first result from the data subscription synchronously so it's available to\n      // the component as soon as possible (and doesn't require a second change detection).\n      if (index === 0) {\n        return of(data);\n      }\n      // Promise.resolve is used to avoid synchronously writing the wrong data when\n      // two of the Observables in the `combineLatest` stream emit one after\n      // another.\n      return Promise.resolve(data);\n    })).subscribe(data => {\n      // Outlet may have been deactivated or changed names to be associated with a different\n      // route\n      if (!outlet.isActivated || !outlet.activatedComponentRef || outlet.activatedRoute !== activatedRoute || activatedRoute.component === null) {\n        this.unsubscribeFromRouteData(outlet);\n        return;\n      }\n      const mirror = reflectComponentType(activatedRoute.component);\n      if (!mirror) {\n        this.unsubscribeFromRouteData(outlet);\n        return;\n      }\n      for (const {\n        templateName\n      } of mirror.inputs) {\n        outlet.activatedComponentRef.setInput(templateName, data[templateName]);\n      }\n    });\n    this.outletDataSubscriptions.set(outlet, dataSubscription);\n  }\n  static ɵfac = function RoutedComponentInputBinder_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RoutedComponentInputBinder)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: RoutedComponentInputBinder,\n    factory: RoutedComponentInputBinder.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RoutedComponentInputBinder, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * This component is used internally within the router to be a placeholder when an empty\n * router-outlet is needed. For example, with a config such as:\n *\n * `{path: 'parent', outlet: 'nav', children: [...]}`\n *\n * In order to render, there needs to be a component on this config, which will default\n * to this `EmptyOutletComponent`.\n */\nclass ɵEmptyOutletComponent {\n  static ɵfac = function ɵEmptyOutletComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ɵEmptyOutletComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ɵEmptyOutletComponent,\n    selectors: [[\"ng-component\"]],\n    exportAs: [\"emptyRouterOutlet\"],\n    decls: 1,\n    vars: 0,\n    template: function _EmptyOutletComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"router-outlet\");\n      }\n    },\n    dependencies: [RouterOutlet],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ɵEmptyOutletComponent, [{\n    type: Component,\n    args: [{\n      template: `<router-outlet/>`,\n      imports: [RouterOutlet],\n      // Used to avoid component ID collisions with user code.\n      exportAs: 'emptyRouterOutlet'\n    }]\n  }], null, null);\n})();\n/**\n * Makes a copy of the config and adds any default required properties.\n */\nfunction standardizeConfig(r) {\n  const children = r.children && r.children.map(standardizeConfig);\n  const c = children ? {\n    ...r,\n    children\n  } : {\n    ...r\n  };\n  if (!c.component && !c.loadComponent && (children || c.loadChildren) && c.outlet && c.outlet !== PRIMARY_OUTLET) {\n    c.component = ɵEmptyOutletComponent;\n  }\n  return c;\n}\nfunction createRouterState(routeReuseStrategy, curr, prevState) {\n  const root = createNode(routeReuseStrategy, curr._root, prevState ? prevState._root : undefined);\n  return new RouterState(root, curr);\n}\nfunction createNode(routeReuseStrategy, curr, prevState) {\n  // reuse an activated route that is currently displayed on the screen\n  if (prevState && routeReuseStrategy.shouldReuseRoute(curr.value, prevState.value.snapshot)) {\n    const value = prevState.value;\n    value._futureSnapshot = curr.value;\n    const children = createOrReuseChildren(routeReuseStrategy, curr, prevState);\n    return new TreeNode(value, children);\n  } else {\n    if (routeReuseStrategy.shouldAttach(curr.value)) {\n      // retrieve an activated route that is used to be displayed, but is not currently displayed\n      const detachedRouteHandle = routeReuseStrategy.retrieve(curr.value);\n      if (detachedRouteHandle !== null) {\n        const tree = detachedRouteHandle.route;\n        tree.value._futureSnapshot = curr.value;\n        tree.children = curr.children.map(c => createNode(routeReuseStrategy, c));\n        return tree;\n      }\n    }\n    const value = createActivatedRoute(curr.value);\n    const children = curr.children.map(c => createNode(routeReuseStrategy, c));\n    return new TreeNode(value, children);\n  }\n}\nfunction createOrReuseChildren(routeReuseStrategy, curr, prevState) {\n  return curr.children.map(child => {\n    for (const p of prevState.children) {\n      if (routeReuseStrategy.shouldReuseRoute(child.value, p.value.snapshot)) {\n        return createNode(routeReuseStrategy, child, p);\n      }\n    }\n    return createNode(routeReuseStrategy, child);\n  });\n}\nfunction createActivatedRoute(c) {\n  return new ActivatedRoute(new BehaviorSubject(c.url), new BehaviorSubject(c.params), new BehaviorSubject(c.queryParams), new BehaviorSubject(c.fragment), new BehaviorSubject(c.data), c.outlet, c.component, c);\n}\n\n/**\n * Can be returned by a `Router` guard to instruct the `Router` to redirect rather than continue\n * processing the path of the in-flight navigation. The `redirectTo` indicates _where_ the new\n * navigation should go to and the optional `navigationBehaviorOptions` can provide more information\n * about _how_ to perform the navigation.\n *\n * ```ts\n * const route: Route = {\n *   path: \"user/:userId\",\n *   component: User,\n *   canActivate: [\n *     () => {\n *       const router = inject(Router);\n *       const authService = inject(AuthenticationService);\n *\n *       if (!authService.isLoggedIn()) {\n *         const loginPath = router.parseUrl(\"/login\");\n *         return new RedirectCommand(loginPath, {\n *           skipLocationChange: \"true\",\n *         });\n *       }\n *\n *       return true;\n *     },\n *   ],\n * };\n * ```\n * @see [Routing guide](guide/routing/common-router-tasks#preventing-unauthorized-access)\n *\n * @publicApi\n */\nclass RedirectCommand {\n  redirectTo;\n  navigationBehaviorOptions;\n  constructor(redirectTo, navigationBehaviorOptions) {\n    this.redirectTo = redirectTo;\n    this.navigationBehaviorOptions = navigationBehaviorOptions;\n  }\n}\nconst NAVIGATION_CANCELING_ERROR = 'ngNavigationCancelingError';\nfunction redirectingNavigationError(urlSerializer, redirect) {\n  const {\n    redirectTo,\n    navigationBehaviorOptions\n  } = isUrlTree(redirect) ? {\n    redirectTo: redirect,\n    navigationBehaviorOptions: undefined\n  } : redirect;\n  const error = navigationCancelingError(ngDevMode && `Redirecting to \"${urlSerializer.serialize(redirectTo)}\"`, NavigationCancellationCode.Redirect);\n  error.url = redirectTo;\n  error.navigationBehaviorOptions = navigationBehaviorOptions;\n  return error;\n}\nfunction navigationCancelingError(message, code) {\n  const error = new Error(`NavigationCancelingError: ${message || ''}`);\n  error[NAVIGATION_CANCELING_ERROR] = true;\n  error.cancellationCode = code;\n  return error;\n}\nfunction isRedirectingNavigationCancelingError(error) {\n  return isNavigationCancelingError(error) && isUrlTree(error.url);\n}\nfunction isNavigationCancelingError(error) {\n  return !!error && error[NAVIGATION_CANCELING_ERROR];\n}\nlet warnedAboutUnsupportedInputBinding = false;\nconst activateRoutes = (rootContexts, routeReuseStrategy, forwardEvent, inputBindingEnabled) => map(t => {\n  new ActivateRoutes(routeReuseStrategy, t.targetRouterState, t.currentRouterState, forwardEvent, inputBindingEnabled).activate(rootContexts);\n  return t;\n});\nclass ActivateRoutes {\n  routeReuseStrategy;\n  futureState;\n  currState;\n  forwardEvent;\n  inputBindingEnabled;\n  constructor(routeReuseStrategy, futureState, currState, forwardEvent, inputBindingEnabled) {\n    this.routeReuseStrategy = routeReuseStrategy;\n    this.futureState = futureState;\n    this.currState = currState;\n    this.forwardEvent = forwardEvent;\n    this.inputBindingEnabled = inputBindingEnabled;\n  }\n  activate(parentContexts) {\n    const futureRoot = this.futureState._root;\n    const currRoot = this.currState ? this.currState._root : null;\n    this.deactivateChildRoutes(futureRoot, currRoot, parentContexts);\n    advanceActivatedRoute(this.futureState.root);\n    this.activateChildRoutes(futureRoot, currRoot, parentContexts);\n  }\n  // De-activate the child route that are not re-used for the future state\n  deactivateChildRoutes(futureNode, currNode, contexts) {\n    const children = nodeChildrenAsMap(currNode);\n    // Recurse on the routes active in the future state to de-activate deeper children\n    futureNode.children.forEach(futureChild => {\n      const childOutletName = futureChild.value.outlet;\n      this.deactivateRoutes(futureChild, children[childOutletName], contexts);\n      delete children[childOutletName];\n    });\n    // De-activate the routes that will not be re-used\n    Object.values(children).forEach(v => {\n      this.deactivateRouteAndItsChildren(v, contexts);\n    });\n  }\n  deactivateRoutes(futureNode, currNode, parentContext) {\n    const future = futureNode.value;\n    const curr = currNode ? currNode.value : null;\n    if (future === curr) {\n      // Reusing the node, check to see if the children need to be de-activated\n      if (future.component) {\n        // If we have a normal route, we need to go through an outlet.\n        const context = parentContext.getContext(future.outlet);\n        if (context) {\n          this.deactivateChildRoutes(futureNode, currNode, context.children);\n        }\n      } else {\n        // if we have a componentless route, we recurse but keep the same outlet map.\n        this.deactivateChildRoutes(futureNode, currNode, parentContext);\n      }\n    } else {\n      if (curr) {\n        // Deactivate the current route which will not be re-used\n        this.deactivateRouteAndItsChildren(currNode, parentContext);\n      }\n    }\n  }\n  deactivateRouteAndItsChildren(route, parentContexts) {\n    // If there is no component, the Route is never attached to an outlet (because there is no\n    // component to attach).\n    if (route.value.component && this.routeReuseStrategy.shouldDetach(route.value.snapshot)) {\n      this.detachAndStoreRouteSubtree(route, parentContexts);\n    } else {\n      this.deactivateRouteAndOutlet(route, parentContexts);\n    }\n  }\n  detachAndStoreRouteSubtree(route, parentContexts) {\n    const context = parentContexts.getContext(route.value.outlet);\n    const contexts = context && route.value.component ? context.children : parentContexts;\n    const children = nodeChildrenAsMap(route);\n    for (const treeNode of Object.values(children)) {\n      this.deactivateRouteAndItsChildren(treeNode, contexts);\n    }\n    if (context && context.outlet) {\n      const componentRef = context.outlet.detach();\n      const contexts = context.children.onOutletDeactivated();\n      this.routeReuseStrategy.store(route.value.snapshot, {\n        componentRef,\n        route,\n        contexts\n      });\n    }\n  }\n  deactivateRouteAndOutlet(route, parentContexts) {\n    const context = parentContexts.getContext(route.value.outlet);\n    // The context could be `null` if we are on a componentless route but there may still be\n    // children that need deactivating.\n    const contexts = context && route.value.component ? context.children : parentContexts;\n    const children = nodeChildrenAsMap(route);\n    for (const treeNode of Object.values(children)) {\n      this.deactivateRouteAndItsChildren(treeNode, contexts);\n    }\n    if (context) {\n      if (context.outlet) {\n        // Destroy the component\n        context.outlet.deactivate();\n        // Destroy the contexts for all the outlets that were in the component\n        context.children.onOutletDeactivated();\n      }\n      // Clear the information about the attached component on the context but keep the reference to\n      // the outlet. Clear even if outlet was not yet activated to avoid activating later with old\n      // info\n      context.attachRef = null;\n      context.route = null;\n    }\n  }\n  activateChildRoutes(futureNode, currNode, contexts) {\n    const children = nodeChildrenAsMap(currNode);\n    futureNode.children.forEach(c => {\n      this.activateRoutes(c, children[c.value.outlet], contexts);\n      this.forwardEvent(new ActivationEnd(c.value.snapshot));\n    });\n    if (futureNode.children.length) {\n      this.forwardEvent(new ChildActivationEnd(futureNode.value.snapshot));\n    }\n  }\n  activateRoutes(futureNode, currNode, parentContexts) {\n    const future = futureNode.value;\n    const curr = currNode ? currNode.value : null;\n    advanceActivatedRoute(future);\n    // reusing the node\n    if (future === curr) {\n      if (future.component) {\n        // If we have a normal route, we need to go through an outlet.\n        const context = parentContexts.getOrCreateContext(future.outlet);\n        this.activateChildRoutes(futureNode, currNode, context.children);\n      } else {\n        // if we have a componentless route, we recurse but keep the same outlet map.\n        this.activateChildRoutes(futureNode, currNode, parentContexts);\n      }\n    } else {\n      if (future.component) {\n        // if we have a normal route, we need to place the component into the outlet and recurse.\n        const context = parentContexts.getOrCreateContext(future.outlet);\n        if (this.routeReuseStrategy.shouldAttach(future.snapshot)) {\n          const stored = this.routeReuseStrategy.retrieve(future.snapshot);\n          this.routeReuseStrategy.store(future.snapshot, null);\n          context.children.onOutletReAttached(stored.contexts);\n          context.attachRef = stored.componentRef;\n          context.route = stored.route.value;\n          if (context.outlet) {\n            // Attach right away when the outlet has already been instantiated\n            // Otherwise attach from `RouterOutlet.ngOnInit` when it is instantiated\n            context.outlet.attach(stored.componentRef, stored.route.value);\n          }\n          advanceActivatedRoute(stored.route.value);\n          this.activateChildRoutes(futureNode, null, context.children);\n        } else {\n          context.attachRef = null;\n          context.route = future;\n          if (context.outlet) {\n            // Activate the outlet when it has already been instantiated\n            // Otherwise it will get activated from its `ngOnInit` when instantiated\n            context.outlet.activateWith(future, context.injector);\n          }\n          this.activateChildRoutes(futureNode, null, context.children);\n        }\n      } else {\n        // if we have a componentless route, we recurse but keep the same outlet map.\n        this.activateChildRoutes(futureNode, null, parentContexts);\n      }\n    }\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const context = parentContexts.getOrCreateContext(future.outlet);\n      const outlet = context.outlet;\n      if (outlet && this.inputBindingEnabled && !outlet.supportsBindingToComponentInputs && !warnedAboutUnsupportedInputBinding) {\n        console.warn(`'withComponentInputBinding' feature is enabled but ` + `this application is using an outlet that may not support binding to component inputs.`);\n        warnedAboutUnsupportedInputBinding = true;\n      }\n    }\n  }\n}\nclass CanActivate {\n  path;\n  route;\n  constructor(path) {\n    this.path = path;\n    this.route = this.path[this.path.length - 1];\n  }\n}\nclass CanDeactivate {\n  component;\n  route;\n  constructor(component, route) {\n    this.component = component;\n    this.route = route;\n  }\n}\nfunction getAllRouteGuards(future, curr, parentContexts) {\n  const futureRoot = future._root;\n  const currRoot = curr ? curr._root : null;\n  return getChildRouteGuards(futureRoot, currRoot, parentContexts, [futureRoot.value]);\n}\nfunction getCanActivateChild(p) {\n  const canActivateChild = p.routeConfig ? p.routeConfig.canActivateChild : null;\n  if (!canActivateChild || canActivateChild.length === 0) return null;\n  return {\n    node: p,\n    guards: canActivateChild\n  };\n}\nfunction getTokenOrFunctionIdentity(tokenOrFunction, injector) {\n  const NOT_FOUND = Symbol();\n  const result = injector.get(tokenOrFunction, NOT_FOUND);\n  if (result === NOT_FOUND) {\n    if (typeof tokenOrFunction === 'function' && !_isInjectable(tokenOrFunction)) {\n      // We think the token is just a function so return it as-is\n      return tokenOrFunction;\n    } else {\n      // This will throw the not found error\n      return injector.get(tokenOrFunction);\n    }\n  }\n  return result;\n}\nfunction getChildRouteGuards(futureNode, currNode, contexts, futurePath, checks = {\n  canDeactivateChecks: [],\n  canActivateChecks: []\n}) {\n  const prevChildren = nodeChildrenAsMap(currNode);\n  // Process the children of the future route\n  futureNode.children.forEach(c => {\n    getRouteGuards(c, prevChildren[c.value.outlet], contexts, futurePath.concat([c.value]), checks);\n    delete prevChildren[c.value.outlet];\n  });\n  // Process any children left from the current route (not active for the future route)\n  Object.entries(prevChildren).forEach(([k, v]) => deactivateRouteAndItsChildren(v, contexts.getContext(k), checks));\n  return checks;\n}\nfunction getRouteGuards(futureNode, currNode, parentContexts, futurePath, checks = {\n  canDeactivateChecks: [],\n  canActivateChecks: []\n}) {\n  const future = futureNode.value;\n  const curr = currNode ? currNode.value : null;\n  const context = parentContexts ? parentContexts.getContext(futureNode.value.outlet) : null;\n  // reusing the node\n  if (curr && future.routeConfig === curr.routeConfig) {\n    const shouldRun = shouldRunGuardsAndResolvers(curr, future, future.routeConfig.runGuardsAndResolvers);\n    if (shouldRun) {\n      checks.canActivateChecks.push(new CanActivate(futurePath));\n    } else {\n      // we need to set the data\n      future.data = curr.data;\n      future._resolvedData = curr._resolvedData;\n    }\n    // If we have a component, we need to go through an outlet.\n    if (future.component) {\n      getChildRouteGuards(futureNode, currNode, context ? context.children : null, futurePath, checks);\n      // if we have a componentless route, we recurse but keep the same outlet map.\n    } else {\n      getChildRouteGuards(futureNode, currNode, parentContexts, futurePath, checks);\n    }\n    if (shouldRun && context && context.outlet && context.outlet.isActivated) {\n      checks.canDeactivateChecks.push(new CanDeactivate(context.outlet.component, curr));\n    }\n  } else {\n    if (curr) {\n      deactivateRouteAndItsChildren(currNode, context, checks);\n    }\n    checks.canActivateChecks.push(new CanActivate(futurePath));\n    // If we have a component, we need to go through an outlet.\n    if (future.component) {\n      getChildRouteGuards(futureNode, null, context ? context.children : null, futurePath, checks);\n      // if we have a componentless route, we recurse but keep the same outlet map.\n    } else {\n      getChildRouteGuards(futureNode, null, parentContexts, futurePath, checks);\n    }\n  }\n  return checks;\n}\nfunction shouldRunGuardsAndResolvers(curr, future, mode) {\n  if (typeof mode === 'function') {\n    return mode(curr, future);\n  }\n  switch (mode) {\n    case 'pathParamsChange':\n      return !equalPath(curr.url, future.url);\n    case 'pathParamsOrQueryParamsChange':\n      return !equalPath(curr.url, future.url) || !shallowEqual(curr.queryParams, future.queryParams);\n    case 'always':\n      return true;\n    case 'paramsOrQueryParamsChange':\n      return !equalParamsAndUrlSegments(curr, future) || !shallowEqual(curr.queryParams, future.queryParams);\n    case 'paramsChange':\n    default:\n      return !equalParamsAndUrlSegments(curr, future);\n  }\n}\nfunction deactivateRouteAndItsChildren(route, context, checks) {\n  const children = nodeChildrenAsMap(route);\n  const r = route.value;\n  Object.entries(children).forEach(([childName, node]) => {\n    if (!r.component) {\n      deactivateRouteAndItsChildren(node, context, checks);\n    } else if (context) {\n      deactivateRouteAndItsChildren(node, context.children.getContext(childName), checks);\n    } else {\n      deactivateRouteAndItsChildren(node, null, checks);\n    }\n  });\n  if (!r.component) {\n    checks.canDeactivateChecks.push(new CanDeactivate(null, r));\n  } else if (context && context.outlet && context.outlet.isActivated) {\n    checks.canDeactivateChecks.push(new CanDeactivate(context.outlet.component, r));\n  } else {\n    checks.canDeactivateChecks.push(new CanDeactivate(null, r));\n  }\n}\n\n/**\n * Simple function check, but generic so type inference will flow. Example:\n *\n * function product(a: number, b: number) {\n *   return a * b;\n * }\n *\n * if (isFunction<product>(fn)) {\n *   return fn(1, 2);\n * } else {\n *   throw \"Must provide the `product` function\";\n * }\n */\nfunction isFunction(v) {\n  return typeof v === 'function';\n}\nfunction isBoolean(v) {\n  return typeof v === 'boolean';\n}\nfunction isCanLoad(guard) {\n  return guard && isFunction(guard.canLoad);\n}\nfunction isCanActivate(guard) {\n  return guard && isFunction(guard.canActivate);\n}\nfunction isCanActivateChild(guard) {\n  return guard && isFunction(guard.canActivateChild);\n}\nfunction isCanDeactivate(guard) {\n  return guard && isFunction(guard.canDeactivate);\n}\nfunction isCanMatch(guard) {\n  return guard && isFunction(guard.canMatch);\n}\nfunction isEmptyError(e) {\n  return e instanceof EmptyError || e?.name === 'EmptyError';\n}\nconst INITIAL_VALUE = /* @__PURE__ */Symbol('INITIAL_VALUE');\nfunction prioritizedGuardValue() {\n  return switchMap(obs => {\n    return combineLatest(obs.map(o => o.pipe(take(1), startWith(INITIAL_VALUE)))).pipe(map(results => {\n      for (const result of results) {\n        if (result === true) {\n          // If result is true, check the next one\n          continue;\n        } else if (result === INITIAL_VALUE) {\n          // If guard has not finished, we need to stop processing.\n          return INITIAL_VALUE;\n        } else if (result === false || isRedirect(result)) {\n          // Result finished and was not true. Return the result.\n          // Note that we only allow false/UrlTree/RedirectCommand. Other values are considered invalid and\n          // ignored.\n          return result;\n        }\n      }\n      // Everything resolved to true. Return true.\n      return true;\n    }), filter(item => item !== INITIAL_VALUE), take(1));\n  });\n}\nfunction isRedirect(val) {\n  return isUrlTree(val) || val instanceof RedirectCommand;\n}\nfunction checkGuards(injector, forwardEvent) {\n  return mergeMap(t => {\n    const {\n      targetSnapshot,\n      currentSnapshot,\n      guards: {\n        canActivateChecks,\n        canDeactivateChecks\n      }\n    } = t;\n    if (canDeactivateChecks.length === 0 && canActivateChecks.length === 0) {\n      return of({\n        ...t,\n        guardsResult: true\n      });\n    }\n    return runCanDeactivateChecks(canDeactivateChecks, targetSnapshot, currentSnapshot, injector).pipe(mergeMap(canDeactivate => {\n      return canDeactivate && isBoolean(canDeactivate) ? runCanActivateChecks(targetSnapshot, canActivateChecks, injector, forwardEvent) : of(canDeactivate);\n    }), map(guardsResult => ({\n      ...t,\n      guardsResult\n    })));\n  });\n}\nfunction runCanDeactivateChecks(checks, futureRSS, currRSS, injector) {\n  return from(checks).pipe(mergeMap(check => runCanDeactivate(check.component, check.route, currRSS, futureRSS, injector)), first(result => {\n    return result !== true;\n  }, true));\n}\nfunction runCanActivateChecks(futureSnapshot, checks, injector, forwardEvent) {\n  return from(checks).pipe(concatMap(check => {\n    return concat(fireChildActivationStart(check.route.parent, forwardEvent), fireActivationStart(check.route, forwardEvent), runCanActivateChild(futureSnapshot, check.path, injector), runCanActivate(futureSnapshot, check.route, injector));\n  }), first(result => {\n    return result !== true;\n  }, true));\n}\n/**\n * This should fire off `ActivationStart` events for each route being activated at this\n * level.\n * In other words, if you're activating `a` and `b` below, `path` will contain the\n * `ActivatedRouteSnapshot`s for both and we will fire `ActivationStart` for both. Always\n * return\n * `true` so checks continue to run.\n */\nfunction fireActivationStart(snapshot, forwardEvent) {\n  if (snapshot !== null && forwardEvent) {\n    forwardEvent(new ActivationStart(snapshot));\n  }\n  return of(true);\n}\n/**\n * This should fire off `ChildActivationStart` events for each route being activated at this\n * level.\n * In other words, if you're activating `a` and `b` below, `path` will contain the\n * `ActivatedRouteSnapshot`s for both and we will fire `ChildActivationStart` for both. Always\n * return\n * `true` so checks continue to run.\n */\nfunction fireChildActivationStart(snapshot, forwardEvent) {\n  if (snapshot !== null && forwardEvent) {\n    forwardEvent(new ChildActivationStart(snapshot));\n  }\n  return of(true);\n}\nfunction runCanActivate(futureRSS, futureARS, injector) {\n  const canActivate = futureARS.routeConfig ? futureARS.routeConfig.canActivate : null;\n  if (!canActivate || canActivate.length === 0) return of(true);\n  const canActivateObservables = canActivate.map(canActivate => {\n    return defer(() => {\n      const closestInjector = getClosestRouteInjector(futureARS) ?? injector;\n      const guard = getTokenOrFunctionIdentity(canActivate, closestInjector);\n      const guardVal = isCanActivate(guard) ? guard.canActivate(futureARS, futureRSS) : runInInjectionContext(closestInjector, () => guard(futureARS, futureRSS));\n      return wrapIntoObservable(guardVal).pipe(first());\n    });\n  });\n  return of(canActivateObservables).pipe(prioritizedGuardValue());\n}\nfunction runCanActivateChild(futureRSS, path, injector) {\n  const futureARS = path[path.length - 1];\n  const canActivateChildGuards = path.slice(0, path.length - 1).reverse().map(p => getCanActivateChild(p)).filter(_ => _ !== null);\n  const canActivateChildGuardsMapped = canActivateChildGuards.map(d => {\n    return defer(() => {\n      const guardsMapped = d.guards.map(canActivateChild => {\n        const closestInjector = getClosestRouteInjector(d.node) ?? injector;\n        const guard = getTokenOrFunctionIdentity(canActivateChild, closestInjector);\n        const guardVal = isCanActivateChild(guard) ? guard.canActivateChild(futureARS, futureRSS) : runInInjectionContext(closestInjector, () => guard(futureARS, futureRSS));\n        return wrapIntoObservable(guardVal).pipe(first());\n      });\n      return of(guardsMapped).pipe(prioritizedGuardValue());\n    });\n  });\n  return of(canActivateChildGuardsMapped).pipe(prioritizedGuardValue());\n}\nfunction runCanDeactivate(component, currARS, currRSS, futureRSS, injector) {\n  const canDeactivate = currARS && currARS.routeConfig ? currARS.routeConfig.canDeactivate : null;\n  if (!canDeactivate || canDeactivate.length === 0) return of(true);\n  const canDeactivateObservables = canDeactivate.map(c => {\n    const closestInjector = getClosestRouteInjector(currARS) ?? injector;\n    const guard = getTokenOrFunctionIdentity(c, closestInjector);\n    const guardVal = isCanDeactivate(guard) ? guard.canDeactivate(component, currARS, currRSS, futureRSS) : runInInjectionContext(closestInjector, () => guard(component, currARS, currRSS, futureRSS));\n    return wrapIntoObservable(guardVal).pipe(first());\n  });\n  return of(canDeactivateObservables).pipe(prioritizedGuardValue());\n}\nfunction runCanLoadGuards(injector, route, segments, urlSerializer) {\n  const canLoad = route.canLoad;\n  if (canLoad === undefined || canLoad.length === 0) {\n    return of(true);\n  }\n  const canLoadObservables = canLoad.map(injectionToken => {\n    const guard = getTokenOrFunctionIdentity(injectionToken, injector);\n    const guardVal = isCanLoad(guard) ? guard.canLoad(route, segments) : runInInjectionContext(injector, () => guard(route, segments));\n    return wrapIntoObservable(guardVal);\n  });\n  return of(canLoadObservables).pipe(prioritizedGuardValue(), redirectIfUrlTree(urlSerializer));\n}\nfunction redirectIfUrlTree(urlSerializer) {\n  return pipe(tap(result => {\n    if (typeof result === 'boolean') return;\n    throw redirectingNavigationError(urlSerializer, result);\n  }), map(result => result === true));\n}\nfunction runCanMatchGuards(injector, route, segments, urlSerializer) {\n  const canMatch = route.canMatch;\n  if (!canMatch || canMatch.length === 0) return of(true);\n  const canMatchObservables = canMatch.map(injectionToken => {\n    const guard = getTokenOrFunctionIdentity(injectionToken, injector);\n    const guardVal = isCanMatch(guard) ? guard.canMatch(route, segments) : runInInjectionContext(injector, () => guard(route, segments));\n    return wrapIntoObservable(guardVal);\n  });\n  return of(canMatchObservables).pipe(prioritizedGuardValue(), redirectIfUrlTree(urlSerializer));\n}\nclass NoMatch {\n  segmentGroup;\n  constructor(segmentGroup) {\n    this.segmentGroup = segmentGroup || null;\n  }\n}\nclass AbsoluteRedirect extends Error {\n  urlTree;\n  constructor(urlTree) {\n    super();\n    this.urlTree = urlTree;\n  }\n}\nfunction noMatch$1(segmentGroup) {\n  return throwError(new NoMatch(segmentGroup));\n}\nfunction namedOutletsRedirect(redirectTo) {\n  return throwError(new _RuntimeError(4000 /* RuntimeErrorCode.NAMED_OUTLET_REDIRECT */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Only absolute redirects can have named outlets. redirectTo: '${redirectTo}'`));\n}\nfunction canLoadFails(route) {\n  return throwError(navigationCancelingError((typeof ngDevMode === 'undefined' || ngDevMode) && `Cannot load children because the guard of the route \"path: '${route.path}'\" returned false`, NavigationCancellationCode.GuardRejected));\n}\nclass ApplyRedirects {\n  urlSerializer;\n  urlTree;\n  constructor(urlSerializer, urlTree) {\n    this.urlSerializer = urlSerializer;\n    this.urlTree = urlTree;\n  }\n  lineralizeSegments(route, urlTree) {\n    let res = [];\n    let c = urlTree.root;\n    while (true) {\n      res = res.concat(c.segments);\n      if (c.numberOfChildren === 0) {\n        return of(res);\n      }\n      if (c.numberOfChildren > 1 || !c.children[PRIMARY_OUTLET]) {\n        return namedOutletsRedirect(`${route.redirectTo}`);\n      }\n      c = c.children[PRIMARY_OUTLET];\n    }\n  }\n  applyRedirectCommands(segments, redirectTo, posParams, currentSnapshot, injector) {\n    if (typeof redirectTo !== 'string') {\n      const redirectToFn = redirectTo;\n      const {\n        queryParams,\n        fragment,\n        routeConfig,\n        url,\n        outlet,\n        params,\n        data,\n        title\n      } = currentSnapshot;\n      const newRedirect = runInInjectionContext(injector, () => redirectToFn({\n        params,\n        data,\n        queryParams,\n        fragment,\n        routeConfig,\n        url,\n        outlet,\n        title\n      }));\n      if (newRedirect instanceof UrlTree) {\n        throw new AbsoluteRedirect(newRedirect);\n      }\n      redirectTo = newRedirect;\n    }\n    const newTree = this.applyRedirectCreateUrlTree(redirectTo, this.urlSerializer.parse(redirectTo), segments, posParams);\n    if (redirectTo[0] === '/') {\n      throw new AbsoluteRedirect(newTree);\n    }\n    return newTree;\n  }\n  applyRedirectCreateUrlTree(redirectTo, urlTree, segments, posParams) {\n    const newRoot = this.createSegmentGroup(redirectTo, urlTree.root, segments, posParams);\n    return new UrlTree(newRoot, this.createQueryParams(urlTree.queryParams, this.urlTree.queryParams), urlTree.fragment);\n  }\n  createQueryParams(redirectToParams, actualParams) {\n    const res = {};\n    Object.entries(redirectToParams).forEach(([k, v]) => {\n      const copySourceValue = typeof v === 'string' && v[0] === ':';\n      if (copySourceValue) {\n        const sourceName = v.substring(1);\n        res[k] = actualParams[sourceName];\n      } else {\n        res[k] = v;\n      }\n    });\n    return res;\n  }\n  createSegmentGroup(redirectTo, group, segments, posParams) {\n    const updatedSegments = this.createSegments(redirectTo, group.segments, segments, posParams);\n    let children = {};\n    Object.entries(group.children).forEach(([name, child]) => {\n      children[name] = this.createSegmentGroup(redirectTo, child, segments, posParams);\n    });\n    return new UrlSegmentGroup(updatedSegments, children);\n  }\n  createSegments(redirectTo, redirectToSegments, actualSegments, posParams) {\n    return redirectToSegments.map(s => s.path[0] === ':' ? this.findPosParam(redirectTo, s, posParams) : this.findOrReturn(s, actualSegments));\n  }\n  findPosParam(redirectTo, redirectToUrlSegment, posParams) {\n    const pos = posParams[redirectToUrlSegment.path.substring(1)];\n    if (!pos) throw new _RuntimeError(4001 /* RuntimeErrorCode.MISSING_REDIRECT */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Cannot redirect to '${redirectTo}'. Cannot find '${redirectToUrlSegment.path}'.`);\n    return pos;\n  }\n  findOrReturn(redirectToUrlSegment, actualSegments) {\n    let idx = 0;\n    for (const s of actualSegments) {\n      if (s.path === redirectToUrlSegment.path) {\n        actualSegments.splice(idx);\n        return s;\n      }\n      idx++;\n    }\n    return redirectToUrlSegment;\n  }\n}\nconst noMatch = {\n  matched: false,\n  consumedSegments: [],\n  remainingSegments: [],\n  parameters: {},\n  positionalParamSegments: {}\n};\nfunction matchWithChecks(segmentGroup, route, segments, injector, urlSerializer) {\n  const result = match(segmentGroup, route, segments);\n  if (!result.matched) {\n    return of(result);\n  }\n  // Only create the Route's `EnvironmentInjector` if it matches the attempted\n  // navigation\n  injector = getOrCreateRouteInjectorIfNeeded(route, injector);\n  return runCanMatchGuards(injector, route, segments, urlSerializer).pipe(map(v => v === true ? result : {\n    ...noMatch\n  }));\n}\nfunction match(segmentGroup, route, segments) {\n  if (route.path === '**') {\n    return createWildcardMatchResult(segments);\n  }\n  if (route.path === '') {\n    if (route.pathMatch === 'full' && (segmentGroup.hasChildren() || segments.length > 0)) {\n      return {\n        ...noMatch\n      };\n    }\n    return {\n      matched: true,\n      consumedSegments: [],\n      remainingSegments: segments,\n      parameters: {},\n      positionalParamSegments: {}\n    };\n  }\n  const matcher = route.matcher || defaultUrlMatcher;\n  const res = matcher(segments, segmentGroup, route);\n  if (!res) return {\n    ...noMatch\n  };\n  const posParams = {};\n  Object.entries(res.posParams ?? {}).forEach(([k, v]) => {\n    posParams[k] = v.path;\n  });\n  const parameters = res.consumed.length > 0 ? {\n    ...posParams,\n    ...res.consumed[res.consumed.length - 1].parameters\n  } : posParams;\n  return {\n    matched: true,\n    consumedSegments: res.consumed,\n    remainingSegments: segments.slice(res.consumed.length),\n    // TODO(atscott): investigate combining parameters and positionalParamSegments\n    parameters,\n    positionalParamSegments: res.posParams ?? {}\n  };\n}\nfunction createWildcardMatchResult(segments) {\n  return {\n    matched: true,\n    parameters: segments.length > 0 ? last(segments).parameters : {},\n    consumedSegments: segments,\n    remainingSegments: [],\n    positionalParamSegments: {}\n  };\n}\nfunction split(segmentGroup, consumedSegments, slicedSegments, config) {\n  if (slicedSegments.length > 0 && containsEmptyPathMatchesWithNamedOutlets(segmentGroup, slicedSegments, config)) {\n    const s = new UrlSegmentGroup(consumedSegments, createChildrenForEmptyPaths(config, new UrlSegmentGroup(slicedSegments, segmentGroup.children)));\n    return {\n      segmentGroup: s,\n      slicedSegments: []\n    };\n  }\n  if (slicedSegments.length === 0 && containsEmptyPathMatches(segmentGroup, slicedSegments, config)) {\n    const s = new UrlSegmentGroup(segmentGroup.segments, addEmptyPathsToChildrenIfNeeded(segmentGroup, slicedSegments, config, segmentGroup.children));\n    return {\n      segmentGroup: s,\n      slicedSegments\n    };\n  }\n  const s = new UrlSegmentGroup(segmentGroup.segments, segmentGroup.children);\n  return {\n    segmentGroup: s,\n    slicedSegments\n  };\n}\nfunction addEmptyPathsToChildrenIfNeeded(segmentGroup, slicedSegments, routes, children) {\n  const res = {};\n  for (const r of routes) {\n    if (emptyPathMatch(segmentGroup, slicedSegments, r) && !children[getOutlet(r)]) {\n      const s = new UrlSegmentGroup([], {});\n      res[getOutlet(r)] = s;\n    }\n  }\n  return {\n    ...children,\n    ...res\n  };\n}\nfunction createChildrenForEmptyPaths(routes, primarySegment) {\n  const res = {};\n  res[PRIMARY_OUTLET] = primarySegment;\n  for (const r of routes) {\n    if (r.path === '' && getOutlet(r) !== PRIMARY_OUTLET) {\n      const s = new UrlSegmentGroup([], {});\n      res[getOutlet(r)] = s;\n    }\n  }\n  return res;\n}\nfunction containsEmptyPathMatchesWithNamedOutlets(segmentGroup, slicedSegments, routes) {\n  return routes.some(r => emptyPathMatch(segmentGroup, slicedSegments, r) && getOutlet(r) !== PRIMARY_OUTLET);\n}\nfunction containsEmptyPathMatches(segmentGroup, slicedSegments, routes) {\n  return routes.some(r => emptyPathMatch(segmentGroup, slicedSegments, r));\n}\nfunction emptyPathMatch(segmentGroup, slicedSegments, r) {\n  if ((segmentGroup.hasChildren() || slicedSegments.length > 0) && r.pathMatch === 'full') {\n    return false;\n  }\n  return r.path === '';\n}\nfunction noLeftoversInUrl(segmentGroup, segments, outlet) {\n  return segments.length === 0 && !segmentGroup.children[outlet];\n}\n\n/**\n * Class used to indicate there were no additional route config matches but that all segments of\n * the URL were consumed during matching so the route was URL matched. When this happens, we still\n * try to match child configs in case there are empty path children.\n */\nclass NoLeftoversInUrl {}\nfunction recognize$1(injector, configLoader, rootComponentType, config, urlTree, urlSerializer, paramsInheritanceStrategy = 'emptyOnly') {\n  return new Recognizer(injector, configLoader, rootComponentType, config, urlTree, paramsInheritanceStrategy, urlSerializer).recognize();\n}\nconst MAX_ALLOWED_REDIRECTS = 31;\nclass Recognizer {\n  injector;\n  configLoader;\n  rootComponentType;\n  config;\n  urlTree;\n  paramsInheritanceStrategy;\n  urlSerializer;\n  applyRedirects;\n  absoluteRedirectCount = 0;\n  allowRedirects = true;\n  constructor(injector, configLoader, rootComponentType, config, urlTree, paramsInheritanceStrategy, urlSerializer) {\n    this.injector = injector;\n    this.configLoader = configLoader;\n    this.rootComponentType = rootComponentType;\n    this.config = config;\n    this.urlTree = urlTree;\n    this.paramsInheritanceStrategy = paramsInheritanceStrategy;\n    this.urlSerializer = urlSerializer;\n    this.applyRedirects = new ApplyRedirects(this.urlSerializer, this.urlTree);\n  }\n  noMatchError(e) {\n    return new _RuntimeError(4002 /* RuntimeErrorCode.NO_MATCH */, typeof ngDevMode === 'undefined' || ngDevMode ? `Cannot match any routes. URL Segment: '${e.segmentGroup}'` : `'${e.segmentGroup}'`);\n  }\n  recognize() {\n    const rootSegmentGroup = split(this.urlTree.root, [], [], this.config).segmentGroup;\n    return this.match(rootSegmentGroup).pipe(map(({\n      children,\n      rootSnapshot\n    }) => {\n      const rootNode = new TreeNode(rootSnapshot, children);\n      const routeState = new RouterStateSnapshot('', rootNode);\n      const tree = createUrlTreeFromSnapshot(rootSnapshot, [], this.urlTree.queryParams, this.urlTree.fragment);\n      // https://github.com/angular/angular/issues/47307\n      // Creating the tree stringifies the query params\n      // We don't want to do this here so reassign them to the original.\n      tree.queryParams = this.urlTree.queryParams;\n      routeState.url = this.urlSerializer.serialize(tree);\n      return {\n        state: routeState,\n        tree\n      };\n    }));\n  }\n  match(rootSegmentGroup) {\n    // Use Object.freeze to prevent readers of the Router state from modifying it outside\n    // of a navigation, resulting in the router being out of sync with the browser.\n    const rootSnapshot = new ActivatedRouteSnapshot([], Object.freeze({}), Object.freeze({\n      ...this.urlTree.queryParams\n    }), this.urlTree.fragment, Object.freeze({}), PRIMARY_OUTLET, this.rootComponentType, null, {});\n    return this.processSegmentGroup(this.injector, this.config, rootSegmentGroup, PRIMARY_OUTLET, rootSnapshot).pipe(map(children => {\n      return {\n        children,\n        rootSnapshot\n      };\n    }), catchError(e => {\n      if (e instanceof AbsoluteRedirect) {\n        this.urlTree = e.urlTree;\n        return this.match(e.urlTree.root);\n      }\n      if (e instanceof NoMatch) {\n        throw this.noMatchError(e);\n      }\n      throw e;\n    }));\n  }\n  processSegmentGroup(injector, config, segmentGroup, outlet, parentRoute) {\n    if (segmentGroup.segments.length === 0 && segmentGroup.hasChildren()) {\n      return this.processChildren(injector, config, segmentGroup, parentRoute);\n    }\n    return this.processSegment(injector, config, segmentGroup, segmentGroup.segments, outlet, true, parentRoute).pipe(map(child => child instanceof TreeNode ? [child] : []));\n  }\n  /**\n   * Matches every child outlet in the `segmentGroup` to a `Route` in the config. Returns `null` if\n   * we cannot find a match for _any_ of the children.\n   *\n   * @param config - The `Routes` to match against\n   * @param segmentGroup - The `UrlSegmentGroup` whose children need to be matched against the\n   *     config.\n   */\n  processChildren(injector, config, segmentGroup, parentRoute) {\n    // Expand outlets one at a time, starting with the primary outlet. We need to do it this way\n    // because an absolute redirect from the primary outlet takes precedence.\n    const childOutlets = [];\n    for (const child of Object.keys(segmentGroup.children)) {\n      if (child === 'primary') {\n        childOutlets.unshift(child);\n      } else {\n        childOutlets.push(child);\n      }\n    }\n    return from(childOutlets).pipe(concatMap(childOutlet => {\n      const child = segmentGroup.children[childOutlet];\n      // Sort the config so that routes with outlets that match the one being activated\n      // appear first, followed by routes for other outlets, which might match if they have\n      // an empty path.\n      const sortedConfig = sortByMatchingOutlets(config, childOutlet);\n      return this.processSegmentGroup(injector, sortedConfig, child, childOutlet, parentRoute);\n    }), scan((children, outletChildren) => {\n      children.push(...outletChildren);\n      return children;\n    }), defaultIfEmpty(null), last$1(), mergeMap(children => {\n      if (children === null) return noMatch$1(segmentGroup);\n      // Because we may have matched two outlets to the same empty path segment, we can have\n      // multiple activated results for the same outlet. We should merge the children of\n      // these results so the final return value is only one `TreeNode` per outlet.\n      const mergedChildren = mergeEmptyPathMatches(children);\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        // This should really never happen - we are only taking the first match for each\n        // outlet and merge the empty path matches.\n        checkOutletNameUniqueness(mergedChildren);\n      }\n      sortActivatedRouteSnapshots(mergedChildren);\n      return of(mergedChildren);\n    }));\n  }\n  processSegment(injector, routes, segmentGroup, segments, outlet, allowRedirects, parentRoute) {\n    return from(routes).pipe(concatMap(r => {\n      return this.processSegmentAgainstRoute(r._injector ?? injector, routes, r, segmentGroup, segments, outlet, allowRedirects, parentRoute).pipe(catchError(e => {\n        if (e instanceof NoMatch) {\n          return of(null);\n        }\n        throw e;\n      }));\n    }), first(x => !!x), catchError(e => {\n      if (isEmptyError(e)) {\n        if (noLeftoversInUrl(segmentGroup, segments, outlet)) {\n          return of(new NoLeftoversInUrl());\n        }\n        return noMatch$1(segmentGroup);\n      }\n      throw e;\n    }));\n  }\n  processSegmentAgainstRoute(injector, routes, route, rawSegment, segments, outlet, allowRedirects, parentRoute) {\n    // We allow matches to empty paths when the outlets differ so we can match a url like `/(b:b)` to\n    // a config like\n    // * `{path: '', children: [{path: 'b', outlet: 'b'}]}`\n    // or even\n    // * `{path: '', outlet: 'a', children: [{path: 'b', outlet: 'b'}]`\n    //\n    // The exception here is when the segment outlet is for the primary outlet. This would\n    // result in a match inside the named outlet because all children there are written as primary\n    // outlets. So we need to prevent child named outlet matches in a url like `/b` in a config like\n    // * `{path: '', outlet: 'x' children: [{path: 'b'}]}`\n    // This should only match if the url is `/(x:b)`.\n    if (getOutlet(route) !== outlet && (outlet === PRIMARY_OUTLET || !emptyPathMatch(rawSegment, segments, route))) {\n      return noMatch$1(rawSegment);\n    }\n    if (route.redirectTo === undefined) {\n      return this.matchSegmentAgainstRoute(injector, rawSegment, route, segments, outlet, parentRoute);\n    }\n    if (this.allowRedirects && allowRedirects) {\n      return this.expandSegmentAgainstRouteUsingRedirect(injector, rawSegment, routes, route, segments, outlet, parentRoute);\n    }\n    return noMatch$1(rawSegment);\n  }\n  expandSegmentAgainstRouteUsingRedirect(injector, segmentGroup, routes, route, segments, outlet, parentRoute) {\n    const {\n      matched,\n      parameters,\n      consumedSegments,\n      positionalParamSegments,\n      remainingSegments\n    } = match(segmentGroup, route, segments);\n    if (!matched) return noMatch$1(segmentGroup);\n    // TODO(atscott): Move all of this under an if(ngDevMode) as a breaking change and allow stack\n    // size exceeded in production\n    if (typeof route.redirectTo === 'string' && route.redirectTo[0] === '/') {\n      this.absoluteRedirectCount++;\n      if (this.absoluteRedirectCount > MAX_ALLOWED_REDIRECTS) {\n        if (ngDevMode) {\n          throw new _RuntimeError(4016 /* RuntimeErrorCode.INFINITE_REDIRECT */, `Detected possible infinite redirect when redirecting from '${this.urlTree}' to '${route.redirectTo}'.\\n` + `This is currently a dev mode only error but will become a` + ` call stack size exceeded error in production in a future major version.`);\n        }\n        this.allowRedirects = false;\n      }\n    }\n    const currentSnapshot = new ActivatedRouteSnapshot(segments, parameters, Object.freeze({\n      ...this.urlTree.queryParams\n    }), this.urlTree.fragment, getData(route), getOutlet(route), route.component ?? route._loadedComponent ?? null, route, getResolve(route));\n    const inherited = getInherited(currentSnapshot, parentRoute, this.paramsInheritanceStrategy);\n    currentSnapshot.params = Object.freeze(inherited.params);\n    currentSnapshot.data = Object.freeze(inherited.data);\n    const newTree = this.applyRedirects.applyRedirectCommands(consumedSegments, route.redirectTo, positionalParamSegments, currentSnapshot, injector);\n    return this.applyRedirects.lineralizeSegments(route, newTree).pipe(mergeMap(newSegments => {\n      return this.processSegment(injector, routes, segmentGroup, newSegments.concat(remainingSegments), outlet, false, parentRoute);\n    }));\n  }\n  matchSegmentAgainstRoute(injector, rawSegment, route, segments, outlet, parentRoute) {\n    const matchResult = matchWithChecks(rawSegment, route, segments, injector, this.urlSerializer);\n    if (route.path === '**') {\n      // Prior versions of the route matching algorithm would stop matching at the wildcard route.\n      // We should investigate a better strategy for any existing children. Otherwise, these\n      // child segments are silently dropped from the navigation.\n      // https://github.com/angular/angular/issues/40089\n      rawSegment.children = {};\n    }\n    return matchResult.pipe(switchMap(result => {\n      if (!result.matched) {\n        return noMatch$1(rawSegment);\n      }\n      // If the route has an injector created from providers, we should start using that.\n      injector = route._injector ?? injector;\n      return this.getChildConfig(injector, route, segments).pipe(switchMap(({\n        routes: childConfig\n      }) => {\n        const childInjector = route._loadedInjector ?? injector;\n        const {\n          parameters,\n          consumedSegments,\n          remainingSegments\n        } = result;\n        const snapshot = new ActivatedRouteSnapshot(consumedSegments, parameters, Object.freeze({\n          ...this.urlTree.queryParams\n        }), this.urlTree.fragment, getData(route), getOutlet(route), route.component ?? route._loadedComponent ?? null, route, getResolve(route));\n        const inherited = getInherited(snapshot, parentRoute, this.paramsInheritanceStrategy);\n        snapshot.params = Object.freeze(inherited.params);\n        snapshot.data = Object.freeze(inherited.data);\n        const {\n          segmentGroup,\n          slicedSegments\n        } = split(rawSegment, consumedSegments, remainingSegments, childConfig);\n        if (slicedSegments.length === 0 && segmentGroup.hasChildren()) {\n          return this.processChildren(childInjector, childConfig, segmentGroup, snapshot).pipe(map(children => {\n            return new TreeNode(snapshot, children);\n          }));\n        }\n        if (childConfig.length === 0 && slicedSegments.length === 0) {\n          return of(new TreeNode(snapshot, []));\n        }\n        const matchedOnOutlet = getOutlet(route) === outlet;\n        // If we matched a config due to empty path match on a different outlet, we need to\n        // continue passing the current outlet for the segment rather than switch to PRIMARY.\n        // Note that we switch to primary when we have a match because outlet configs look like\n        // this: {path: 'a', outlet: 'a', children: [\n        //  {path: 'b', component: B},\n        //  {path: 'c', component: C},\n        // ]}\n        // Notice that the children of the named outlet are configured with the primary outlet\n        return this.processSegment(childInjector, childConfig, segmentGroup, slicedSegments, matchedOnOutlet ? PRIMARY_OUTLET : outlet, true, snapshot).pipe(map(child => {\n          return new TreeNode(snapshot, child instanceof TreeNode ? [child] : []);\n        }));\n      }));\n    }));\n  }\n  getChildConfig(injector, route, segments) {\n    if (route.children) {\n      // The children belong to the same module\n      return of({\n        routes: route.children,\n        injector\n      });\n    }\n    if (route.loadChildren) {\n      // lazy children belong to the loaded module\n      if (route._loadedRoutes !== undefined) {\n        return of({\n          routes: route._loadedRoutes,\n          injector: route._loadedInjector\n        });\n      }\n      return runCanLoadGuards(injector, route, segments, this.urlSerializer).pipe(mergeMap(shouldLoadResult => {\n        if (shouldLoadResult) {\n          return this.configLoader.loadChildren(injector, route).pipe(tap(cfg => {\n            route._loadedRoutes = cfg.routes;\n            route._loadedInjector = cfg.injector;\n          }));\n        }\n        return canLoadFails(route);\n      }));\n    }\n    return of({\n      routes: [],\n      injector\n    });\n  }\n}\nfunction sortActivatedRouteSnapshots(nodes) {\n  nodes.sort((a, b) => {\n    if (a.value.outlet === PRIMARY_OUTLET) return -1;\n    if (b.value.outlet === PRIMARY_OUTLET) return 1;\n    return a.value.outlet.localeCompare(b.value.outlet);\n  });\n}\nfunction hasEmptyPathConfig(node) {\n  const config = node.value.routeConfig;\n  return config && config.path === '';\n}\n/**\n * Finds `TreeNode`s with matching empty path route configs and merges them into `TreeNode` with\n * the children from each duplicate. This is necessary because different outlets can match a\n * single empty path route config and the results need to then be merged.\n */\nfunction mergeEmptyPathMatches(nodes) {\n  const result = [];\n  // The set of nodes which contain children that were merged from two duplicate empty path nodes.\n  const mergedNodes = new Set();\n  for (const node of nodes) {\n    if (!hasEmptyPathConfig(node)) {\n      result.push(node);\n      continue;\n    }\n    const duplicateEmptyPathNode = result.find(resultNode => node.value.routeConfig === resultNode.value.routeConfig);\n    if (duplicateEmptyPathNode !== undefined) {\n      duplicateEmptyPathNode.children.push(...node.children);\n      mergedNodes.add(duplicateEmptyPathNode);\n    } else {\n      result.push(node);\n    }\n  }\n  // For each node which has children from multiple sources, we need to recompute a new `TreeNode`\n  // by also merging those children. This is necessary when there are multiple empty path configs\n  // in a row. Put another way: whenever we combine children of two nodes, we need to also check\n  // if any of those children can be combined into a single node as well.\n  for (const mergedNode of mergedNodes) {\n    const mergedChildren = mergeEmptyPathMatches(mergedNode.children);\n    result.push(new TreeNode(mergedNode.value, mergedChildren));\n  }\n  return result.filter(n => !mergedNodes.has(n));\n}\nfunction checkOutletNameUniqueness(nodes) {\n  const names = {};\n  nodes.forEach(n => {\n    const routeWithSameOutletName = names[n.value.outlet];\n    if (routeWithSameOutletName) {\n      const p = routeWithSameOutletName.url.map(s => s.toString()).join('/');\n      const c = n.value.url.map(s => s.toString()).join('/');\n      throw new _RuntimeError(4006 /* RuntimeErrorCode.TWO_SEGMENTS_WITH_SAME_OUTLET */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Two segments cannot have the same outlet name: '${p}' and '${c}'.`);\n    }\n    names[n.value.outlet] = n.value;\n  });\n}\nfunction getData(route) {\n  return route.data || {};\n}\nfunction getResolve(route) {\n  return route.resolve || {};\n}\nfunction recognize(injector, configLoader, rootComponentType, config, serializer, paramsInheritanceStrategy) {\n  return mergeMap(t => recognize$1(injector, configLoader, rootComponentType, config, t.extractedUrl, serializer, paramsInheritanceStrategy).pipe(map(({\n    state: targetSnapshot,\n    tree: urlAfterRedirects\n  }) => {\n    return {\n      ...t,\n      targetSnapshot,\n      urlAfterRedirects\n    };\n  })));\n}\nfunction resolveData(paramsInheritanceStrategy, injector) {\n  return mergeMap(t => {\n    const {\n      targetSnapshot,\n      guards: {\n        canActivateChecks\n      }\n    } = t;\n    if (!canActivateChecks.length) {\n      return of(t);\n    }\n    // Iterating a Set in javascript  happens in insertion order so it is safe to use a `Set` to\n    // preserve the correct order that the resolvers should run in.\n    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Set#description\n    const routesWithResolversToRun = new Set(canActivateChecks.map(check => check.route));\n    const routesNeedingDataUpdates = new Set();\n    for (const route of routesWithResolversToRun) {\n      if (routesNeedingDataUpdates.has(route)) {\n        continue;\n      }\n      // All children under the route with a resolver to run need to recompute inherited data.\n      for (const newRoute of flattenRouteTree(route)) {\n        routesNeedingDataUpdates.add(newRoute);\n      }\n    }\n    let routesProcessed = 0;\n    return from(routesNeedingDataUpdates).pipe(concatMap(route => {\n      if (routesWithResolversToRun.has(route)) {\n        return runResolve(route, targetSnapshot, paramsInheritanceStrategy, injector);\n      } else {\n        route.data = getInherited(route, route.parent, paramsInheritanceStrategy).resolve;\n        return of(void 0);\n      }\n    }), tap(() => routesProcessed++), takeLast(1), mergeMap(_ => routesProcessed === routesNeedingDataUpdates.size ? of(t) : EMPTY));\n  });\n}\n/**\n *  Returns the `ActivatedRouteSnapshot` tree as an array, using DFS to traverse the route tree.\n */\nfunction flattenRouteTree(route) {\n  const descendants = route.children.map(child => flattenRouteTree(child)).flat();\n  return [route, ...descendants];\n}\nfunction runResolve(futureARS, futureRSS, paramsInheritanceStrategy, injector) {\n  const config = futureARS.routeConfig;\n  const resolve = futureARS._resolve;\n  if (config?.title !== undefined && !hasStaticTitle(config)) {\n    resolve[RouteTitleKey] = config.title;\n  }\n  return resolveNode(resolve, futureARS, futureRSS, injector).pipe(map(resolvedData => {\n    futureARS._resolvedData = resolvedData;\n    futureARS.data = getInherited(futureARS, futureARS.parent, paramsInheritanceStrategy).resolve;\n    return null;\n  }));\n}\nfunction resolveNode(resolve, futureARS, futureRSS, injector) {\n  const keys = getDataKeys(resolve);\n  if (keys.length === 0) {\n    return of({});\n  }\n  const data = {};\n  return from(keys).pipe(mergeMap(key => getResolver(resolve[key], futureARS, futureRSS, injector).pipe(first(), tap(value => {\n    if (value instanceof RedirectCommand) {\n      throw redirectingNavigationError(new DefaultUrlSerializer(), value);\n    }\n    data[key] = value;\n  }))), takeLast(1), map(() => data), catchError(e => isEmptyError(e) ? EMPTY : throwError(e)));\n}\nfunction getResolver(injectionToken, futureARS, futureRSS, injector) {\n  const closestInjector = getClosestRouteInjector(futureARS) ?? injector;\n  const resolver = getTokenOrFunctionIdentity(injectionToken, closestInjector);\n  const resolverValue = resolver.resolve ? resolver.resolve(futureARS, futureRSS) : runInInjectionContext(closestInjector, () => resolver(futureARS, futureRSS));\n  return wrapIntoObservable(resolverValue);\n}\n\n/**\n * Perform a side effect through a switchMap for every emission on the source Observable,\n * but return an Observable that is identical to the source. It's essentially the same as\n * the `tap` operator, but if the side effectful `next` function returns an ObservableInput,\n * it will wait before continuing with the original value.\n */\nfunction switchTap(next) {\n  return switchMap(v => {\n    const nextResult = next(v);\n    if (nextResult) {\n      return from(nextResult).pipe(map(() => v));\n    }\n    return of(v);\n  });\n}\n\n/**\n * Provides a strategy for setting the page title after a router navigation.\n *\n * The built-in implementation traverses the router state snapshot and finds the deepest primary\n * outlet with `title` property. Given the `Routes` below, navigating to\n * `/base/child(popup:aux)` would result in the document title being set to \"child\".\n * ```ts\n * [\n *   {path: 'base', title: 'base', children: [\n *     {path: 'child', title: 'child'},\n *   ],\n *   {path: 'aux', outlet: 'popup', title: 'popupTitle'}\n * ]\n * ```\n *\n * This class can be used as a base class for custom title strategies. That is, you can create your\n * own class that extends the `TitleStrategy`. Note that in the above example, the `title`\n * from the named outlet is never used. However, a custom strategy might be implemented to\n * incorporate titles in named outlets.\n *\n * @publicApi\n * @see [Page title guide](guide/routing/common-router-tasks#setting-the-page-title)\n */\nclass TitleStrategy {\n  /**\n   * @returns The `title` of the deepest primary route.\n   */\n  buildTitle(snapshot) {\n    let pageTitle;\n    let route = snapshot.root;\n    while (route !== undefined) {\n      pageTitle = this.getResolvedTitleForRoute(route) ?? pageTitle;\n      route = route.children.find(child => child.outlet === PRIMARY_OUTLET);\n    }\n    return pageTitle;\n  }\n  /**\n   * Given an `ActivatedRouteSnapshot`, returns the final value of the\n   * `Route.title` property, which can either be a static string or a resolved value.\n   */\n  getResolvedTitleForRoute(snapshot) {\n    return snapshot.data[RouteTitleKey];\n  }\n  static ɵfac = function TitleStrategy_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TitleStrategy)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TitleStrategy,\n    factory: () => (() => inject(DefaultTitleStrategy))(),\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TitleStrategy, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: () => inject(DefaultTitleStrategy)\n    }]\n  }], null, null);\n})();\n/**\n * The default `TitleStrategy` used by the router that updates the title using the `Title` service.\n */\nclass DefaultTitleStrategy extends TitleStrategy {\n  title;\n  constructor(title) {\n    super();\n    this.title = title;\n  }\n  /**\n   * Sets the title of the browser to the given value.\n   *\n   * @param title The `pageTitle` from the deepest primary route.\n   */\n  updateTitle(snapshot) {\n    const title = this.buildTitle(snapshot);\n    if (title !== undefined) {\n      this.title.setTitle(title);\n    }\n  }\n  static ɵfac = function DefaultTitleStrategy_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DefaultTitleStrategy)(i0.ɵɵinject(i1.Title));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DefaultTitleStrategy,\n    factory: DefaultTitleStrategy.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultTitleStrategy, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.Title\n  }], null);\n})();\n\n/**\n * A DI token for the router service.\n *\n * @publicApi\n */\nconst ROUTER_CONFIGURATION = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'router config' : '', {\n  providedIn: 'root',\n  factory: () => ({})\n});\n\n/**\n * The DI token for a router configuration.\n *\n * `ROUTES` is a low level API for router configuration via dependency injection.\n *\n * We recommend that in almost all cases to use higher level APIs such as `RouterModule.forRoot()`,\n * `provideRouter`, or `Router.resetConfig()`.\n *\n * @publicApi\n */\nconst ROUTES = new InjectionToken(ngDevMode ? 'ROUTES' : '');\nclass RouterConfigLoader {\n  componentLoaders = new WeakMap();\n  childrenLoaders = new WeakMap();\n  onLoadStartListener;\n  onLoadEndListener;\n  compiler = inject(Compiler);\n  loadComponent(route) {\n    if (this.componentLoaders.get(route)) {\n      return this.componentLoaders.get(route);\n    } else if (route._loadedComponent) {\n      return of(route._loadedComponent);\n    }\n    if (this.onLoadStartListener) {\n      this.onLoadStartListener(route);\n    }\n    const loadRunner = wrapIntoObservable(route.loadComponent()).pipe(map(maybeUnwrapDefaultExport), tap(component => {\n      if (this.onLoadEndListener) {\n        this.onLoadEndListener(route);\n      }\n      (typeof ngDevMode === 'undefined' || ngDevMode) && assertStandalone(route.path ?? '', component);\n      route._loadedComponent = component;\n    }), finalize(() => {\n      this.componentLoaders.delete(route);\n    }));\n    // Use custom ConnectableObservable as share in runners pipe increasing the bundle size too much\n    const loader = new ConnectableObservable(loadRunner, () => new Subject()).pipe(refCount());\n    this.componentLoaders.set(route, loader);\n    return loader;\n  }\n  loadChildren(parentInjector, route) {\n    if (this.childrenLoaders.get(route)) {\n      return this.childrenLoaders.get(route);\n    } else if (route._loadedRoutes) {\n      return of({\n        routes: route._loadedRoutes,\n        injector: route._loadedInjector\n      });\n    }\n    if (this.onLoadStartListener) {\n      this.onLoadStartListener(route);\n    }\n    const moduleFactoryOrRoutes$ = loadChildren(route, this.compiler, parentInjector, this.onLoadEndListener);\n    const loadRunner = moduleFactoryOrRoutes$.pipe(finalize(() => {\n      this.childrenLoaders.delete(route);\n    }));\n    // Use custom ConnectableObservable as share in runners pipe increasing the bundle size too much\n    const loader = new ConnectableObservable(loadRunner, () => new Subject()).pipe(refCount());\n    this.childrenLoaders.set(route, loader);\n    return loader;\n  }\n  static ɵfac = function RouterConfigLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RouterConfigLoader)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: RouterConfigLoader,\n    factory: RouterConfigLoader.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterConfigLoader, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/**\n * Executes a `route.loadChildren` callback and converts the result to an array of child routes and\n * an injector if that callback returned a module.\n *\n * This function is used for the route discovery during prerendering\n * in @angular-devkit/build-angular. If there are any updates to the contract here, it will require\n * an update to the extractor.\n */\nfunction loadChildren(route, compiler, parentInjector, onLoadEndListener) {\n  return wrapIntoObservable(route.loadChildren()).pipe(map(maybeUnwrapDefaultExport), mergeMap(t => {\n    if (t instanceof NgModuleFactory || Array.isArray(t)) {\n      return of(t);\n    } else {\n      return from(compiler.compileModuleAsync(t));\n    }\n  }), map(factoryOrRoutes => {\n    if (onLoadEndListener) {\n      onLoadEndListener(route);\n    }\n    // This injector comes from the `NgModuleRef` when lazy loading an `NgModule`. There is\n    // no injector associated with lazy loading a `Route` array.\n    let injector;\n    let rawRoutes;\n    let requireStandaloneComponents = false;\n    if (Array.isArray(factoryOrRoutes)) {\n      rawRoutes = factoryOrRoutes;\n      requireStandaloneComponents = true;\n    } else {\n      injector = factoryOrRoutes.create(parentInjector).injector;\n      // When loading a module that doesn't provide `RouterModule.forChild()` preloader\n      // will get stuck in an infinite loop. The child module's Injector will look to\n      // its parent `Injector` when it doesn't find any ROUTES so it will return routes\n      // for it's parent module instead.\n      rawRoutes = injector.get(ROUTES, [], {\n        optional: true,\n        self: true\n      }).flat();\n    }\n    const routes = rawRoutes.map(standardizeConfig);\n    (typeof ngDevMode === 'undefined' || ngDevMode) && validateConfig(routes, route.path, requireStandaloneComponents);\n    return {\n      routes,\n      injector\n    };\n  }));\n}\nfunction isWrappedDefaultExport(value) {\n  // We use `in` here with a string key `'default'`, because we expect `DefaultExport` objects to be\n  // dynamically imported ES modules with a spec-mandated `default` key. Thus we don't expect that\n  // `default` will be a renamed property.\n  return value && typeof value === 'object' && 'default' in value;\n}\nfunction maybeUnwrapDefaultExport(input) {\n  // As per `isWrappedDefaultExport`, the `default` key here is generated by the browser and not\n  // subject to property renaming, so we reference it with bracket access.\n  return isWrappedDefaultExport(input) ? input['default'] : input;\n}\n\n/**\n * @description\n *\n * Provides a way to migrate AngularJS applications to Angular.\n *\n * @publicApi\n */\nclass UrlHandlingStrategy {\n  static ɵfac = function UrlHandlingStrategy_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || UrlHandlingStrategy)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: UrlHandlingStrategy,\n    factory: () => (() => inject(DefaultUrlHandlingStrategy))(),\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UrlHandlingStrategy, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: () => inject(DefaultUrlHandlingStrategy)\n    }]\n  }], null, null);\n})();\n/**\n * @publicApi\n */\nclass DefaultUrlHandlingStrategy {\n  shouldProcessUrl(url) {\n    return true;\n  }\n  extract(url) {\n    return url;\n  }\n  merge(newUrlPart, wholeUrl) {\n    return newUrlPart;\n  }\n  static ɵfac = function DefaultUrlHandlingStrategy_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DefaultUrlHandlingStrategy)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DefaultUrlHandlingStrategy,\n    factory: DefaultUrlHandlingStrategy.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultUrlHandlingStrategy, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/// <reference types=\"dom-view-transitions\" />\nconst CREATE_VIEW_TRANSITION = new InjectionToken(ngDevMode ? 'view transition helper' : '');\nconst VIEW_TRANSITION_OPTIONS = new InjectionToken(ngDevMode ? 'view transition options' : '');\n/**\n * A helper function for using browser view transitions. This function skips the call to\n * `startViewTransition` if the browser does not support it.\n *\n * @returns A Promise that resolves when the view transition callback begins.\n */\nfunction createViewTransition(injector, from, to) {\n  const transitionOptions = injector.get(VIEW_TRANSITION_OPTIONS);\n  const document = injector.get(DOCUMENT);\n  // Create promises outside the Angular zone to avoid causing extra change detections\n  return injector.get(NgZone).runOutsideAngular(() => {\n    if (!document.startViewTransition || transitionOptions.skipNextTransition) {\n      transitionOptions.skipNextTransition = false;\n      // The timing of `startViewTransition` is closer to a macrotask. It won't be called\n      // until the current event loop exits so we use a promise resolved in a timeout instead\n      // of Promise.resolve().\n      return new Promise(resolve => setTimeout(resolve));\n    }\n    let resolveViewTransitionStarted;\n    const viewTransitionStarted = new Promise(resolve => {\n      resolveViewTransitionStarted = resolve;\n    });\n    const transition = document.startViewTransition(() => {\n      resolveViewTransitionStarted();\n      // We don't actually update dom within the transition callback. The resolving of the above\n      // promise unblocks the Router navigation, which synchronously activates and deactivates\n      // routes (the DOM update). This view transition waits for the next change detection to\n      // complete (below), which includes the update phase of the routed components.\n      return createRenderPromise(injector);\n      // TODO(atscott): Types in DefinitelyTyped are not up-to-date\n    });\n    const {\n      onViewTransitionCreated\n    } = transitionOptions;\n    if (onViewTransitionCreated) {\n      runInInjectionContext(injector, () => onViewTransitionCreated({\n        transition,\n        from,\n        to\n      }));\n    }\n    return viewTransitionStarted;\n  });\n}\n/**\n * Creates a promise that resolves after next render.\n */\nfunction createRenderPromise(injector) {\n  return new Promise(resolve => {\n    // Wait for the microtask queue to empty after the next render happens (by waiting a macrotask).\n    // This ensures any follow-up renders in the microtask queue are completed before the\n    // view transition starts animating.\n    afterNextRender({\n      read: () => setTimeout(resolve)\n    }, {\n      injector\n    });\n  });\n}\nconst NAVIGATION_ERROR_HANDLER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'navigation error handler' : '');\nclass NavigationTransitions {\n  currentNavigation = null;\n  currentTransition = null;\n  lastSuccessfulNavigation = null;\n  /**\n   * These events are used to communicate back to the Router about the state of the transition. The\n   * Router wants to respond to these events in various ways. Because the `NavigationTransition`\n   * class is not public, this event subject is not publicly exposed.\n   */\n  events = new Subject();\n  /**\n   * Used to abort the current transition with an error.\n   */\n  transitionAbortSubject = new Subject();\n  configLoader = inject(RouterConfigLoader);\n  environmentInjector = inject(EnvironmentInjector);\n  destroyRef = inject(DestroyRef);\n  urlSerializer = inject(UrlSerializer);\n  rootContexts = inject(ChildrenOutletContexts);\n  location = inject(Location);\n  inputBindingEnabled = inject(INPUT_BINDER, {\n    optional: true\n  }) !== null;\n  titleStrategy = inject(TitleStrategy);\n  options = inject(ROUTER_CONFIGURATION, {\n    optional: true\n  }) || {};\n  paramsInheritanceStrategy = this.options.paramsInheritanceStrategy || 'emptyOnly';\n  urlHandlingStrategy = inject(UrlHandlingStrategy);\n  createViewTransition = inject(CREATE_VIEW_TRANSITION, {\n    optional: true\n  });\n  navigationErrorHandler = inject(NAVIGATION_ERROR_HANDLER, {\n    optional: true\n  });\n  navigationId = 0;\n  get hasRequestedNavigation() {\n    return this.navigationId !== 0;\n  }\n  transitions;\n  /**\n   * Hook that enables you to pause navigation after the preactivation phase.\n   * Used by `RouterModule`.\n   *\n   * @internal\n   */\n  afterPreactivation = () => of(void 0);\n  /** @internal */\n  rootComponentType = null;\n  destroyed = false;\n  constructor() {\n    const onLoadStart = r => this.events.next(new RouteConfigLoadStart(r));\n    const onLoadEnd = r => this.events.next(new RouteConfigLoadEnd(r));\n    this.configLoader.onLoadEndListener = onLoadEnd;\n    this.configLoader.onLoadStartListener = onLoadStart;\n    this.destroyRef.onDestroy(() => {\n      this.destroyed = true;\n    });\n  }\n  complete() {\n    this.transitions?.complete();\n  }\n  handleNavigationRequest(request) {\n    const id = ++this.navigationId;\n    this.transitions?.next({\n      ...request,\n      extractedUrl: this.urlHandlingStrategy.extract(request.rawUrl),\n      targetSnapshot: null,\n      targetRouterState: null,\n      guards: {\n        canActivateChecks: [],\n        canDeactivateChecks: []\n      },\n      guardsResult: null,\n      id\n    });\n  }\n  setupNavigations(router) {\n    this.transitions = new BehaviorSubject(null);\n    return this.transitions.pipe(filter(t => t !== null),\n    // Using switchMap so we cancel executing navigations when a new one comes in\n    switchMap(overallTransitionState => {\n      let completed = false;\n      let errored = false;\n      return of(overallTransitionState).pipe(switchMap(t => {\n        // It is possible that `switchMap` fails to cancel previous navigations if a new one happens synchronously while the operator\n        // is processing the `next` notification of that previous navigation. This can happen when a new navigation (say 2) cancels a\n        // previous one (1) and yet another navigation (3) happens synchronously in response to the `NavigationCancel` event for (1).\n        // https://github.com/ReactiveX/rxjs/issues/7455\n        if (this.navigationId > overallTransitionState.id) {\n          const cancellationReason = typeof ngDevMode === 'undefined' || ngDevMode ? `Navigation ID ${overallTransitionState.id} is not equal to the current navigation id ${this.navigationId}` : '';\n          this.cancelNavigationTransition(overallTransitionState, cancellationReason, NavigationCancellationCode.SupersededByNewNavigation);\n          return EMPTY;\n        }\n        this.currentTransition = overallTransitionState;\n        // Store the Navigation object\n        this.currentNavigation = {\n          id: t.id,\n          initialUrl: t.rawUrl,\n          extractedUrl: t.extractedUrl,\n          targetBrowserUrl: typeof t.extras.browserUrl === 'string' ? this.urlSerializer.parse(t.extras.browserUrl) : t.extras.browserUrl,\n          trigger: t.source,\n          extras: t.extras,\n          previousNavigation: !this.lastSuccessfulNavigation ? null : {\n            ...this.lastSuccessfulNavigation,\n            previousNavigation: null\n          }\n        };\n        const urlTransition = !router.navigated || this.isUpdatingInternalState() || this.isUpdatedBrowserUrl();\n        const onSameUrlNavigation = t.extras.onSameUrlNavigation ?? router.onSameUrlNavigation;\n        if (!urlTransition && onSameUrlNavigation !== 'reload') {\n          const reason = typeof ngDevMode === 'undefined' || ngDevMode ? `Navigation to ${t.rawUrl} was ignored because it is the same as the current Router URL.` : '';\n          this.events.next(new NavigationSkipped(t.id, this.urlSerializer.serialize(t.rawUrl), reason, NavigationSkippedCode.IgnoredSameUrlNavigation));\n          t.resolve(false);\n          return EMPTY;\n        }\n        if (this.urlHandlingStrategy.shouldProcessUrl(t.rawUrl)) {\n          return of(t).pipe(\n          // Fire NavigationStart event\n          switchMap(t => {\n            this.events.next(new NavigationStart(t.id, this.urlSerializer.serialize(t.extractedUrl), t.source, t.restoredState));\n            if (t.id !== this.navigationId) {\n              return EMPTY;\n            }\n            // This delay is required to match old behavior that forced\n            // navigation to always be async\n            return Promise.resolve(t);\n          }),\n          // Recognize\n          recognize(this.environmentInjector, this.configLoader, this.rootComponentType, router.config, this.urlSerializer, this.paramsInheritanceStrategy),\n          // Update URL if in `eager` update mode\n          tap(t => {\n            overallTransitionState.targetSnapshot = t.targetSnapshot;\n            overallTransitionState.urlAfterRedirects = t.urlAfterRedirects;\n            this.currentNavigation = {\n              ...this.currentNavigation,\n              finalUrl: t.urlAfterRedirects\n            };\n            // Fire RoutesRecognized\n            const routesRecognized = new RoutesRecognized(t.id, this.urlSerializer.serialize(t.extractedUrl), this.urlSerializer.serialize(t.urlAfterRedirects), t.targetSnapshot);\n            this.events.next(routesRecognized);\n          }));\n        } else if (urlTransition && this.urlHandlingStrategy.shouldProcessUrl(t.currentRawUrl)) {\n          /* When the current URL shouldn't be processed, but the previous one\n           * was, we handle this \"error condition\" by navigating to the\n           * previously successful URL, but leaving the URL intact.*/\n          const {\n            id,\n            extractedUrl,\n            source,\n            restoredState,\n            extras\n          } = t;\n          const navStart = new NavigationStart(id, this.urlSerializer.serialize(extractedUrl), source, restoredState);\n          this.events.next(navStart);\n          const targetSnapshot = createEmptyState(this.rootComponentType).snapshot;\n          this.currentTransition = overallTransitionState = {\n            ...t,\n            targetSnapshot,\n            urlAfterRedirects: extractedUrl,\n            extras: {\n              ...extras,\n              skipLocationChange: false,\n              replaceUrl: false\n            }\n          };\n          this.currentNavigation.finalUrl = extractedUrl;\n          return of(overallTransitionState);\n        } else {\n          /* When neither the current or previous URL can be processed, do\n           * nothing other than update router's internal reference to the\n           * current \"settled\" URL. This way the next navigation will be coming\n           * from the current URL in the browser.\n           */\n          const reason = typeof ngDevMode === 'undefined' || ngDevMode ? `Navigation was ignored because the UrlHandlingStrategy` + ` indicated neither the current URL ${t.currentRawUrl} nor target URL ${t.rawUrl} should be processed.` : '';\n          this.events.next(new NavigationSkipped(t.id, this.urlSerializer.serialize(t.extractedUrl), reason, NavigationSkippedCode.IgnoredByUrlHandlingStrategy));\n          t.resolve(false);\n          return EMPTY;\n        }\n      }),\n      // --- GUARDS ---\n      tap(t => {\n        const guardsStart = new GuardsCheckStart(t.id, this.urlSerializer.serialize(t.extractedUrl), this.urlSerializer.serialize(t.urlAfterRedirects), t.targetSnapshot);\n        this.events.next(guardsStart);\n      }), map(t => {\n        this.currentTransition = overallTransitionState = {\n          ...t,\n          guards: getAllRouteGuards(t.targetSnapshot, t.currentSnapshot, this.rootContexts)\n        };\n        return overallTransitionState;\n      }), checkGuards(this.environmentInjector, evt => this.events.next(evt)), tap(t => {\n        overallTransitionState.guardsResult = t.guardsResult;\n        if (t.guardsResult && typeof t.guardsResult !== 'boolean') {\n          throw redirectingNavigationError(this.urlSerializer, t.guardsResult);\n        }\n        const guardsEnd = new GuardsCheckEnd(t.id, this.urlSerializer.serialize(t.extractedUrl), this.urlSerializer.serialize(t.urlAfterRedirects), t.targetSnapshot, !!t.guardsResult);\n        this.events.next(guardsEnd);\n      }), filter(t => {\n        if (!t.guardsResult) {\n          this.cancelNavigationTransition(t, '', NavigationCancellationCode.GuardRejected);\n          return false;\n        }\n        return true;\n      }),\n      // --- RESOLVE ---\n      switchTap(t => {\n        if (t.guards.canActivateChecks.length === 0) {\n          return undefined;\n        }\n        return of(t).pipe(tap(t => {\n          const resolveStart = new ResolveStart(t.id, this.urlSerializer.serialize(t.extractedUrl), this.urlSerializer.serialize(t.urlAfterRedirects), t.targetSnapshot);\n          this.events.next(resolveStart);\n        }), switchMap(t => {\n          let dataResolved = false;\n          return of(t).pipe(resolveData(this.paramsInheritanceStrategy, this.environmentInjector), tap({\n            next: () => dataResolved = true,\n            complete: () => {\n              if (!dataResolved) {\n                this.cancelNavigationTransition(t, typeof ngDevMode === 'undefined' || ngDevMode ? `At least one route resolver didn't emit any value.` : '', NavigationCancellationCode.NoDataFromResolver);\n              }\n            }\n          }));\n        }), tap(t => {\n          const resolveEnd = new ResolveEnd(t.id, this.urlSerializer.serialize(t.extractedUrl), this.urlSerializer.serialize(t.urlAfterRedirects), t.targetSnapshot);\n          this.events.next(resolveEnd);\n        }));\n      }),\n      // --- LOAD COMPONENTS ---\n      switchTap(t => {\n        const loadComponents = route => {\n          const loaders = [];\n          if (route.routeConfig?.loadComponent && !route.routeConfig._loadedComponent) {\n            loaders.push(this.configLoader.loadComponent(route.routeConfig).pipe(tap(loadedComponent => {\n              route.component = loadedComponent;\n            }), map(() => void 0)));\n          }\n          for (const child of route.children) {\n            loaders.push(...loadComponents(child));\n          }\n          return loaders;\n        };\n        return combineLatest(loadComponents(t.targetSnapshot.root)).pipe(defaultIfEmpty(null), take(1));\n      }), switchTap(() => this.afterPreactivation()), switchMap(() => {\n        const {\n          currentSnapshot,\n          targetSnapshot\n        } = overallTransitionState;\n        const viewTransitionStarted = this.createViewTransition?.(this.environmentInjector, currentSnapshot.root, targetSnapshot.root);\n        // If view transitions are enabled, block the navigation until the view\n        // transition callback starts. Otherwise, continue immediately.\n        return viewTransitionStarted ? from(viewTransitionStarted).pipe(map(() => overallTransitionState)) : of(overallTransitionState);\n      }), map(t => {\n        const targetRouterState = createRouterState(router.routeReuseStrategy, t.targetSnapshot, t.currentRouterState);\n        this.currentTransition = overallTransitionState = {\n          ...t,\n          targetRouterState\n        };\n        this.currentNavigation.targetRouterState = targetRouterState;\n        return overallTransitionState;\n      }), tap(() => {\n        this.events.next(new BeforeActivateRoutes());\n      }), activateRoutes(this.rootContexts, router.routeReuseStrategy, evt => this.events.next(evt), this.inputBindingEnabled),\n      // Ensure that if some observable used to drive the transition doesn't\n      // complete, the navigation still finalizes This should never happen, but\n      // this is done as a safety measure to avoid surfacing this error (#49567).\n      take(1), tap({\n        next: t => {\n          completed = true;\n          this.lastSuccessfulNavigation = this.currentNavigation;\n          this.events.next(new NavigationEnd(t.id, this.urlSerializer.serialize(t.extractedUrl), this.urlSerializer.serialize(t.urlAfterRedirects)));\n          this.titleStrategy?.updateTitle(t.targetRouterState.snapshot);\n          t.resolve(true);\n        },\n        complete: () => {\n          completed = true;\n        }\n      }),\n      // There used to be a lot more logic happening directly within the\n      // transition Observable. Some of this logic has been refactored out to\n      // other places but there may still be errors that happen there. This gives\n      // us a way to cancel the transition from the outside. This may also be\n      // required in the future to support something like the abort signal of the\n      // Navigation API where the navigation gets aborted from outside the\n      // transition.\n      takeUntil(this.transitionAbortSubject.pipe(tap(err => {\n        throw err;\n      }))), finalize(() => {\n        /* When the navigation stream finishes either through error or success,\n         * we set the `completed` or `errored` flag. However, there are some\n         * situations where we could get here without either of those being set.\n         * For instance, a redirect during NavigationStart. Therefore, this is a\n         * catch-all to make sure the NavigationCancel event is fired when a\n         * navigation gets cancelled but not caught by other means. */\n        if (!completed && !errored) {\n          const cancelationReason = typeof ngDevMode === 'undefined' || ngDevMode ? `Navigation ID ${overallTransitionState.id} is not equal to the current navigation id ${this.navigationId}` : '';\n          this.cancelNavigationTransition(overallTransitionState, cancelationReason, NavigationCancellationCode.SupersededByNewNavigation);\n        }\n        // Only clear current navigation if it is still set to the one that\n        // finalized.\n        if (this.currentTransition?.id === overallTransitionState.id) {\n          this.currentNavigation = null;\n          this.currentTransition = null;\n        }\n      }), catchError(e => {\n        // If the application is already destroyed, the catch block should not\n        // execute anything in practice because other resources have already\n        // been released and destroyed.\n        if (this.destroyed) {\n          overallTransitionState.resolve(false);\n          return EMPTY;\n        }\n        errored = true;\n        /* This error type is issued during Redirect, and is handled as a\n         * cancellation rather than an error. */\n        if (isNavigationCancelingError(e)) {\n          this.events.next(new NavigationCancel(overallTransitionState.id, this.urlSerializer.serialize(overallTransitionState.extractedUrl), e.message, e.cancellationCode));\n          // When redirecting, we need to delay resolving the navigation\n          // promise and push it to the redirect navigation\n          if (!isRedirectingNavigationCancelingError(e)) {\n            overallTransitionState.resolve(false);\n          } else {\n            this.events.next(new RedirectRequest(e.url, e.navigationBehaviorOptions));\n          }\n          /* All other errors should reset to the router's internal URL reference\n           * to the pre-error state. */\n        } else {\n          const navigationError = new NavigationError(overallTransitionState.id, this.urlSerializer.serialize(overallTransitionState.extractedUrl), e, overallTransitionState.targetSnapshot ?? undefined);\n          try {\n            const navigationErrorHandlerResult = runInInjectionContext(this.environmentInjector, () => this.navigationErrorHandler?.(navigationError));\n            if (navigationErrorHandlerResult instanceof RedirectCommand) {\n              const {\n                message,\n                cancellationCode\n              } = redirectingNavigationError(this.urlSerializer, navigationErrorHandlerResult);\n              this.events.next(new NavigationCancel(overallTransitionState.id, this.urlSerializer.serialize(overallTransitionState.extractedUrl), message, cancellationCode));\n              this.events.next(new RedirectRequest(navigationErrorHandlerResult.redirectTo, navigationErrorHandlerResult.navigationBehaviorOptions));\n            } else {\n              this.events.next(navigationError);\n              throw e;\n            }\n          } catch (ee) {\n            // TODO(atscott): consider flipping the default behavior of\n            // resolveNavigationPromiseOnError to be `resolve(false)` when\n            // undefined. This is the most sane thing to do given that\n            // applications very rarely handle the promise rejection and, as a\n            // result, would get \"unhandled promise rejection\" console logs.\n            // The vast majority of applications would not be affected by this\n            // change so omitting a migration seems reasonable. Instead,\n            // applications that rely on rejection can specifically opt-in to the\n            // old behavior.\n            if (this.options.resolveNavigationPromiseOnError) {\n              overallTransitionState.resolve(false);\n            } else {\n              overallTransitionState.reject(ee);\n            }\n          }\n        }\n        return EMPTY;\n      }));\n      // casting because `pipe` returns observable({}) when called with 8+ arguments\n    }));\n  }\n  cancelNavigationTransition(t, reason, code) {\n    const navCancel = new NavigationCancel(t.id, this.urlSerializer.serialize(t.extractedUrl), reason, code);\n    this.events.next(navCancel);\n    t.resolve(false);\n  }\n  /**\n   * @returns Whether we're navigating to somewhere that is not what the Router is\n   * currently set to.\n   */\n  isUpdatingInternalState() {\n    // TODO(atscott): The serializer should likely be used instead of\n    // `UrlTree.toString()`. Custom serializers are often written to handle\n    // things better than the default one (objects, for example will be\n    // [Object object] with the custom serializer and be \"the same\" when they\n    // aren't).\n    // (Same for isUpdatedBrowserUrl)\n    return this.currentTransition?.extractedUrl.toString() !== this.currentTransition?.currentUrlTree.toString();\n  }\n  /**\n   * @returns Whether we're updating the browser URL to something new (navigation is going\n   * to somewhere not displayed in the URL bar and we will update the URL\n   * bar if navigation succeeds).\n   */\n  isUpdatedBrowserUrl() {\n    // The extracted URL is the part of the URL that this application cares about. `extract` may\n    // return only part of the browser URL and that part may have not changed even if some other\n    // portion of the URL did.\n    const currentBrowserUrl = this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(true)));\n    const targetBrowserUrl = this.currentNavigation?.targetBrowserUrl ?? this.currentNavigation?.extractedUrl;\n    return currentBrowserUrl.toString() !== targetBrowserUrl?.toString() && !this.currentNavigation?.extras.skipLocationChange;\n  }\n  static ɵfac = function NavigationTransitions_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NavigationTransitions)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NavigationTransitions,\n    factory: NavigationTransitions.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NavigationTransitions, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nfunction isBrowserTriggeredNavigation(source) {\n  return source !== IMPERATIVE_NAVIGATION;\n}\n\n/**\n * @description\n *\n * Provides a way to customize when activated routes get reused.\n *\n * @publicApi\n */\nclass RouteReuseStrategy {\n  static ɵfac = function RouteReuseStrategy_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RouteReuseStrategy)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: RouteReuseStrategy,\n    factory: () => (() => inject(DefaultRouteReuseStrategy))(),\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouteReuseStrategy, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: () => inject(DefaultRouteReuseStrategy)\n    }]\n  }], null, null);\n})();\n/**\n * @description\n *\n * This base route reuse strategy only reuses routes when the matched router configs are\n * identical. This prevents components from being destroyed and recreated\n * when just the route parameters, query parameters or fragment change\n * (that is, the existing component is _reused_).\n *\n * This strategy does not store any routes for later reuse.\n *\n * Angular uses this strategy by default.\n *\n *\n * It can be used as a base class for custom route reuse strategies, i.e. you can create your own\n * class that extends the `BaseRouteReuseStrategy` one.\n * @publicApi\n */\nclass BaseRouteReuseStrategy {\n  /**\n   * Whether the given route should detach for later reuse.\n   * Always returns false for `BaseRouteReuseStrategy`.\n   * */\n  shouldDetach(route) {\n    return false;\n  }\n  /**\n   * A no-op; the route is never stored since this strategy never detaches routes for later re-use.\n   */\n  store(route, detachedTree) {}\n  /** Returns `false`, meaning the route (and its subtree) is never reattached */\n  shouldAttach(route) {\n    return false;\n  }\n  /** Returns `null` because this strategy does not store routes for later re-use. */\n  retrieve(route) {\n    return null;\n  }\n  /**\n   * Determines if a route should be reused.\n   * This strategy returns `true` when the future route config and current route config are\n   * identical.\n   */\n  shouldReuseRoute(future, curr) {\n    return future.routeConfig === curr.routeConfig;\n  }\n}\nclass DefaultRouteReuseStrategy extends BaseRouteReuseStrategy {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵDefaultRouteReuseStrategy_BaseFactory;\n    return function DefaultRouteReuseStrategy_Factory(__ngFactoryType__) {\n      return (ɵDefaultRouteReuseStrategy_BaseFactory || (ɵDefaultRouteReuseStrategy_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultRouteReuseStrategy)))(__ngFactoryType__ || DefaultRouteReuseStrategy);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DefaultRouteReuseStrategy,\n    factory: DefaultRouteReuseStrategy.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultRouteReuseStrategy, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass StateManager {\n  urlSerializer = inject(UrlSerializer);\n  options = inject(ROUTER_CONFIGURATION, {\n    optional: true\n  }) || {};\n  canceledNavigationResolution = this.options.canceledNavigationResolution || 'replace';\n  location = inject(Location);\n  urlHandlingStrategy = inject(UrlHandlingStrategy);\n  urlUpdateStrategy = this.options.urlUpdateStrategy || 'deferred';\n  currentUrlTree = new UrlTree();\n  /**\n   * Returns the currently activated `UrlTree`.\n   *\n   * This `UrlTree` shows only URLs that the `Router` is configured to handle (through\n   * `UrlHandlingStrategy`).\n   *\n   * The value is set after finding the route config tree to activate but before activating the\n   * route.\n   */\n  getCurrentUrlTree() {\n    return this.currentUrlTree;\n  }\n  rawUrlTree = this.currentUrlTree;\n  /**\n   * Returns a `UrlTree` that is represents what the browser is actually showing.\n   *\n   * In the life of a navigation transition:\n   * 1. When a navigation begins, the raw `UrlTree` is updated to the full URL that's being\n   * navigated to.\n   * 2. During a navigation, redirects are applied, which might only apply to _part_ of the URL (due\n   * to `UrlHandlingStrategy`).\n   * 3. Just before activation, the raw `UrlTree` is updated to include the redirects on top of the\n   * original raw URL.\n   *\n   * Note that this is _only_ here to support `UrlHandlingStrategy.extract` and\n   * `UrlHandlingStrategy.shouldProcessUrl`. Without those APIs, the current `UrlTree` would not\n   * deviated from the raw `UrlTree`.\n   *\n   * For `extract`, a raw `UrlTree` is needed because `extract` may only return part\n   * of the navigation URL. Thus, the current `UrlTree` may only represent _part_ of the browser\n   * URL. When a navigation gets cancelled and the router needs to reset the URL or a new navigation\n   * occurs, it needs to know the _whole_ browser URL, not just the part handled by\n   * `UrlHandlingStrategy`.\n   * For `shouldProcessUrl`, when the return is `false`, the router ignores the navigation but\n   * still updates the raw `UrlTree` with the assumption that the navigation was caused by the\n   * location change listener due to a URL update by the AngularJS router. In this case, the router\n   * still need to know what the browser's URL is for future navigations.\n   */\n  getRawUrlTree() {\n    return this.rawUrlTree;\n  }\n  createBrowserPath({\n    finalUrl,\n    initialUrl,\n    targetBrowserUrl\n  }) {\n    const rawUrl = finalUrl !== undefined ? this.urlHandlingStrategy.merge(finalUrl, initialUrl) : initialUrl;\n    const url = targetBrowserUrl ?? rawUrl;\n    const path = url instanceof UrlTree ? this.urlSerializer.serialize(url) : url;\n    return path;\n  }\n  commitTransition({\n    targetRouterState,\n    finalUrl,\n    initialUrl\n  }) {\n    // If we are committing the transition after having a final URL and target state, we're updating\n    // all pieces of the state. Otherwise, we likely skipped the transition (due to URL handling strategy)\n    // and only want to update the rawUrlTree, which represents the browser URL (and doesn't necessarily match router state).\n    if (finalUrl && targetRouterState) {\n      this.currentUrlTree = finalUrl;\n      this.rawUrlTree = this.urlHandlingStrategy.merge(finalUrl, initialUrl);\n      this.routerState = targetRouterState;\n    } else {\n      this.rawUrlTree = initialUrl;\n    }\n  }\n  routerState = createEmptyState(null);\n  /** Returns the current RouterState. */\n  getRouterState() {\n    return this.routerState;\n  }\n  stateMemento = this.createStateMemento();\n  updateStateMemento() {\n    this.stateMemento = this.createStateMemento();\n  }\n  createStateMemento() {\n    return {\n      rawUrlTree: this.rawUrlTree,\n      currentUrlTree: this.currentUrlTree,\n      routerState: this.routerState\n    };\n  }\n  resetInternalState({\n    finalUrl\n  }) {\n    this.routerState = this.stateMemento.routerState;\n    this.currentUrlTree = this.stateMemento.currentUrlTree;\n    // Note here that we use the urlHandlingStrategy to get the reset `rawUrlTree` because it may be\n    // configured to handle only part of the navigation URL. This means we would only want to reset\n    // the part of the navigation handled by the Angular router rather than the whole URL. In\n    // addition, the URLHandlingStrategy may be configured to specifically preserve parts of the URL\n    // when merging, such as the query params so they are not lost on a refresh.\n    this.rawUrlTree = this.urlHandlingStrategy.merge(this.currentUrlTree, finalUrl ?? this.rawUrlTree);\n  }\n  static ɵfac = function StateManager_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || StateManager)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: StateManager,\n    factory: () => (() => inject(HistoryStateManager))(),\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StateManager, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: () => inject(HistoryStateManager)\n    }]\n  }], null, null);\n})();\nclass HistoryStateManager extends StateManager {\n  /**\n   * The id of the currently active page in the router.\n   * Updated to the transition's target id on a successful navigation.\n   *\n   * This is used to track what page the router last activated. When an attempted navigation fails,\n   * the router can then use this to compute how to restore the state back to the previously active\n   * page.\n   */\n  currentPageId = 0;\n  lastSuccessfulId = -1;\n  restoredState() {\n    return this.location.getState();\n  }\n  /**\n   * The ɵrouterPageId of whatever page is currently active in the browser history. This is\n   * important for computing the target page id for new navigations because we need to ensure each\n   * page id in the browser history is 1 more than the previous entry.\n   */\n  get browserPageId() {\n    if (this.canceledNavigationResolution !== 'computed') {\n      return this.currentPageId;\n    }\n    return this.restoredState()?.ɵrouterPageId ?? this.currentPageId;\n  }\n  registerNonRouterCurrentEntryChangeListener(listener) {\n    return this.location.subscribe(event => {\n      if (event['type'] === 'popstate') {\n        // The `setTimeout` was added in #12160 and is likely to support Angular/AngularJS\n        // hybrid apps.\n        setTimeout(() => {\n          listener(event['url'], event.state, 'popstate');\n        });\n      }\n    });\n  }\n  handleRouterEvent(e, currentTransition) {\n    if (e instanceof NavigationStart) {\n      this.updateStateMemento();\n    } else if (e instanceof NavigationSkipped) {\n      this.commitTransition(currentTransition);\n    } else if (e instanceof RoutesRecognized) {\n      if (this.urlUpdateStrategy === 'eager') {\n        if (!currentTransition.extras.skipLocationChange) {\n          this.setBrowserUrl(this.createBrowserPath(currentTransition), currentTransition);\n        }\n      }\n    } else if (e instanceof BeforeActivateRoutes) {\n      this.commitTransition(currentTransition);\n      if (this.urlUpdateStrategy === 'deferred' && !currentTransition.extras.skipLocationChange) {\n        this.setBrowserUrl(this.createBrowserPath(currentTransition), currentTransition);\n      }\n    } else if (e instanceof NavigationCancel && (e.code === NavigationCancellationCode.GuardRejected || e.code === NavigationCancellationCode.NoDataFromResolver)) {\n      this.restoreHistory(currentTransition);\n    } else if (e instanceof NavigationError) {\n      this.restoreHistory(currentTransition, true);\n    } else if (e instanceof NavigationEnd) {\n      this.lastSuccessfulId = e.id;\n      this.currentPageId = this.browserPageId;\n    }\n  }\n  setBrowserUrl(path, {\n    extras,\n    id\n  }) {\n    const {\n      replaceUrl,\n      state\n    } = extras;\n    if (this.location.isCurrentPathEqualTo(path) || !!replaceUrl) {\n      // replacements do not update the target page\n      const currentBrowserPageId = this.browserPageId;\n      const newState = {\n        ...state,\n        ...this.generateNgRouterState(id, currentBrowserPageId)\n      };\n      this.location.replaceState(path, '', newState);\n    } else {\n      const newState = {\n        ...state,\n        ...this.generateNgRouterState(id, this.browserPageId + 1)\n      };\n      this.location.go(path, '', newState);\n    }\n  }\n  /**\n   * Performs the necessary rollback action to restore the browser URL to the\n   * state before the transition.\n   */\n  restoreHistory(navigation, restoringFromCaughtError = false) {\n    if (this.canceledNavigationResolution === 'computed') {\n      const currentBrowserPageId = this.browserPageId;\n      const targetPagePosition = this.currentPageId - currentBrowserPageId;\n      if (targetPagePosition !== 0) {\n        this.location.historyGo(targetPagePosition);\n      } else if (this.getCurrentUrlTree() === navigation.finalUrl && targetPagePosition === 0) {\n        // We got to the activation stage (where currentUrlTree is set to the navigation's\n        // finalUrl), but we weren't moving anywhere in history (skipLocationChange or replaceUrl).\n        // We still need to reset the router state back to what it was when the navigation started.\n        this.resetInternalState(navigation);\n        this.resetUrlToCurrentUrlTree();\n      } else ;\n    } else if (this.canceledNavigationResolution === 'replace') {\n      // TODO(atscott): It seems like we should _always_ reset the state here. It would be a no-op\n      // for `deferred` navigations that haven't change the internal state yet because guards\n      // reject. For 'eager' navigations, it seems like we also really should reset the state\n      // because the navigation was cancelled. Investigate if this can be done by running TGP.\n      if (restoringFromCaughtError) {\n        this.resetInternalState(navigation);\n      }\n      this.resetUrlToCurrentUrlTree();\n    }\n  }\n  resetUrlToCurrentUrlTree() {\n    this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()), '', this.generateNgRouterState(this.lastSuccessfulId, this.currentPageId));\n  }\n  generateNgRouterState(navigationId, routerPageId) {\n    if (this.canceledNavigationResolution === 'computed') {\n      return {\n        navigationId,\n        ɵrouterPageId: routerPageId\n      };\n    }\n    return {\n      navigationId\n    };\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵHistoryStateManager_BaseFactory;\n    return function HistoryStateManager_Factory(__ngFactoryType__) {\n      return (ɵHistoryStateManager_BaseFactory || (ɵHistoryStateManager_BaseFactory = i0.ɵɵgetInheritedFactory(HistoryStateManager)))(__ngFactoryType__ || HistoryStateManager);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HistoryStateManager,\n    factory: HistoryStateManager.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HistoryStateManager, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Performs the given action once the router finishes its next/current navigation.\n *\n * The navigation is considered complete under the following conditions:\n * - `NavigationCancel` event emits and the code is not `NavigationCancellationCode.Redirect` or\n * `NavigationCancellationCode.SupersededByNewNavigation`. In these cases, the\n * redirecting/superseding navigation must finish.\n * - `NavigationError`, `NavigationEnd`, or `NavigationSkipped` event emits\n */\nfunction afterNextNavigation(router, action) {\n  router.events.pipe(filter(e => e instanceof NavigationEnd || e instanceof NavigationCancel || e instanceof NavigationError || e instanceof NavigationSkipped), map(e => {\n    if (e instanceof NavigationEnd || e instanceof NavigationSkipped) {\n      return 0 /* NavigationResult.COMPLETE */;\n    }\n    const redirecting = e instanceof NavigationCancel ? e.code === NavigationCancellationCode.Redirect || e.code === NavigationCancellationCode.SupersededByNewNavigation : false;\n    return redirecting ? 2 /* NavigationResult.REDIRECTING */ : 1 /* NavigationResult.FAILED */;\n  }), filter(result => result !== 2 /* NavigationResult.REDIRECTING */), take(1)).subscribe(() => {\n    action();\n  });\n}\n\n/**\n * The equivalent `IsActiveMatchOptions` options for `Router.isActive` is called with `true`\n * (exact = true).\n */\nconst exactMatchOptions = {\n  paths: 'exact',\n  fragment: 'ignored',\n  matrixParams: 'ignored',\n  queryParams: 'exact'\n};\n/**\n * The equivalent `IsActiveMatchOptions` options for `Router.isActive` is called with `false`\n * (exact = false).\n */\nconst subsetMatchOptions = {\n  paths: 'subset',\n  fragment: 'ignored',\n  matrixParams: 'ignored',\n  queryParams: 'subset'\n};\n/**\n * @description\n *\n * A service that facilitates navigation among views and URL manipulation capabilities.\n * This service is provided in the root scope and configured with [provideRouter](api/router/provideRouter).\n *\n * @see {@link Route}\n * @see {@link provideRouter}\n * @see [Routing and Navigation Guide](guide/routing/common-router-tasks).\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\nclass Router {\n  get currentUrlTree() {\n    return this.stateManager.getCurrentUrlTree();\n  }\n  get rawUrlTree() {\n    return this.stateManager.getRawUrlTree();\n  }\n  disposed = false;\n  nonRouterCurrentEntryChangeSubscription;\n  console = inject(_Console);\n  stateManager = inject(StateManager);\n  options = inject(ROUTER_CONFIGURATION, {\n    optional: true\n  }) || {};\n  pendingTasks = inject(_PendingTasksInternal);\n  urlUpdateStrategy = this.options.urlUpdateStrategy || 'deferred';\n  navigationTransitions = inject(NavigationTransitions);\n  urlSerializer = inject(UrlSerializer);\n  location = inject(Location);\n  urlHandlingStrategy = inject(UrlHandlingStrategy);\n  /**\n   * The private `Subject` type for the public events exposed in the getter. This is used internally\n   * to push events to. The separate field allows us to expose separate types in the public API\n   * (i.e., an Observable rather than the Subject).\n   */\n  _events = new Subject();\n  /**\n   * An event stream for routing events.\n   */\n  get events() {\n    // TODO(atscott): This _should_ be events.asObservable(). However, this change requires internal\n    // cleanup: tests are doing `(route.events as Subject<Event>).next(...)`. This isn't\n    // allowed/supported but we still have to fix these or file bugs against the teams before making\n    // the change.\n    return this._events;\n  }\n  /**\n   * The current state of routing in this NgModule.\n   */\n  get routerState() {\n    return this.stateManager.getRouterState();\n  }\n  /**\n   * True if at least one navigation event has occurred,\n   * false otherwise.\n   */\n  navigated = false;\n  /**\n   * A strategy for re-using routes.\n   *\n   * @deprecated Configure using `providers` instead:\n   *   `{provide: RouteReuseStrategy, useClass: MyStrategy}`.\n   */\n  routeReuseStrategy = inject(RouteReuseStrategy);\n  /**\n   * How to handle a navigation request to the current URL.\n   *\n   *\n   * @deprecated Configure this through `provideRouter` or `RouterModule.forRoot` instead.\n   * @see {@link withRouterConfig}\n   * @see {@link provideRouter}\n   * @see {@link RouterModule}\n   */\n  onSameUrlNavigation = this.options.onSameUrlNavigation || 'ignore';\n  config = inject(ROUTES, {\n    optional: true\n  })?.flat() ?? [];\n  /**\n   * Indicates whether the application has opted in to binding Router data to component inputs.\n   *\n   * This option is enabled by the `withComponentInputBinding` feature of `provideRouter` or\n   * `bindToComponentInputs` in the `ExtraOptions` of `RouterModule.forRoot`.\n   */\n  componentInputBindingEnabled = !!inject(INPUT_BINDER, {\n    optional: true\n  });\n  constructor() {\n    this.resetConfig(this.config);\n    this.navigationTransitions.setupNavigations(this).subscribe({\n      error: e => {\n        this.console.warn(ngDevMode ? `Unhandled Navigation Error: ${e}` : e);\n      }\n    });\n    this.subscribeToNavigationEvents();\n  }\n  eventsSubscription = new Subscription();\n  subscribeToNavigationEvents() {\n    const subscription = this.navigationTransitions.events.subscribe(e => {\n      try {\n        const currentTransition = this.navigationTransitions.currentTransition;\n        const currentNavigation = this.navigationTransitions.currentNavigation;\n        if (currentTransition !== null && currentNavigation !== null) {\n          this.stateManager.handleRouterEvent(e, currentNavigation);\n          if (e instanceof NavigationCancel && e.code !== NavigationCancellationCode.Redirect && e.code !== NavigationCancellationCode.SupersededByNewNavigation) {\n            // It seems weird that `navigated` is set to `true` when the navigation is rejected,\n            // however it's how things were written initially. Investigation would need to be done\n            // to determine if this can be removed.\n            this.navigated = true;\n          } else if (e instanceof NavigationEnd) {\n            this.navigated = true;\n          } else if (e instanceof RedirectRequest) {\n            const opts = e.navigationBehaviorOptions;\n            const mergedTree = this.urlHandlingStrategy.merge(e.url, currentTransition.currentRawUrl);\n            const extras = {\n              browserUrl: currentTransition.extras.browserUrl,\n              info: currentTransition.extras.info,\n              skipLocationChange: currentTransition.extras.skipLocationChange,\n              // The URL is already updated at this point if we have 'eager' URL\n              // updates or if the navigation was triggered by the browser (back\n              // button, URL bar, etc). We want to replace that item in history\n              // if the navigation is rejected.\n              replaceUrl: currentTransition.extras.replaceUrl || this.urlUpdateStrategy === 'eager' || isBrowserTriggeredNavigation(currentTransition.source),\n              // allow developer to override default options with RedirectCommand\n              ...opts\n            };\n            this.scheduleNavigation(mergedTree, IMPERATIVE_NAVIGATION, null, extras, {\n              resolve: currentTransition.resolve,\n              reject: currentTransition.reject,\n              promise: currentTransition.promise\n            });\n          }\n        }\n        // Note that it's important to have the Router process the events _before_ the event is\n        // pushed through the public observable. This ensures the correct router state is in place\n        // before applications observe the events.\n        if (isPublicRouterEvent(e)) {\n          this._events.next(e);\n        }\n      } catch (e) {\n        this.navigationTransitions.transitionAbortSubject.next(e);\n      }\n    });\n    this.eventsSubscription.add(subscription);\n  }\n  /** @internal */\n  resetRootComponentType(rootComponentType) {\n    // TODO: vsavkin router 4.0 should make the root component set to null\n    // this will simplify the lifecycle of the router.\n    this.routerState.root.component = rootComponentType;\n    this.navigationTransitions.rootComponentType = rootComponentType;\n  }\n  /**\n   * Sets up the location change listener and performs the initial navigation.\n   */\n  initialNavigation() {\n    this.setUpLocationChangeListener();\n    if (!this.navigationTransitions.hasRequestedNavigation) {\n      this.navigateToSyncWithBrowser(this.location.path(true), IMPERATIVE_NAVIGATION, this.stateManager.restoredState());\n    }\n  }\n  /**\n   * Sets up the location change listener. This listener detects navigations triggered from outside\n   * the Router (the browser back/forward buttons, for example) and schedules a corresponding Router\n   * navigation so that the correct events, guards, etc. are triggered.\n   */\n  setUpLocationChangeListener() {\n    // Don't need to use Zone.wrap any more, because zone.js\n    // already patch onPopState, so location change callback will\n    // run into ngZone\n    this.nonRouterCurrentEntryChangeSubscription ??= this.stateManager.registerNonRouterCurrentEntryChangeListener((url, state, source) => {\n      this.navigateToSyncWithBrowser(url, source, state);\n    });\n  }\n  /**\n   * Schedules a router navigation to synchronize Router state with the browser state.\n   *\n   * This is done as a response to a popstate event and the initial navigation. These\n   * two scenarios represent times when the browser URL/state has been updated and\n   * the Router needs to respond to ensure its internal state matches.\n   */\n  navigateToSyncWithBrowser(url, source, state) {\n    const extras = {\n      replaceUrl: true\n    };\n    // TODO: restoredState should always include the entire state, regardless\n    // of navigationId. This requires a breaking change to update the type on\n    // NavigationStart’s restoredState, which currently requires navigationId\n    // to always be present. The Router used to only restore history state if\n    // a navigationId was present.\n    // The stored navigationId is used by the RouterScroller to retrieve the scroll\n    // position for the page.\n    const restoredState = state?.navigationId ? state : null;\n    // Separate to NavigationStart.restoredState, we must also restore the state to\n    // history.state and generate a new navigationId, since it will be overwritten\n    if (state) {\n      const stateCopy = {\n        ...state\n      };\n      delete stateCopy.navigationId;\n      delete stateCopy.ɵrouterPageId;\n      if (Object.keys(stateCopy).length !== 0) {\n        extras.state = stateCopy;\n      }\n    }\n    const urlTree = this.parseUrl(url);\n    this.scheduleNavigation(urlTree, source, restoredState, extras);\n  }\n  /** The current URL. */\n  get url() {\n    return this.serializeUrl(this.currentUrlTree);\n  }\n  /**\n   * Returns the current `Navigation` object when the router is navigating,\n   * and `null` when idle.\n   */\n  getCurrentNavigation() {\n    return this.navigationTransitions.currentNavigation;\n  }\n  /**\n   * The `Navigation` object of the most recent navigation to succeed and `null` if there\n   *     has not been a successful navigation yet.\n   */\n  get lastSuccessfulNavigation() {\n    return this.navigationTransitions.lastSuccessfulNavigation;\n  }\n  /**\n   * Resets the route configuration used for navigation and generating links.\n   *\n   * @param config The route array for the new configuration.\n   *\n   * @usageNotes\n   *\n   * ```ts\n   * router.resetConfig([\n   *  { path: 'team/:id', component: TeamCmp, children: [\n   *    { path: 'simple', component: SimpleCmp },\n   *    { path: 'user/:name', component: UserCmp }\n   *  ]}\n   * ]);\n   * ```\n   */\n  resetConfig(config) {\n    (typeof ngDevMode === 'undefined' || ngDevMode) && validateConfig(config);\n    this.config = config.map(standardizeConfig);\n    this.navigated = false;\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    this.dispose();\n  }\n  /** Disposes of the router. */\n  dispose() {\n    // We call `unsubscribe()` to release observers, as users may forget to\n    // unsubscribe manually when subscribing to `router.events`. We do not call\n    // `complete()` because it is unsafe; if someone subscribes using the `first`\n    // operator and the observable completes before emitting a value,\n    // RxJS will throw an error.\n    this._events.unsubscribe();\n    this.navigationTransitions.complete();\n    if (this.nonRouterCurrentEntryChangeSubscription) {\n      this.nonRouterCurrentEntryChangeSubscription.unsubscribe();\n      this.nonRouterCurrentEntryChangeSubscription = undefined;\n    }\n    this.disposed = true;\n    this.eventsSubscription.unsubscribe();\n  }\n  /**\n   * Appends URL segments to the current URL tree to create a new URL tree.\n   *\n   * @param commands An array of URL fragments with which to construct the new URL tree.\n   * If the path is static, can be the literal URL string. For a dynamic path, pass an array of path\n   * segments, followed by the parameters for each segment.\n   * The fragments are applied to the current URL tree or the one provided  in the `relativeTo`\n   * property of the options object, if supplied.\n   * @param navigationExtras Options that control the navigation strategy.\n   * @returns The new URL tree.\n   *\n   * @usageNotes\n   *\n   * ```\n   * // create /team/33/user/11\n   * router.createUrlTree(['/team', 33, 'user', 11]);\n   *\n   * // create /team/33;expand=true/user/11\n   * router.createUrlTree(['/team', 33, {expand: true}, 'user', 11]);\n   *\n   * // you can collapse static segments like this (this works only with the first passed-in value):\n   * router.createUrlTree(['/team/33/user', userId]);\n   *\n   * // If the first segment can contain slashes, and you do not want the router to split it,\n   * // you can do the following:\n   * router.createUrlTree([{segmentPath: '/one/two'}]);\n   *\n   * // create /team/33/(user/11//right:chat)\n   * router.createUrlTree(['/team', 33, {outlets: {primary: 'user/11', right: 'chat'}}]);\n   *\n   * // remove the right secondary node\n   * router.createUrlTree(['/team', 33, {outlets: {primary: 'user/11', right: null}}]);\n   *\n   * // assuming the current url is `/team/33/user/11` and the route points to `user/11`\n   *\n   * // navigate to /team/33/user/11/details\n   * router.createUrlTree(['details'], {relativeTo: route});\n   *\n   * // navigate to /team/33/user/22\n   * router.createUrlTree(['../22'], {relativeTo: route});\n   *\n   * // navigate to /team/44/user/22\n   * router.createUrlTree(['../../team/44/user/22'], {relativeTo: route});\n   *\n   * Note that a value of `null` or `undefined` for `relativeTo` indicates that the\n   * tree should be created relative to the root.\n   * ```\n   */\n  createUrlTree(commands, navigationExtras = {}) {\n    const {\n      relativeTo,\n      queryParams,\n      fragment,\n      queryParamsHandling,\n      preserveFragment\n    } = navigationExtras;\n    const f = preserveFragment ? this.currentUrlTree.fragment : fragment;\n    let q = null;\n    switch (queryParamsHandling ?? this.options.defaultQueryParamsHandling) {\n      case 'merge':\n        q = {\n          ...this.currentUrlTree.queryParams,\n          ...queryParams\n        };\n        break;\n      case 'preserve':\n        q = this.currentUrlTree.queryParams;\n        break;\n      default:\n        q = queryParams || null;\n    }\n    if (q !== null) {\n      q = this.removeEmptyProps(q);\n    }\n    let relativeToUrlSegmentGroup;\n    try {\n      const relativeToSnapshot = relativeTo ? relativeTo.snapshot : this.routerState.snapshot.root;\n      relativeToUrlSegmentGroup = createSegmentGroupFromRoute(relativeToSnapshot);\n    } catch (e) {\n      // This is strictly for backwards compatibility with tests that create\n      // invalid `ActivatedRoute` mocks.\n      // Note: the difference between having this fallback for invalid `ActivatedRoute` setups and\n      // just throwing is ~500 test failures. Fixing all of those tests by hand is not feasible at\n      // the moment.\n      if (typeof commands[0] !== 'string' || commands[0][0] !== '/') {\n        // Navigations that were absolute in the old way of creating UrlTrees\n        // would still work because they wouldn't attempt to match the\n        // segments in the `ActivatedRoute` to the `currentUrlTree` but\n        // instead just replace the root segment with the navigation result.\n        // Non-absolute navigations would fail to apply the commands because\n        // the logic could not find the segment to replace (so they'd act like there were no\n        // commands).\n        commands = [];\n      }\n      relativeToUrlSegmentGroup = this.currentUrlTree.root;\n    }\n    return createUrlTreeFromSegmentGroup(relativeToUrlSegmentGroup, commands, q, f ?? null);\n  }\n  /**\n   * Navigates to a view using an absolute route path.\n   *\n   * @param url An absolute path for a defined route. The function does not apply any delta to the\n   *     current URL.\n   * @param extras An object containing properties that modify the navigation strategy.\n   *\n   * @returns A Promise that resolves to 'true' when navigation succeeds,\n   * to 'false' when navigation fails, or is rejected on error.\n   *\n   * @usageNotes\n   *\n   * The following calls request navigation to an absolute path.\n   *\n   * ```ts\n   * router.navigateByUrl(\"/team/33/user/11\");\n   *\n   * // Navigate without updating the URL\n   * router.navigateByUrl(\"/team/33/user/11\", { skipLocationChange: true });\n   * ```\n   *\n   * @see [Routing and Navigation guide](guide/routing/common-router-tasks)\n   *\n   */\n  navigateByUrl(url, extras = {\n    skipLocationChange: false\n  }) {\n    const urlTree = isUrlTree(url) ? url : this.parseUrl(url);\n    const mergedTree = this.urlHandlingStrategy.merge(urlTree, this.rawUrlTree);\n    return this.scheduleNavigation(mergedTree, IMPERATIVE_NAVIGATION, null, extras);\n  }\n  /**\n   * Navigate based on the provided array of commands and a starting point.\n   * If no starting route is provided, the navigation is absolute.\n   *\n   * @param commands An array of URL fragments with which to construct the target URL.\n   * If the path is static, can be the literal URL string. For a dynamic path, pass an array of path\n   * segments, followed by the parameters for each segment.\n   * The fragments are applied to the current URL or the one provided  in the `relativeTo` property\n   * of the options object, if supplied.\n   * @param extras An options object that determines how the URL should be constructed or\n   *     interpreted.\n   *\n   * @returns A Promise that resolves to `true` when navigation succeeds, or `false` when navigation\n   *     fails. The Promise is rejected when an error occurs if `resolveNavigationPromiseOnError` is\n   * not `true`.\n   *\n   * @usageNotes\n   *\n   * The following calls request navigation to a dynamic route path relative to the current URL.\n   *\n   * ```ts\n   * router.navigate(['team', 33, 'user', 11], {relativeTo: route});\n   *\n   * // Navigate without updating the URL, overriding the default behavior\n   * router.navigate(['team', 33, 'user', 11], {relativeTo: route, skipLocationChange: true});\n   * ```\n   *\n   * @see [Routing and Navigation guide](guide/routing/common-router-tasks)\n   *\n   */\n  navigate(commands, extras = {\n    skipLocationChange: false\n  }) {\n    validateCommands(commands);\n    return this.navigateByUrl(this.createUrlTree(commands, extras), extras);\n  }\n  /** Serializes a `UrlTree` into a string */\n  serializeUrl(url) {\n    return this.urlSerializer.serialize(url);\n  }\n  /** Parses a string into a `UrlTree` */\n  parseUrl(url) {\n    try {\n      return this.urlSerializer.parse(url);\n    } catch {\n      return this.urlSerializer.parse('/');\n    }\n  }\n  isActive(url, matchOptions) {\n    let options;\n    if (matchOptions === true) {\n      options = {\n        ...exactMatchOptions\n      };\n    } else if (matchOptions === false) {\n      options = {\n        ...subsetMatchOptions\n      };\n    } else {\n      options = matchOptions;\n    }\n    if (isUrlTree(url)) {\n      return containsTree(this.currentUrlTree, url, options);\n    }\n    const urlTree = this.parseUrl(url);\n    return containsTree(this.currentUrlTree, urlTree, options);\n  }\n  removeEmptyProps(params) {\n    return Object.entries(params).reduce((result, [key, value]) => {\n      if (value !== null && value !== undefined) {\n        result[key] = value;\n      }\n      return result;\n    }, {});\n  }\n  scheduleNavigation(rawUrl, source, restoredState, extras, priorPromise) {\n    if (this.disposed) {\n      return Promise.resolve(false);\n    }\n    let resolve;\n    let reject;\n    let promise;\n    if (priorPromise) {\n      resolve = priorPromise.resolve;\n      reject = priorPromise.reject;\n      promise = priorPromise.promise;\n    } else {\n      promise = new Promise((res, rej) => {\n        resolve = res;\n        reject = rej;\n      });\n    }\n    // Indicate that the navigation is happening.\n    const taskId = this.pendingTasks.add();\n    afterNextNavigation(this, () => {\n      // Remove pending task in a microtask to allow for cancelled\n      // initial navigations and redirects within the same task.\n      queueMicrotask(() => this.pendingTasks.remove(taskId));\n    });\n    this.navigationTransitions.handleNavigationRequest({\n      source,\n      restoredState,\n      currentUrlTree: this.currentUrlTree,\n      currentRawUrl: this.currentUrlTree,\n      rawUrl,\n      extras,\n      resolve: resolve,\n      reject: reject,\n      promise,\n      currentSnapshot: this.routerState.snapshot,\n      currentRouterState: this.routerState\n    });\n    // Make sure that the error is propagated even though `processNavigations` catch\n    // handler does not rethrow\n    return promise.catch(e => {\n      return Promise.reject(e);\n    });\n  }\n  static ɵfac = function Router_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Router)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Router,\n    factory: Router.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Router, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nfunction validateCommands(commands) {\n  for (let i = 0; i < commands.length; i++) {\n    const cmd = commands[i];\n    if (cmd == null) {\n      throw new _RuntimeError(4008 /* RuntimeErrorCode.NULLISH_COMMAND */, (typeof ngDevMode === 'undefined' || ngDevMode) && `The requested path contains ${cmd} segment at index ${i}`);\n    }\n  }\n}\nfunction isPublicRouterEvent(e) {\n  return !(e instanceof BeforeActivateRoutes) && !(e instanceof RedirectRequest);\n}\nexport { ActivatedRoute, ActivatedRouteSnapshot, ActivationEnd, ActivationStart, BaseRouteReuseStrategy, CREATE_VIEW_TRANSITION, ChildActivationEnd, ChildActivationStart, ChildrenOutletContexts, DefaultTitleStrategy, DefaultUrlSerializer, EventType, GuardsCheckEnd, GuardsCheckStart, INPUT_BINDER, NAVIGATION_ERROR_HANDLER, NavigationCancel, NavigationCancellationCode, NavigationEnd, NavigationError, NavigationSkipped, NavigationSkippedCode, NavigationStart, NavigationTransitions, OutletContext, PRIMARY_OUTLET, ROUTER_CONFIGURATION, ROUTER_OUTLET_DATA, ROUTES, RedirectCommand, ResolveEnd, ResolveStart, RouteConfigLoadEnd, RouteConfigLoadStart, RouteReuseStrategy, RoutedComponentInputBinder, Router, RouterConfigLoader, RouterEvent, RouterOutlet, RouterState, RouterStateSnapshot, RoutesRecognized, Scroll, TitleStrategy, UrlHandlingStrategy, UrlSegment, UrlSegmentGroup, UrlSerializer, UrlTree, VIEW_TRANSITION_OPTIONS, afterNextNavigation, convertToParamMap, createUrlTreeFromSnapshot, createViewTransition, defaultUrlMatcher, isUrlTree, loadChildren, stringifyEvent, ɵEmptyOutletComponent };\n", "/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i3 from '@angular/common';\nimport { LOCATION_INITIALIZED, HashLocationStrategy, LocationStrategy, ViewportScroller, Location, PathLocationStrategy } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { ɵRuntimeError as _RuntimeError, ɵɵsanitizeUrlOrResourceUrl as __sanitizeUrlOrResourceUrl, booleanAttribute, HostListener, Input, HostBinding, Attribute, Directive, EventEmitter, Output, ContentChildren, Optional, createEnvironmentInjector, Injectable, InjectionToken, ɵperformanceMarkFeature as _performanceMarkFeature, makeEnvironmentProviders, APP_BOOTSTRAP_LISTENER, ENVIRONMENT_INITIALIZER, provideAppInitializer, inject, Injector, ApplicationRef, InjectFlags, NgZone, SkipSelf, NgModule } from '@angular/core';\nimport { NavigationEnd, isUrlTree, Router, ActivatedRoute, RouterConfigLoader, NavigationStart, NavigationSkipped, NavigationSkippedCode, Scroll, UrlSerializer, NavigationTransitions, ROUTES, afterNextNavigation, ROUTER_CONFIGURATION, NAVIGATION_ERROR_HANDLER, RoutedComponentInputBinder, INPUT_BINDER, createViewTransition, CREATE_VIEW_TRANSITION, VIEW_TRANSITION_OPTIONS, stringifyEvent, DefaultUrlSerializer, ChildrenOutletContexts, RouterOutlet, ɵEmptyOutletComponent as _EmptyOutletComponent } from './router-Dwfin5Au.mjs';\nimport { Subject, of, from } from 'rxjs';\nimport { mergeAll, catchError, filter, concatMap, mergeMap } from 'rxjs/operators';\n\n/**\n * @description\n *\n * When applied to an element in a template, makes that element a link\n * that initiates navigation to a route. Navigation opens one or more routed components\n * in one or more `<router-outlet>` locations on the page.\n *\n * Given a route configuration `[{ path: 'user/:name', component: UserCmp }]`,\n * the following creates a static link to the route:\n * `<a routerLink=\"/user/bob\">link to user component</a>`\n *\n * You can use dynamic values to generate the link.\n * For a dynamic link, pass an array of path segments,\n * followed by the params for each segment.\n * For example, `['/team', teamId, 'user', userName, {details: true}]`\n * generates a link to `/team/11/user/bob;details=true`.\n *\n * Multiple static segments can be merged into one term and combined with dynamic segments.\n * For example, `['/team/11/user', userName, {details: true}]`\n *\n * The input that you provide to the link is treated as a delta to the current URL.\n * For instance, suppose the current URL is `/user/(box//aux:team)`.\n * The link `<a [routerLink]=\"['/user/jim']\">Jim</a>` creates the URL\n * `/user/(jim//aux:team)`.\n * See {@link Router#createUrlTree} for more information.\n *\n * @usageNotes\n *\n * You can use absolute or relative paths in a link, set query parameters,\n * control how parameters are handled, and keep a history of navigation states.\n *\n * ### Relative link paths\n *\n * The first segment name can be prepended with `/`, `./`, or `../`.\n * * If the first segment begins with `/`, the router looks up the route from the root of the\n *   app.\n * * If the first segment begins with `./`, or doesn't begin with a slash, the router\n *   looks in the children of the current activated route.\n * * If the first segment begins with `../`, the router goes up one level in the route tree.\n *\n * ### Setting and handling query params and fragments\n *\n * The following link adds a query parameter and a fragment to the generated URL:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" fragment=\"education\">\n *   link to user component\n * </a>\n * ```\n * By default, the directive constructs the new URL using the given query parameters.\n * The example generates the link: `/user/bob?debug=true#education`.\n *\n * You can instruct the directive to handle query parameters differently\n * by specifying the `queryParamsHandling` option in the link.\n * Allowed values are:\n *\n *  - `'merge'`: Merge the given `queryParams` into the current query params.\n *  - `'preserve'`: Preserve the current query params.\n *\n * For example:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" queryParamsHandling=\"merge\">\n *   link to user component\n * </a>\n * ```\n *\n * `queryParams`, `fragment`, `queryParamsHandling`, `preserveFragment`, and `relativeTo`\n * cannot be used when the `routerLink` input is a `UrlTree`.\n *\n * See {@link UrlCreationOptions#queryParamsHandling}.\n *\n * ### Preserving navigation history\n *\n * You can provide a `state` value to be persisted to the browser's\n * [`History.state` property](https://developer.mozilla.org/en-US/docs/Web/API/History#Properties).\n * For example:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [state]=\"{tracingId: 123}\">\n *   link to user component\n * </a>\n * ```\n *\n * Use {@link Router#getCurrentNavigation} to retrieve a saved\n * navigation-state value. For example, to capture the `tracingId` during the `NavigationStart`\n * event:\n *\n * ```ts\n * // Get NavigationStart events\n * router.events.pipe(filter(e => e instanceof NavigationStart)).subscribe(e => {\n *   const navigation = router.getCurrentNavigation();\n *   tracingService.trace({id: navigation.extras.state.tracingId});\n * });\n * ```\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\nclass RouterLink {\n  router;\n  route;\n  tabIndexAttribute;\n  renderer;\n  el;\n  locationStrategy;\n  /**\n   * Represents an `href` attribute value applied to a host element,\n   * when a host element is `<a>`. For other tags, the value is `null`.\n   */\n  href = null;\n  /**\n   * Represents the `target` attribute on a host element.\n   * This is only used when the host element is an `<a>` tag.\n   */\n  target;\n  /**\n   * Passed to {@link Router#createUrlTree} as part of the\n   * `UrlCreationOptions`.\n   * @see {@link UrlCreationOptions#queryParams}\n   * @see {@link Router#createUrlTree}\n   */\n  queryParams;\n  /**\n   * Passed to {@link Router#createUrlTree} as part of the\n   * `UrlCreationOptions`.\n   * @see {@link UrlCreationOptions#fragment}\n   * @see {@link Router#createUrlTree}\n   */\n  fragment;\n  /**\n   * Passed to {@link Router#createUrlTree} as part of the\n   * `UrlCreationOptions`.\n   * @see {@link UrlCreationOptions#queryParamsHandling}\n   * @see {@link Router#createUrlTree}\n   */\n  queryParamsHandling;\n  /**\n   * Passed to {@link Router#navigateByUrl} as part of the\n   * `NavigationBehaviorOptions`.\n   * @see {@link NavigationBehaviorOptions#state}\n   * @see {@link Router#navigateByUrl}\n   */\n  state;\n  /**\n   * Passed to {@link Router#navigateByUrl} as part of the\n   * `NavigationBehaviorOptions`.\n   * @see {@link NavigationBehaviorOptions#info}\n   * @see {@link Router#navigateByUrl}\n   */\n  info;\n  /**\n   * Passed to {@link Router#createUrlTree} as part of the\n   * `UrlCreationOptions`.\n   * Specify a value here when you do not want to use the default value\n   * for `routerLink`, which is the current activated route.\n   * Note that a value of `undefined` here will use the `routerLink` default.\n   * @see {@link UrlCreationOptions#relativeTo}\n   * @see {@link Router#createUrlTree}\n   */\n  relativeTo;\n  /** Whether a host element is an `<a>` tag. */\n  isAnchorElement;\n  subscription;\n  /** @internal */\n  onChanges = new Subject();\n  constructor(router, route, tabIndexAttribute, renderer, el, locationStrategy) {\n    this.router = router;\n    this.route = route;\n    this.tabIndexAttribute = tabIndexAttribute;\n    this.renderer = renderer;\n    this.el = el;\n    this.locationStrategy = locationStrategy;\n    const tagName = el.nativeElement.tagName?.toLowerCase();\n    this.isAnchorElement = tagName === 'a' || tagName === 'area';\n    if (this.isAnchorElement) {\n      this.subscription = router.events.subscribe(s => {\n        if (s instanceof NavigationEnd) {\n          this.updateHref();\n        }\n      });\n    } else {\n      this.setTabIndexIfNotOnNativeEl('0');\n    }\n  }\n  /**\n   * Passed to {@link Router#createUrlTree} as part of the\n   * `UrlCreationOptions`.\n   * @see {@link UrlCreationOptions#preserveFragment}\n   * @see {@link Router#createUrlTree}\n   */\n  preserveFragment = false;\n  /**\n   * Passed to {@link Router#navigateByUrl} as part of the\n   * `NavigationBehaviorOptions`.\n   * @see {@link NavigationBehaviorOptions#skipLocationChange}\n   * @see {@link Router#navigateByUrl}\n   */\n  skipLocationChange = false;\n  /**\n   * Passed to {@link Router#navigateByUrl} as part of the\n   * `NavigationBehaviorOptions`.\n   * @see {@link NavigationBehaviorOptions#replaceUrl}\n   * @see {@link Router#navigateByUrl}\n   */\n  replaceUrl = false;\n  /**\n   * Modifies the tab index if there was not a tabindex attribute on the element during\n   * instantiation.\n   */\n  setTabIndexIfNotOnNativeEl(newTabIndex) {\n    if (this.tabIndexAttribute != null /* both `null` and `undefined` */ || this.isAnchorElement) {\n      return;\n    }\n    this.applyAttributeValue('tabindex', newTabIndex);\n  }\n  /** @docs-private */\n  // TODO(atscott): Remove changes parameter in major version as a breaking change.\n  ngOnChanges(changes) {\n    if (ngDevMode && isUrlTree(this.routerLinkInput) && (this.fragment !== undefined || this.queryParams || this.queryParamsHandling || this.preserveFragment || this.relativeTo)) {\n      throw new _RuntimeError(4016 /* RuntimeErrorCode.INVALID_ROUTER_LINK_INPUTS */, 'Cannot configure queryParams or fragment when using a UrlTree as the routerLink input value.');\n    }\n    if (this.isAnchorElement) {\n      this.updateHref();\n    }\n    // This is subscribed to by `RouterLinkActive` so that it knows to update when there are changes\n    // to the RouterLinks it's tracking.\n    this.onChanges.next(this);\n  }\n  routerLinkInput = null;\n  /**\n   * Commands to pass to {@link Router#createUrlTree} or a `UrlTree`.\n   *   - **array**: commands to pass to {@link Router#createUrlTree}.\n   *   - **string**: shorthand for array of commands with just the string, i.e. `['/route']`\n   *   - **UrlTree**: a `UrlTree` for this link rather than creating one from the commands\n   *     and other inputs that correspond to properties of `UrlCreationOptions`.\n   *   - **null|undefined**: effectively disables the `routerLink`\n   * @see {@link Router#createUrlTree}\n   */\n  set routerLink(commandsOrUrlTree) {\n    if (commandsOrUrlTree == null) {\n      this.routerLinkInput = null;\n      this.setTabIndexIfNotOnNativeEl(null);\n    } else {\n      if (isUrlTree(commandsOrUrlTree)) {\n        this.routerLinkInput = commandsOrUrlTree;\n      } else {\n        this.routerLinkInput = Array.isArray(commandsOrUrlTree) ? commandsOrUrlTree : [commandsOrUrlTree];\n      }\n      this.setTabIndexIfNotOnNativeEl('0');\n    }\n  }\n  /** @docs-private */\n  onClick(button, ctrlKey, shiftKey, altKey, metaKey) {\n    const urlTree = this.urlTree;\n    if (urlTree === null) {\n      return true;\n    }\n    if (this.isAnchorElement) {\n      if (button !== 0 || ctrlKey || shiftKey || altKey || metaKey) {\n        return true;\n      }\n      if (typeof this.target === 'string' && this.target != '_self') {\n        return true;\n      }\n    }\n    const extras = {\n      skipLocationChange: this.skipLocationChange,\n      replaceUrl: this.replaceUrl,\n      state: this.state,\n      info: this.info\n    };\n    this.router.navigateByUrl(urlTree, extras);\n    // Return `false` for `<a>` elements to prevent default action\n    // and cancel the native behavior, since the navigation is handled\n    // by the Router.\n    return !this.isAnchorElement;\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    this.subscription?.unsubscribe();\n  }\n  updateHref() {\n    const urlTree = this.urlTree;\n    this.href = urlTree !== null && this.locationStrategy ? this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(urlTree)) : null;\n    const sanitizedValue = this.href === null ? null :\n    // This class represents a directive that can be added to both `<a>` elements,\n    // as well as other elements. As a result, we can't define security context at\n    // compile time. So the security context is deferred to runtime.\n    // The `ɵɵsanitizeUrlOrResourceUrl` selects the necessary sanitizer function\n    // based on the tag and property names. The logic mimics the one from\n    // `packages/compiler/src/schema/dom_security_schema.ts`, which is used at compile time.\n    //\n    // Note: we should investigate whether we can switch to using `@HostBinding('attr.href')`\n    // instead of applying a value via a renderer, after a final merge of the\n    // `RouterLinkWithHref` directive.\n    __sanitizeUrlOrResourceUrl(this.href, this.el.nativeElement.tagName.toLowerCase(), 'href');\n    this.applyAttributeValue('href', sanitizedValue);\n  }\n  applyAttributeValue(attrName, attrValue) {\n    const renderer = this.renderer;\n    const nativeElement = this.el.nativeElement;\n    if (attrValue !== null) {\n      renderer.setAttribute(nativeElement, attrName, attrValue);\n    } else {\n      renderer.removeAttribute(nativeElement, attrName);\n    }\n  }\n  get urlTree() {\n    if (this.routerLinkInput === null) {\n      return null;\n    } else if (isUrlTree(this.routerLinkInput)) {\n      return this.routerLinkInput;\n    }\n    return this.router.createUrlTree(this.routerLinkInput, {\n      // If the `relativeTo` input is not defined, we want to use `this.route` by default.\n      // Otherwise, we should use the value provided by the user in the input.\n      relativeTo: this.relativeTo !== undefined ? this.relativeTo : this.route,\n      queryParams: this.queryParams,\n      fragment: this.fragment,\n      queryParamsHandling: this.queryParamsHandling,\n      preserveFragment: this.preserveFragment\n    });\n  }\n  static ɵfac = function RouterLink_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RouterLink)(i0.ɵɵdirectiveInject(Router), i0.ɵɵdirectiveInject(ActivatedRoute), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.LocationStrategy));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RouterLink,\n    selectors: [[\"\", \"routerLink\", \"\"]],\n    hostVars: 1,\n    hostBindings: function RouterLink_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function RouterLink_click_HostBindingHandler($event) {\n          return ctx.onClick($event.button, $event.ctrlKey, $event.shiftKey, $event.altKey, $event.metaKey);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"target\", ctx.target);\n      }\n    },\n    inputs: {\n      target: \"target\",\n      queryParams: \"queryParams\",\n      fragment: \"fragment\",\n      queryParamsHandling: \"queryParamsHandling\",\n      state: \"state\",\n      info: \"info\",\n      relativeTo: \"relativeTo\",\n      preserveFragment: [2, \"preserveFragment\", \"preserveFragment\", booleanAttribute],\n      skipLocationChange: [2, \"skipLocationChange\", \"skipLocationChange\", booleanAttribute],\n      replaceUrl: [2, \"replaceUrl\", \"replaceUrl\", booleanAttribute],\n      routerLink: \"routerLink\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterLink, [{\n    type: Directive,\n    args: [{\n      selector: '[routerLink]'\n    }]\n  }], () => [{\n    type: Router\n  }, {\n    type: ActivatedRoute\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['tabindex']\n    }]\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i3.LocationStrategy\n  }], {\n    target: [{\n      type: HostBinding,\n      args: ['attr.target']\n    }, {\n      type: Input\n    }],\n    queryParams: [{\n      type: Input\n    }],\n    fragment: [{\n      type: Input\n    }],\n    queryParamsHandling: [{\n      type: Input\n    }],\n    state: [{\n      type: Input\n    }],\n    info: [{\n      type: Input\n    }],\n    relativeTo: [{\n      type: Input\n    }],\n    preserveFragment: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    skipLocationChange: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    replaceUrl: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    routerLink: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event.button', '$event.ctrlKey', '$event.shiftKey', '$event.altKey', '$event.metaKey']]\n    }]\n  });\n})();\n\n/**\n *\n * @description\n *\n * Tracks whether the linked route of an element is currently active, and allows you\n * to specify one or more CSS classes to add to the element when the linked route\n * is active.\n *\n * Use this directive to create a visual distinction for elements associated with an active route.\n * For example, the following code highlights the word \"Bob\" when the router\n * activates the associated route:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\">Bob</a>\n * ```\n *\n * Whenever the URL is either '/user' or '/user/bob', the \"active-link\" class is\n * added to the anchor tag. If the URL changes, the class is removed.\n *\n * You can set more than one class using a space-separated string or an array.\n * For example:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"class1 class2\">Bob</a>\n * <a routerLink=\"/user/bob\" [routerLinkActive]=\"['class1', 'class2']\">Bob</a>\n * ```\n *\n * To add the classes only when the URL matches the link exactly, add the option `exact: true`:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact:\n * true}\">Bob</a>\n * ```\n *\n * To directly check the `isActive` status of the link, assign the `RouterLinkActive`\n * instance to a template variable.\n * For example, the following checks the status without assigning any CSS classes:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive #rla=\"routerLinkActive\">\n *   Bob {{ rla.isActive ? '(already open)' : ''}}\n * </a>\n * ```\n *\n * You can apply the `RouterLinkActive` directive to an ancestor of linked elements.\n * For example, the following sets the active-link class on the `<div>`  parent tag\n * when the URL is either '/user/jim' or '/user/bob'.\n *\n * ```html\n * <div routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact: true}\">\n *   <a routerLink=\"/user/jim\">Jim</a>\n *   <a routerLink=\"/user/bob\">Bob</a>\n * </div>\n * ```\n *\n * The `RouterLinkActive` directive can also be used to set the aria-current attribute\n * to provide an alternative distinction for active elements to visually impaired users.\n *\n * For example, the following code adds the 'active' class to the Home Page link when it is\n * indeed active and in such case also sets its aria-current attribute to 'page':\n *\n * ```html\n * <a routerLink=\"/\" routerLinkActive=\"active\" ariaCurrentWhenActive=\"page\">Home Page</a>\n * ```\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\nclass RouterLinkActive {\n  router;\n  element;\n  renderer;\n  cdr;\n  link;\n  links;\n  classes = [];\n  routerEventsSubscription;\n  linkInputChangesSubscription;\n  _isActive = false;\n  get isActive() {\n    return this._isActive;\n  }\n  /**\n   * Options to configure how to determine if the router link is active.\n   *\n   * These options are passed to the `Router.isActive()` function.\n   *\n   * @see {@link Router#isActive}\n   */\n  routerLinkActiveOptions = {\n    exact: false\n  };\n  /**\n   * Aria-current attribute to apply when the router link is active.\n   *\n   * Possible values: `'page'` | `'step'` | `'location'` | `'date'` | `'time'` | `true` | `false`.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-current}\n   */\n  ariaCurrentWhenActive;\n  /**\n   *\n   * You can use the output `isActiveChange` to get notified each time the link becomes\n   * active or inactive.\n   *\n   * Emits:\n   * true  -> Route is active\n   * false -> Route is inactive\n   *\n   * ```html\n   * <a\n   *  routerLink=\"/user/bob\"\n   *  routerLinkActive=\"active-link\"\n   *  (isActiveChange)=\"this.onRouterLinkActive($event)\">Bob</a>\n   * ```\n   */\n  isActiveChange = new EventEmitter();\n  constructor(router, element, renderer, cdr, link) {\n    this.router = router;\n    this.element = element;\n    this.renderer = renderer;\n    this.cdr = cdr;\n    this.link = link;\n    this.routerEventsSubscription = router.events.subscribe(s => {\n      if (s instanceof NavigationEnd) {\n        this.update();\n      }\n    });\n  }\n  /** @docs-private */\n  ngAfterContentInit() {\n    // `of(null)` is used to force subscribe body to execute once immediately (like `startWith`).\n    of(this.links.changes, of(null)).pipe(mergeAll()).subscribe(_ => {\n      this.update();\n      this.subscribeToEachLinkOnChanges();\n    });\n  }\n  subscribeToEachLinkOnChanges() {\n    this.linkInputChangesSubscription?.unsubscribe();\n    const allLinkChanges = [...this.links.toArray(), this.link].filter(link => !!link).map(link => link.onChanges);\n    this.linkInputChangesSubscription = from(allLinkChanges).pipe(mergeAll()).subscribe(link => {\n      if (this._isActive !== this.isLinkActive(this.router)(link)) {\n        this.update();\n      }\n    });\n  }\n  set routerLinkActive(data) {\n    const classes = Array.isArray(data) ? data : data.split(' ');\n    this.classes = classes.filter(c => !!c);\n  }\n  /** @docs-private */\n  ngOnChanges(changes) {\n    this.update();\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    this.routerEventsSubscription.unsubscribe();\n    this.linkInputChangesSubscription?.unsubscribe();\n  }\n  update() {\n    if (!this.links || !this.router.navigated) return;\n    queueMicrotask(() => {\n      const hasActiveLinks = this.hasActiveLinks();\n      this.classes.forEach(c => {\n        if (hasActiveLinks) {\n          this.renderer.addClass(this.element.nativeElement, c);\n        } else {\n          this.renderer.removeClass(this.element.nativeElement, c);\n        }\n      });\n      if (hasActiveLinks && this.ariaCurrentWhenActive !== undefined) {\n        this.renderer.setAttribute(this.element.nativeElement, 'aria-current', this.ariaCurrentWhenActive.toString());\n      } else {\n        this.renderer.removeAttribute(this.element.nativeElement, 'aria-current');\n      }\n      // Only emit change if the active state changed.\n      if (this._isActive !== hasActiveLinks) {\n        this._isActive = hasActiveLinks;\n        this.cdr.markForCheck();\n        // Emit on isActiveChange after classes are updated\n        this.isActiveChange.emit(hasActiveLinks);\n      }\n    });\n  }\n  isLinkActive(router) {\n    const options = isActiveMatchOptions(this.routerLinkActiveOptions) ? this.routerLinkActiveOptions :\n    // While the types should disallow `undefined` here, it's possible without strict inputs\n    this.routerLinkActiveOptions.exact || false;\n    return link => {\n      const urlTree = link.urlTree;\n      return urlTree ? router.isActive(urlTree, options) : false;\n    };\n  }\n  hasActiveLinks() {\n    const isActiveCheckFn = this.isLinkActive(this.router);\n    return this.link && isActiveCheckFn(this.link) || this.links.some(isActiveCheckFn);\n  }\n  static ɵfac = function RouterLinkActive_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RouterLinkActive)(i0.ɵɵdirectiveInject(Router), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(RouterLink, 8));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RouterLinkActive,\n    selectors: [[\"\", \"routerLinkActive\", \"\"]],\n    contentQueries: function RouterLinkActive_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, RouterLink, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.links = _t);\n      }\n    },\n    inputs: {\n      routerLinkActiveOptions: \"routerLinkActiveOptions\",\n      ariaCurrentWhenActive: \"ariaCurrentWhenActive\",\n      routerLinkActive: \"routerLinkActive\"\n    },\n    outputs: {\n      isActiveChange: \"isActiveChange\"\n    },\n    exportAs: [\"routerLinkActive\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterLinkActive, [{\n    type: Directive,\n    args: [{\n      selector: '[routerLinkActive]',\n      exportAs: 'routerLinkActive'\n    }]\n  }], () => [{\n    type: Router\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: RouterLink,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    links: [{\n      type: ContentChildren,\n      args: [RouterLink, {\n        descendants: true\n      }]\n    }],\n    routerLinkActiveOptions: [{\n      type: Input\n    }],\n    ariaCurrentWhenActive: [{\n      type: Input\n    }],\n    isActiveChange: [{\n      type: Output\n    }],\n    routerLinkActive: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Use instead of `'paths' in options` to be compatible with property renaming\n */\nfunction isActiveMatchOptions(options) {\n  return !!options.paths;\n}\n\n/**\n * @description\n *\n * Provides a preloading strategy.\n *\n * @publicApi\n */\nclass PreloadingStrategy {}\n/**\n * @description\n *\n * Provides a preloading strategy that preloads all modules as quickly as possible.\n *\n * ```ts\n * RouterModule.forRoot(ROUTES, {preloadingStrategy: PreloadAllModules})\n * ```\n *\n * @publicApi\n */\nclass PreloadAllModules {\n  preload(route, fn) {\n    return fn().pipe(catchError(() => of(null)));\n  }\n  static ɵfac = function PreloadAllModules_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PreloadAllModules)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: PreloadAllModules,\n    factory: PreloadAllModules.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PreloadAllModules, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/**\n * @description\n *\n * Provides a preloading strategy that does not preload any modules.\n *\n * This strategy is enabled by default.\n *\n * @publicApi\n */\nclass NoPreloading {\n  preload(route, fn) {\n    return of(null);\n  }\n  static ɵfac = function NoPreloading_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NoPreloading)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NoPreloading,\n    factory: NoPreloading.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NoPreloading, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/**\n * The preloader optimistically loads all router configurations to\n * make navigations into lazily-loaded sections of the application faster.\n *\n * The preloader runs in the background. When the router bootstraps, the preloader\n * starts listening to all navigation events. After every such event, the preloader\n * will check if any configurations can be loaded lazily.\n *\n * If a route is protected by `canLoad` guards, the preloaded will not load it.\n *\n * @publicApi\n */\nclass RouterPreloader {\n  router;\n  injector;\n  preloadingStrategy;\n  loader;\n  subscription;\n  constructor(router, injector, preloadingStrategy, loader) {\n    this.router = router;\n    this.injector = injector;\n    this.preloadingStrategy = preloadingStrategy;\n    this.loader = loader;\n  }\n  setUpPreloading() {\n    this.subscription = this.router.events.pipe(filter(e => e instanceof NavigationEnd), concatMap(() => this.preload())).subscribe(() => {});\n  }\n  preload() {\n    return this.processRoutes(this.injector, this.router.config);\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n  processRoutes(injector, routes) {\n    const res = [];\n    for (const route of routes) {\n      if (route.providers && !route._injector) {\n        route._injector = createEnvironmentInjector(route.providers, injector, `Route: ${route.path}`);\n      }\n      const injectorForCurrentRoute = route._injector ?? injector;\n      const injectorForChildren = route._loadedInjector ?? injectorForCurrentRoute;\n      // Note that `canLoad` is only checked as a condition that prevents `loadChildren` and not\n      // `loadComponent`. `canLoad` guards only block loading of child routes by design. This\n      // happens as a consequence of needing to descend into children for route matching immediately\n      // while component loading is deferred until route activation. Because `canLoad` guards can\n      // have side effects, we cannot execute them here so we instead skip preloading altogether\n      // when present. Lastly, it remains to be decided whether `canLoad` should behave this way\n      // at all. Code splitting and lazy loading is separate from client-side authorization checks\n      // and should not be used as a security measure to prevent loading of code.\n      if (route.loadChildren && !route._loadedRoutes && route.canLoad === undefined || route.loadComponent && !route._loadedComponent) {\n        res.push(this.preloadConfig(injectorForCurrentRoute, route));\n      }\n      if (route.children || route._loadedRoutes) {\n        res.push(this.processRoutes(injectorForChildren, route.children ?? route._loadedRoutes));\n      }\n    }\n    return from(res).pipe(mergeAll());\n  }\n  preloadConfig(injector, route) {\n    return this.preloadingStrategy.preload(route, () => {\n      let loadedChildren$;\n      if (route.loadChildren && route.canLoad === undefined) {\n        loadedChildren$ = this.loader.loadChildren(injector, route);\n      } else {\n        loadedChildren$ = of(null);\n      }\n      const recursiveLoadChildren$ = loadedChildren$.pipe(mergeMap(config => {\n        if (config === null) {\n          return of(void 0);\n        }\n        route._loadedRoutes = config.routes;\n        route._loadedInjector = config.injector;\n        // If the loaded config was a module, use that as the module/module injector going\n        // forward. Otherwise, continue using the current module/module injector.\n        return this.processRoutes(config.injector ?? injector, config.routes);\n      }));\n      if (route.loadComponent && !route._loadedComponent) {\n        const loadComponent$ = this.loader.loadComponent(route);\n        return from([recursiveLoadChildren$, loadComponent$]).pipe(mergeAll());\n      } else {\n        return recursiveLoadChildren$;\n      }\n    });\n  }\n  static ɵfac = function RouterPreloader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RouterPreloader)(i0.ɵɵinject(Router), i0.ɵɵinject(i0.EnvironmentInjector), i0.ɵɵinject(PreloadingStrategy), i0.ɵɵinject(RouterConfigLoader));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: RouterPreloader,\n    factory: RouterPreloader.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterPreloader, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: Router\n  }, {\n    type: i0.EnvironmentInjector\n  }, {\n    type: PreloadingStrategy\n  }, {\n    type: RouterConfigLoader\n  }], null);\n})();\nconst ROUTER_SCROLLER = new InjectionToken('');\nclass RouterScroller {\n  urlSerializer;\n  transitions;\n  viewportScroller;\n  zone;\n  options;\n  routerEventsSubscription;\n  scrollEventsSubscription;\n  lastId = 0;\n  lastSource = 'imperative';\n  restoredId = 0;\n  store = {};\n  /** @docs-private */\n  constructor(urlSerializer, transitions, viewportScroller, zone, options = {}) {\n    this.urlSerializer = urlSerializer;\n    this.transitions = transitions;\n    this.viewportScroller = viewportScroller;\n    this.zone = zone;\n    this.options = options;\n    // Default both options to 'disabled'\n    options.scrollPositionRestoration ||= 'disabled';\n    options.anchorScrolling ||= 'disabled';\n  }\n  init() {\n    // we want to disable the automatic scrolling because having two places\n    // responsible for scrolling results race conditions, especially given\n    // that browser don't implement this behavior consistently\n    if (this.options.scrollPositionRestoration !== 'disabled') {\n      this.viewportScroller.setHistoryScrollRestoration('manual');\n    }\n    this.routerEventsSubscription = this.createScrollEvents();\n    this.scrollEventsSubscription = this.consumeScrollEvents();\n  }\n  createScrollEvents() {\n    return this.transitions.events.subscribe(e => {\n      if (e instanceof NavigationStart) {\n        // store the scroll position of the current stable navigations.\n        this.store[this.lastId] = this.viewportScroller.getScrollPosition();\n        this.lastSource = e.navigationTrigger;\n        this.restoredId = e.restoredState ? e.restoredState.navigationId : 0;\n      } else if (e instanceof NavigationEnd) {\n        this.lastId = e.id;\n        this.scheduleScrollEvent(e, this.urlSerializer.parse(e.urlAfterRedirects).fragment);\n      } else if (e instanceof NavigationSkipped && e.code === NavigationSkippedCode.IgnoredSameUrlNavigation) {\n        this.lastSource = undefined;\n        this.restoredId = 0;\n        this.scheduleScrollEvent(e, this.urlSerializer.parse(e.url).fragment);\n      }\n    });\n  }\n  consumeScrollEvents() {\n    return this.transitions.events.subscribe(e => {\n      if (!(e instanceof Scroll)) return;\n      // a popstate event. The pop state event will always ignore anchor scrolling.\n      if (e.position) {\n        if (this.options.scrollPositionRestoration === 'top') {\n          this.viewportScroller.scrollToPosition([0, 0]);\n        } else if (this.options.scrollPositionRestoration === 'enabled') {\n          this.viewportScroller.scrollToPosition(e.position);\n        }\n        // imperative navigation \"forward\"\n      } else {\n        if (e.anchor && this.options.anchorScrolling === 'enabled') {\n          this.viewportScroller.scrollToAnchor(e.anchor);\n        } else if (this.options.scrollPositionRestoration !== 'disabled') {\n          this.viewportScroller.scrollToPosition([0, 0]);\n        }\n      }\n    });\n  }\n  scheduleScrollEvent(routerEvent, anchor) {\n    this.zone.runOutsideAngular(() => {\n      // The scroll event needs to be delayed until after change detection. Otherwise, we may\n      // attempt to restore the scroll position before the router outlet has fully rendered the\n      // component by executing its update block of the template function.\n      setTimeout(() => {\n        this.zone.run(() => {\n          this.transitions.events.next(new Scroll(routerEvent, this.lastSource === 'popstate' ? this.store[this.restoredId] : null, anchor));\n        });\n      }, 0);\n    });\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    this.routerEventsSubscription?.unsubscribe();\n    this.scrollEventsSubscription?.unsubscribe();\n  }\n  static ɵfac = function RouterScroller_Factory(__ngFactoryType__) {\n    i0.ɵɵinvalidFactory();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: RouterScroller,\n    factory: RouterScroller.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterScroller, [{\n    type: Injectable\n  }], () => [{\n    type: UrlSerializer\n  }, {\n    type: NavigationTransitions\n  }, {\n    type: i3.ViewportScroller\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined\n  }], null);\n})();\n\n/**\n * Sets up providers necessary to enable `Router` functionality for the application.\n * Allows to configure a set of routes as well as extra features that should be enabled.\n *\n * @usageNotes\n *\n * Basic example of how you can add a Router to your application:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent, {\n *   providers: [provideRouter(appRoutes)]\n * });\n * ```\n *\n * You can also enable optional features in the Router by adding functions from the `RouterFeatures`\n * type:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes,\n *         withDebugTracing(),\n *         withRouterConfig({paramsInheritanceStrategy: 'always'}))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link RouterFeatures}\n *\n * @publicApi\n * @param routes A set of `Route`s to use for the application routing table.\n * @param features Optional features to configure additional router behaviors.\n * @returns A set of providers to setup a Router.\n */\nfunction provideRouter(routes, ...features) {\n  return makeEnvironmentProviders([{\n    provide: ROUTES,\n    multi: true,\n    useValue: routes\n  }, typeof ngDevMode === 'undefined' || ngDevMode ? {\n    provide: ROUTER_IS_PROVIDED,\n    useValue: true\n  } : [], {\n    provide: ActivatedRoute,\n    useFactory: rootRoute,\n    deps: [Router]\n  }, {\n    provide: APP_BOOTSTRAP_LISTENER,\n    multi: true,\n    useFactory: getBootstrapListener\n  }, features.map(feature => feature.ɵproviders)]);\n}\nfunction rootRoute(router) {\n  return router.routerState.root;\n}\n/**\n * Helper function to create an object that represents a Router feature.\n */\nfunction routerFeature(kind, providers) {\n  return {\n    ɵkind: kind,\n    ɵproviders: providers\n  };\n}\n/**\n * An Injection token used to indicate whether `provideRouter` or `RouterModule.forRoot` was ever\n * called.\n */\nconst ROUTER_IS_PROVIDED = new InjectionToken('', {\n  providedIn: 'root',\n  factory: () => false\n});\nconst routerIsProvidedDevModeCheck = {\n  provide: ENVIRONMENT_INITIALIZER,\n  multi: true,\n  useFactory() {\n    return () => {\n      if (!inject(ROUTER_IS_PROVIDED)) {\n        console.warn('`provideRoutes` was called without `provideRouter` or `RouterModule.forRoot`. ' + 'This is likely a mistake.');\n      }\n    };\n  }\n};\n/**\n * Registers a DI provider for a set of routes.\n * @param routes The route configuration to provide.\n *\n * @usageNotes\n *\n * ```ts\n * @NgModule({\n *   providers: [provideRoutes(ROUTES)]\n * })\n * class LazyLoadedChildModule {}\n * ```\n *\n * @deprecated If necessary, provide routes using the `ROUTES` `InjectionToken`.\n * @see {@link ROUTES}\n * @publicApi\n */\nfunction provideRoutes(routes) {\n  return [{\n    provide: ROUTES,\n    multi: true,\n    useValue: routes\n  }, typeof ngDevMode === 'undefined' || ngDevMode ? routerIsProvidedDevModeCheck : []];\n}\n/**\n * Enables customizable scrolling behavior for router navigations.\n *\n * @usageNotes\n *\n * Basic example of how you can enable scrolling feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withInMemoryScrolling())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n * @see {@link ViewportScroller}\n *\n * @publicApi\n * @param options Set of configuration parameters to customize scrolling behavior, see\n *     `InMemoryScrollingOptions` for additional information.\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withInMemoryScrolling(options = {}) {\n  const providers = [{\n    provide: ROUTER_SCROLLER,\n    useFactory: () => {\n      const viewportScroller = inject(ViewportScroller);\n      const zone = inject(NgZone);\n      const transitions = inject(NavigationTransitions);\n      const urlSerializer = inject(UrlSerializer);\n      return new RouterScroller(urlSerializer, transitions, viewportScroller, zone, options);\n    }\n  }];\n  return routerFeature(4 /* RouterFeatureKind.InMemoryScrollingFeature */, providers);\n}\nfunction getBootstrapListener() {\n  const injector = inject(Injector);\n  return bootstrappedComponentRef => {\n    const ref = injector.get(ApplicationRef);\n    if (bootstrappedComponentRef !== ref.components[0]) {\n      return;\n    }\n    const router = injector.get(Router);\n    const bootstrapDone = injector.get(BOOTSTRAP_DONE);\n    if (injector.get(INITIAL_NAVIGATION) === 1 /* InitialNavigation.EnabledNonBlocking */) {\n      router.initialNavigation();\n    }\n    injector.get(ROUTER_PRELOADER, null, InjectFlags.Optional)?.setUpPreloading();\n    injector.get(ROUTER_SCROLLER, null, InjectFlags.Optional)?.init();\n    router.resetRootComponentType(ref.componentTypes[0]);\n    if (!bootstrapDone.closed) {\n      bootstrapDone.next();\n      bootstrapDone.complete();\n      bootstrapDone.unsubscribe();\n    }\n  };\n}\n/**\n * A subject used to indicate that the bootstrapping phase is done. When initial navigation is\n * `enabledBlocking`, the first navigation waits until bootstrapping is finished before continuing\n * to the activation phase.\n */\nconst BOOTSTRAP_DONE = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'bootstrap done indicator' : '', {\n  factory: () => {\n    return new Subject();\n  }\n});\nconst INITIAL_NAVIGATION = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'initial navigation' : '', {\n  providedIn: 'root',\n  factory: () => 1 /* InitialNavigation.EnabledNonBlocking */\n});\n/**\n * Configures initial navigation to start before the root component is created.\n *\n * The bootstrap is blocked until the initial navigation is complete. This should be set in case\n * you use [server-side rendering](guide/ssr), but do not enable [hydration](guide/hydration) for\n * your application.\n *\n * @usageNotes\n *\n * Basic example of how you can enable this navigation behavior:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withEnabledBlockingInitialNavigation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @publicApi\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withEnabledBlockingInitialNavigation() {\n  const providers = [{\n    provide: INITIAL_NAVIGATION,\n    useValue: 0 /* InitialNavigation.EnabledBlocking */\n  }, provideAppInitializer(() => {\n    const injector = inject(Injector);\n    const locationInitialized = injector.get(LOCATION_INITIALIZED, Promise.resolve());\n    return locationInitialized.then(() => {\n      return new Promise(resolve => {\n        const router = injector.get(Router);\n        const bootstrapDone = injector.get(BOOTSTRAP_DONE);\n        afterNextNavigation(router, () => {\n          // Unblock APP_INITIALIZER in case the initial navigation was canceled or errored\n          // without a redirect.\n          resolve(true);\n        });\n        injector.get(NavigationTransitions).afterPreactivation = () => {\n          // Unblock APP_INITIALIZER once we get to `afterPreactivation`. At this point, we\n          // assume activation will complete successfully (even though this is not\n          // guaranteed).\n          resolve(true);\n          return bootstrapDone.closed ? of(void 0) : bootstrapDone;\n        };\n        router.initialNavigation();\n      });\n    });\n  })];\n  return routerFeature(2 /* RouterFeatureKind.EnabledBlockingInitialNavigationFeature */, providers);\n}\n/**\n * Disables initial navigation.\n *\n * Use if there is a reason to have more control over when the router starts its initial navigation\n * due to some complex initialization logic.\n *\n * @usageNotes\n *\n * Basic example of how you can disable initial navigation:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withDisabledInitialNavigation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withDisabledInitialNavigation() {\n  const providers = [provideAppInitializer(() => {\n    inject(Router).setUpLocationChangeListener();\n  }), {\n    provide: INITIAL_NAVIGATION,\n    useValue: 2 /* InitialNavigation.Disabled */\n  }];\n  return routerFeature(3 /* RouterFeatureKind.DisabledInitialNavigationFeature */, providers);\n}\n/**\n * Enables logging of all internal navigation events to the console.\n * Extra logging might be useful for debugging purposes to inspect Router event sequence.\n *\n * @usageNotes\n *\n * Basic example of how you can enable debug tracing:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withDebugTracing())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withDebugTracing() {\n  let providers = [];\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    providers = [{\n      provide: ENVIRONMENT_INITIALIZER,\n      multi: true,\n      useFactory: () => {\n        const router = inject(Router);\n        return () => router.events.subscribe(e => {\n          // tslint:disable:no-console\n          console.group?.(`Router Event: ${e.constructor.name}`);\n          console.log(stringifyEvent(e));\n          console.log(e);\n          console.groupEnd?.();\n          // tslint:enable:no-console\n        });\n      }\n    }];\n  } else {\n    providers = [];\n  }\n  return routerFeature(1 /* RouterFeatureKind.DebugTracingFeature */, providers);\n}\nconst ROUTER_PRELOADER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'router preloader' : '');\n/**\n * Allows to configure a preloading strategy to use. The strategy is configured by providing a\n * reference to a class that implements a `PreloadingStrategy`.\n *\n * @usageNotes\n *\n * Basic example of how you can configure preloading:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withPreloading(PreloadAllModules))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @param preloadingStrategy A reference to a class that implements a `PreloadingStrategy` that\n *     should be used.\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withPreloading(preloadingStrategy) {\n  const providers = [{\n    provide: ROUTER_PRELOADER,\n    useExisting: RouterPreloader\n  }, {\n    provide: PreloadingStrategy,\n    useExisting: preloadingStrategy\n  }];\n  return routerFeature(0 /* RouterFeatureKind.PreloadingFeature */, providers);\n}\n/**\n * Allows to provide extra parameters to configure Router.\n *\n * @usageNotes\n *\n * Basic example of how you can provide extra configuration options:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withRouterConfig({\n *          onSameUrlNavigation: 'reload'\n *       }))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @param options A set of parameters to configure Router, see `RouterConfigOptions` for\n *     additional information.\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withRouterConfig(options) {\n  const providers = [{\n    provide: ROUTER_CONFIGURATION,\n    useValue: options\n  }];\n  return routerFeature(5 /* RouterFeatureKind.RouterConfigurationFeature */, providers);\n}\n/**\n * Provides the location strategy that uses the URL fragment instead of the history API.\n *\n * @usageNotes\n *\n * Basic example of how you can use the hash location option:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withHashLocation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n * @see {@link /api/common/HashLocationStrategy HashLocationStrategy}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withHashLocation() {\n  const providers = [{\n    provide: LocationStrategy,\n    useClass: HashLocationStrategy\n  }];\n  return routerFeature(6 /* RouterFeatureKind.RouterHashLocationFeature */, providers);\n}\n/**\n * Provides a function which is called when a navigation error occurs.\n *\n * This function is run inside application's [injection context](guide/di/dependency-injection-context)\n * so you can use the [`inject`](api/core/inject) function.\n *\n * This function can return a `RedirectCommand` to convert the error to a redirect, similar to returning\n * a `UrlTree` or `RedirectCommand` from a guard. This will also prevent the `Router` from emitting\n * `NavigationError`; it will instead emit `NavigationCancel` with code NavigationCancellationCode.Redirect.\n * Return values other than `RedirectCommand` are ignored and do not change any behavior with respect to\n * how the `Router` handles the error.\n *\n * @usageNotes\n *\n * Basic example of how you can use the error handler option:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withNavigationErrorHandler((e: NavigationError) =>\n * inject(MyErrorTracker).trackError(e)))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link NavigationError}\n * @see {@link /api/core/inject inject}\n * @see {@link runInInjectionContext}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withNavigationErrorHandler(handler) {\n  const providers = [{\n    provide: NAVIGATION_ERROR_HANDLER,\n    useValue: handler\n  }];\n  return routerFeature(7 /* RouterFeatureKind.NavigationErrorHandlerFeature */, providers);\n}\n/**\n * Enables binding information from the `Router` state directly to the inputs of the component in\n * `Route` configurations.\n *\n * @usageNotes\n *\n * Basic example of how you can enable the feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withComponentInputBinding())\n *     ]\n *   }\n * );\n * ```\n *\n * The router bindings information from any of the following sources:\n *\n *  - query parameters\n *  - path and matrix parameters\n *  - static route data\n *  - data from resolvers\n *\n * Duplicate keys are resolved in the same order from above, from least to greatest,\n * meaning that resolvers have the highest precedence and override any of the other information\n * from the route.\n *\n * Importantly, when an input does not have an item in the route data with a matching key, this\n * input is set to `undefined`. This prevents previous information from being\n * retained if the data got removed from the route (i.e. if a query parameter is removed).\n * Default values can be provided with a resolver on the route to ensure the value is always present\n * or an input and use an input transform in the component.\n *\n * @see {@link /guide/components/inputs#input-transforms Input Transforms}\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withComponentInputBinding() {\n  const providers = [RoutedComponentInputBinder, {\n    provide: INPUT_BINDER,\n    useExisting: RoutedComponentInputBinder\n  }];\n  return routerFeature(8 /* RouterFeatureKind.ComponentInputBindingFeature */, providers);\n}\n/**\n * Enables view transitions in the Router by running the route activation and deactivation inside of\n * `document.startViewTransition`.\n *\n * Note: The View Transitions API is not available in all browsers. If the browser does not support\n * view transitions, the Router will not attempt to start a view transition and continue processing\n * the navigation as usual.\n *\n * @usageNotes\n *\n * Basic example of how you can enable the feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withViewTransitions())\n *     ]\n *   }\n * );\n * ```\n *\n * @returns A set of providers for use with `provideRouter`.\n * @see https://developer.chrome.com/docs/web-platform/view-transitions/\n * @see https://developer.mozilla.org/en-US/docs/Web/API/View_Transitions_API\n * @developerPreview\n */\nfunction withViewTransitions(options) {\n  _performanceMarkFeature('NgRouterViewTransitions');\n  const providers = [{\n    provide: CREATE_VIEW_TRANSITION,\n    useValue: createViewTransition\n  }, {\n    provide: VIEW_TRANSITION_OPTIONS,\n    useValue: {\n      skipNextTransition: !!options?.skipInitialTransition,\n      ...options\n    }\n  }];\n  return routerFeature(9 /* RouterFeatureKind.ViewTransitionsFeature */, providers);\n}\n\n/**\n * The directives defined in the `RouterModule`.\n */\nconst ROUTER_DIRECTIVES = [RouterOutlet, RouterLink, RouterLinkActive, _EmptyOutletComponent];\n/**\n * @docsNotRequired\n */\nconst ROUTER_FORROOT_GUARD = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'router duplicate forRoot guard' : '');\n// TODO(atscott): All of these except `ActivatedRoute` are `providedIn: 'root'`. They are only kept\n// here to avoid a breaking change whereby the provider order matters based on where the\n// `RouterModule`/`RouterTestingModule` is imported. These can/should be removed as a \"breaking\"\n// change in a major version.\nconst ROUTER_PROVIDERS = [Location, {\n  provide: UrlSerializer,\n  useClass: DefaultUrlSerializer\n}, Router, ChildrenOutletContexts, {\n  provide: ActivatedRoute,\n  useFactory: rootRoute,\n  deps: [Router]\n}, RouterConfigLoader,\n// Only used to warn when `provideRoutes` is used without `RouterModule` or `provideRouter`. Can\n// be removed when `provideRoutes` is removed.\ntypeof ngDevMode === 'undefined' || ngDevMode ? {\n  provide: ROUTER_IS_PROVIDED,\n  useValue: true\n} : []];\n/**\n * @description\n *\n * Adds directives and providers for in-app navigation among views defined in an application.\n * Use the Angular `Router` service to declaratively specify application states and manage state\n * transitions.\n *\n * You can import this NgModule multiple times, once for each lazy-loaded bundle.\n * However, only one `Router` service can be active.\n * To ensure this, there are two ways to register routes when importing this module:\n *\n * * The `forRoot()` method creates an `NgModule` that contains all the directives, the given\n * routes, and the `Router` service itself.\n * * The `forChild()` method creates an `NgModule` that contains all the directives and the given\n * routes, but does not include the `Router` service.\n *\n * @see [Routing and Navigation guide](guide/routing/common-router-tasks) for an\n * overview of how the `Router` service should be used.\n *\n * @publicApi\n */\nclass RouterModule {\n  constructor() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      inject(ROUTER_FORROOT_GUARD, {\n        optional: true\n      });\n    }\n  }\n  /**\n   * Creates and configures a module with all the router providers and directives.\n   * Optionally sets up an application listener to perform an initial navigation.\n   *\n   * When registering the NgModule at the root, import as follows:\n   *\n   * ```ts\n   * @NgModule({\n   *   imports: [RouterModule.forRoot(ROUTES)]\n   * })\n   * class MyNgModule {}\n   * ```\n   *\n   * @param routes An array of `Route` objects that define the navigation paths for the application.\n   * @param config An `ExtraOptions` configuration object that controls how navigation is performed.\n   * @return The new `NgModule`.\n   *\n   */\n  static forRoot(routes, config) {\n    return {\n      ngModule: RouterModule,\n      providers: [ROUTER_PROVIDERS, typeof ngDevMode === 'undefined' || ngDevMode ? config?.enableTracing ? withDebugTracing().ɵproviders : [] : [], {\n        provide: ROUTES,\n        multi: true,\n        useValue: routes\n      }, typeof ngDevMode === 'undefined' || ngDevMode ? {\n        provide: ROUTER_FORROOT_GUARD,\n        useFactory: provideForRootGuard,\n        deps: [[Router, new Optional(), new SkipSelf()]]\n      } : [], config?.errorHandler ? {\n        provide: NAVIGATION_ERROR_HANDLER,\n        useValue: config.errorHandler\n      } : [], {\n        provide: ROUTER_CONFIGURATION,\n        useValue: config ? config : {}\n      }, config?.useHash ? provideHashLocationStrategy() : providePathLocationStrategy(), provideRouterScroller(), config?.preloadingStrategy ? withPreloading(config.preloadingStrategy).ɵproviders : [], config?.initialNavigation ? provideInitialNavigation(config) : [], config?.bindToComponentInputs ? withComponentInputBinding().ɵproviders : [], config?.enableViewTransitions ? withViewTransitions().ɵproviders : [], provideRouterInitializer()]\n    };\n  }\n  /**\n   * Creates a module with all the router directives and a provider registering routes,\n   * without creating a new Router service.\n   * When registering for submodules and lazy-loaded submodules, create the NgModule as follows:\n   *\n   * ```ts\n   * @NgModule({\n   *   imports: [RouterModule.forChild(ROUTES)]\n   * })\n   * class MyNgModule {}\n   * ```\n   *\n   * @param routes An array of `Route` objects that define the navigation paths for the submodule.\n   * @return The new NgModule.\n   *\n   */\n  static forChild(routes) {\n    return {\n      ngModule: RouterModule,\n      providers: [{\n        provide: ROUTES,\n        multi: true,\n        useValue: routes\n      }]\n    };\n  }\n  static ɵfac = function RouterModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RouterModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: RouterModule,\n    imports: [RouterOutlet, RouterLink, RouterLinkActive, _EmptyOutletComponent],\n    exports: [RouterOutlet, RouterLink, RouterLinkActive, _EmptyOutletComponent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterModule, [{\n    type: NgModule,\n    args: [{\n      imports: ROUTER_DIRECTIVES,\n      exports: ROUTER_DIRECTIVES\n    }]\n  }], () => [], null);\n})();\n/**\n * For internal use by `RouterModule` only. Note that this differs from `withInMemoryRouterScroller`\n * because it reads from the `ExtraOptions` which should not be used in the standalone world.\n */\nfunction provideRouterScroller() {\n  return {\n    provide: ROUTER_SCROLLER,\n    useFactory: () => {\n      const viewportScroller = inject(ViewportScroller);\n      const zone = inject(NgZone);\n      const config = inject(ROUTER_CONFIGURATION);\n      const transitions = inject(NavigationTransitions);\n      const urlSerializer = inject(UrlSerializer);\n      if (config.scrollOffset) {\n        viewportScroller.setOffset(config.scrollOffset);\n      }\n      return new RouterScroller(urlSerializer, transitions, viewportScroller, zone, config);\n    }\n  };\n}\n// Note: For internal use only with `RouterModule`. Standalone setup via `provideRouter` should\n// provide hash location directly via `{provide: LocationStrategy, useClass: HashLocationStrategy}`.\nfunction provideHashLocationStrategy() {\n  return {\n    provide: LocationStrategy,\n    useClass: HashLocationStrategy\n  };\n}\n// Note: For internal use only with `RouterModule`. Standalone setup via `provideRouter` does not\n// need this at all because `PathLocationStrategy` is the default factory for `LocationStrategy`.\nfunction providePathLocationStrategy() {\n  return {\n    provide: LocationStrategy,\n    useClass: PathLocationStrategy\n  };\n}\nfunction provideForRootGuard(router) {\n  if (router) {\n    throw new _RuntimeError(4007 /* RuntimeErrorCode.FOR_ROOT_CALLED_TWICE */, `The Router was provided more than once. This can happen if 'forRoot' is used outside of the root injector.` + ` Lazy loaded modules should use RouterModule.forChild() instead.`);\n  }\n  return 'guarded';\n}\n// Note: For internal use only with `RouterModule`. Standalone router setup with `provideRouter`\n// users call `withXInitialNavigation` directly.\nfunction provideInitialNavigation(config) {\n  return [config.initialNavigation === 'disabled' ? withDisabledInitialNavigation().ɵproviders : [], config.initialNavigation === 'enabledBlocking' ? withEnabledBlockingInitialNavigation().ɵproviders : []];\n}\n// TODO(atscott): This should not be in the public API\n/**\n * A DI token for the router initializer that\n * is called after the app is bootstrapped.\n *\n * @publicApi\n */\nconst ROUTER_INITIALIZER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'Router Initializer' : '');\nfunction provideRouterInitializer() {\n  return [\n  // ROUTER_INITIALIZER token should be removed. It's public API but shouldn't be. We can just\n  // have `getBootstrapListener` directly attached to APP_BOOTSTRAP_LISTENER.\n  {\n    provide: ROUTER_INITIALIZER,\n    useFactory: getBootstrapListener\n  }, {\n    provide: APP_BOOTSTRAP_LISTENER,\n    multi: true,\n    useExisting: ROUTER_INITIALIZER\n  }];\n}\nexport { NoPreloading, PreloadAllModules, PreloadingStrategy, ROUTER_INITIALIZER, ROUTER_PROVIDERS, RouterLink, RouterLinkActive, RouterModule, RouterPreloader, provideRouter, provideRoutes, withComponentInputBinding, withDebugTracing, withDisabledInitialNavigation, withEnabledBlockingInitialNavigation, withHashLocation, withInMemoryScrolling, withNavigationErrorHandler, withPreloading, withRouterConfig, withViewTransitions };\n", "/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nexport { ActivatedRoute, ActivatedRouteSnapshot, ActivationEnd, ActivationStart, BaseRouteReuseStrategy, ChildActivationEnd, ChildActivationStart, ChildrenOutletContexts, DefaultTitleStrategy, DefaultUrlSerializer, EventType, GuardsCheckEnd, GuardsCheckStart, NavigationCancel, NavigationCancellationCode, NavigationEnd, NavigationError, NavigationSkipped, NavigationSkippedCode, NavigationStart, OutletContext, PRIMARY_OUTLET, ROUTER_CONFIGURATION, ROUTER_OUTLET_DATA, ROUTES, RedirectCommand, ResolveEnd, ResolveStart, RouteConfigLoadEnd, RouteConfigLoadStart, RouteReuseStrategy, Router, RouterEvent, RouterOutlet, RouterState, RouterStateSnapshot, RoutesRecognized, Scroll, TitleStrategy, UrlHandlingStrategy, UrlSegment, UrlSegmentGroup, UrlSerializer, UrlTree, convertToParamMap, createUrlTreeFromSnapshot, defaultUrlMatcher, ɵEmptyOutletComponent, afterNextNavigation as ɵafterNextNavigation, loadChildren as ɵloadChildren } from './router-Dwfin5Au.mjs';\nexport { NoPreloading, PreloadAllModules, PreloadingStrategy, ROUTER_INITIALIZER, RouterLink, RouterLinkActive, RouterLink as RouterLinkWithHref, RouterModule, RouterPreloader, provideRouter, provideRoutes, withComponentInputBinding, withDebugTracing, withDisabledInitialNavigation, withEnabledBlockingInitialNavigation, withHashLocation, withInMemoryScrolling, withNavigationErrorHandler, withPreloading, withRouterConfig, withViewTransitions, ROUTER_PROVIDERS as ɵROUTER_PROVIDERS } from './router_module-DTJgGWLd.mjs';\nimport { inject, Version } from '@angular/core';\nimport '@angular/common';\nimport 'rxjs';\nimport 'rxjs/operators';\nimport '@angular/platform-browser';\n\n/**\n * Maps an array of injectable classes with canMatch functions to an array of equivalent\n * `CanMatchFn` for use in a `Route` definition.\n *\n * Usage {@example router/utils/functional_guards.ts region='CanActivate'}\n *\n * @publicApi\n * @see {@link Route}\n */\nfunction mapToCanMatch(providers) {\n  return providers.map(provider => (...params) => inject(provider).canMatch(...params));\n}\n/**\n * Maps an array of injectable classes with canActivate functions to an array of equivalent\n * `CanActivateFn` for use in a `Route` definition.\n *\n * Usage {@example router/utils/functional_guards.ts region='CanActivate'}\n *\n * @publicApi\n * @see {@link Route}\n */\nfunction mapToCanActivate(providers) {\n  return providers.map(provider => (...params) => inject(provider).canActivate(...params));\n}\n/**\n * Maps an array of injectable classes with canActivateChild functions to an array of equivalent\n * `CanActivateChildFn` for use in a `Route` definition.\n *\n * Usage {@example router/utils/functional_guards.ts region='CanActivate'}\n *\n * @publicApi\n * @see {@link Route}\n */\nfunction mapToCanActivateChild(providers) {\n  return providers.map(provider => (...params) => inject(provider).canActivateChild(...params));\n}\n/**\n * Maps an array of injectable classes with canDeactivate functions to an array of equivalent\n * `CanDeactivateFn` for use in a `Route` definition.\n *\n * Usage {@example router/utils/functional_guards.ts region='CanActivate'}\n *\n * @publicApi\n * @see {@link Route}\n */\nfunction mapToCanDeactivate(providers) {\n  return providers.map(provider => (...params) => inject(provider).canDeactivate(...params));\n}\n/**\n * Maps an injectable class with a resolve function to an equivalent `ResolveFn`\n * for use in a `Route` definition.\n *\n * Usage {@example router/utils/functional_guards.ts region='Resolve'}\n *\n * @publicApi\n * @see {@link Route}\n */\nfunction mapToResolve(provider) {\n  return (...params) => inject(provider).resolve(...params);\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the router package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('19.2.14');\nexport { VERSION, mapToCanActivate, mapToCanActivateChild, mapToCanDeactivate, mapToCanMatch, mapToResolve };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,kBAAyK;AACzK,uBAAoL;AAQpL,IAAM,iBAAiB;AAMvB,IAAM,gBAA+B,OAAO,YAAY;AACxD,IAAM,cAAN,MAAkB;AAAA,EAChB;AAAA,EACA,YAAY,QAAQ;AAClB,SAAK,SAAS,UAAU,CAAC;AAAA,EAC3B;AAAA,EACA,IAAI,MAAM;AACR,WAAO,OAAO,UAAU,eAAe,KAAK,KAAK,QAAQ,IAAI;AAAA,EAC/D;AAAA,EACA,IAAI,MAAM;AACR,QAAI,KAAK,IAAI,IAAI,GAAG;AAClB,YAAM,IAAI,KAAK,OAAO,IAAI;AAC1B,aAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI;AAAA,IACnC;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,MAAM;AACX,QAAI,KAAK,IAAI,IAAI,GAAG;AAClB,YAAM,IAAI,KAAK,OAAO,IAAI;AAC1B,aAAO,MAAM,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC;AAAA,IAClC;AACA,WAAO,CAAC;AAAA,EACV;AAAA,EACA,IAAI,OAAO;AACT,WAAO,OAAO,KAAK,KAAK,MAAM;AAAA,EAChC;AACF;AAQA,SAAS,kBAAkB,QAAQ;AACjC,SAAO,IAAI,YAAY,MAAM;AAC/B;AAgBA,SAAS,kBAAkB,UAAU,cAAc,OAAO;AACxD,QAAM,QAAQ,MAAM,KAAK,MAAM,GAAG;AAClC,MAAI,MAAM,SAAS,SAAS,QAAQ;AAElC,WAAO;AAAA,EACT;AACA,MAAI,MAAM,cAAc,WAAW,aAAa,YAAY,KAAK,MAAM,SAAS,SAAS,SAAS;AAEhG,WAAO;AAAA,EACT;AACA,QAAM,YAAY,CAAC;AAEnB,WAAS,QAAQ,GAAG,QAAQ,MAAM,QAAQ,SAAS;AACjD,UAAM,OAAO,MAAM,KAAK;AACxB,UAAM,UAAU,SAAS,KAAK;AAC9B,UAAM,cAAc,KAAK,CAAC,MAAM;AAChC,QAAI,aAAa;AACf,gBAAU,KAAK,UAAU,CAAC,CAAC,IAAI;AAAA,IACjC,WAAW,SAAS,QAAQ,MAAM;AAEhC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AAAA,IACL,UAAU,SAAS,MAAM,GAAG,MAAM,MAAM;AAAA,IACxC;AAAA,EACF;AACF;AACA,SAAS,mBAAmB,GAAG,GAAG;AAChC,MAAI,EAAE,WAAW,EAAE,OAAQ,QAAO;AAClC,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAG;AACjC,QAAI,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAG,QAAO;AAAA,EACxC;AACA,SAAO;AACT;AACA,SAAS,aAAa,GAAG,GAAG;AAG1B,QAAM,KAAK,IAAI,YAAY,CAAC,IAAI;AAChC,QAAM,KAAK,IAAI,YAAY,CAAC,IAAI;AAChC,MAAI,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,GAAG,QAAQ;AACxC,WAAO;AAAA,EACT;AACA,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClC,UAAM,GAAG,CAAC;AACV,QAAI,CAAC,oBAAoB,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG;AACxC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAIA,SAAS,YAAY,KAAK;AACxB,SAAO,CAAC,GAAG,OAAO,KAAK,GAAG,GAAG,GAAG,OAAO,sBAAsB,GAAG,CAAC;AACnE;AAIA,SAAS,oBAAoB,GAAG,GAAG;AACjC,MAAI,MAAM,QAAQ,CAAC,KAAK,MAAM,QAAQ,CAAC,GAAG;AACxC,QAAI,EAAE,WAAW,EAAE,OAAQ,QAAO;AAClC,UAAM,UAAU,CAAC,GAAG,CAAC,EAAE,KAAK;AAC5B,UAAM,UAAU,CAAC,GAAG,CAAC,EAAE,KAAK;AAC5B,WAAO,QAAQ,MAAM,CAAC,KAAK,UAAU,QAAQ,KAAK,MAAM,GAAG;AAAA,EAC7D,OAAO;AACL,WAAO,MAAM;AAAA,EACf;AACF;AAIA,SAAS,KAAK,GAAG;AACf,SAAO,EAAE,SAAS,IAAI,EAAE,EAAE,SAAS,CAAC,IAAI;AAC1C;AACA,SAAS,mBAAmB,OAAO;AACjC,UAAI,0BAAa,KAAK,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,UAAW,KAAK,GAAG;AAIrB,eAAO,kBAAK,QAAQ,QAAQ,KAAK,CAAC;AAAA,EACpC;AACA,aAAO,gBAAG,KAAK;AACjB;AACA,IAAM,iBAAiB;AAAA,EACrB,SAAS;AAAA,EACT,UAAU;AACZ;AACA,IAAM,kBAAkB;AAAA,EACtB,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW,MAAM;AACnB;AACA,SAAS,aAAa,WAAW,WAAW,SAAS;AACnD,SAAO,eAAe,QAAQ,KAAK,EAAE,UAAU,MAAM,UAAU,MAAM,QAAQ,YAAY,KAAK,gBAAgB,QAAQ,WAAW,EAAE,UAAU,aAAa,UAAU,WAAW,KAAK,EAAE,QAAQ,aAAa,WAAW,UAAU,aAAa,UAAU;AACzP;AACA,SAAS,YAAY,WAAW,WAAW;AAEzC,SAAO,aAAa,WAAW,SAAS;AAC1C;AACA,SAAS,mBAAmB,WAAW,WAAW,cAAc;AAC9D,MAAI,CAAC,UAAU,UAAU,UAAU,UAAU,QAAQ,EAAG,QAAO;AAC/D,MAAI,CAAC,kBAAkB,UAAU,UAAU,UAAU,UAAU,YAAY,GAAG;AAC5E,WAAO;AAAA,EACT;AACA,MAAI,UAAU,qBAAqB,UAAU,iBAAkB,QAAO;AACtE,aAAW,KAAK,UAAU,UAAU;AAClC,QAAI,CAAC,UAAU,SAAS,CAAC,EAAG,QAAO;AACnC,QAAI,CAAC,mBAAmB,UAAU,SAAS,CAAC,GAAG,UAAU,SAAS,CAAC,GAAG,YAAY,EAAG,QAAO;AAAA,EAC9F;AACA,SAAO;AACT;AACA,SAAS,eAAe,WAAW,WAAW;AAC5C,SAAO,OAAO,KAAK,SAAS,EAAE,UAAU,OAAO,KAAK,SAAS,EAAE,UAAU,OAAO,KAAK,SAAS,EAAE,MAAM,SAAO,oBAAoB,UAAU,GAAG,GAAG,UAAU,GAAG,CAAC,CAAC;AAClK;AACA,SAAS,qBAAqB,WAAW,WAAW,cAAc;AAChE,SAAO,2BAA2B,WAAW,WAAW,UAAU,UAAU,YAAY;AAC1F;AACA,SAAS,2BAA2B,WAAW,WAAW,gBAAgB,cAAc;AACtF,MAAI,UAAU,SAAS,SAAS,eAAe,QAAQ;AACrD,UAAM,UAAU,UAAU,SAAS,MAAM,GAAG,eAAe,MAAM;AACjE,QAAI,CAAC,UAAU,SAAS,cAAc,EAAG,QAAO;AAChD,QAAI,UAAU,YAAY,EAAG,QAAO;AACpC,QAAI,CAAC,kBAAkB,SAAS,gBAAgB,YAAY,EAAG,QAAO;AACtE,WAAO;AAAA,EACT,WAAW,UAAU,SAAS,WAAW,eAAe,QAAQ;AAC9D,QAAI,CAAC,UAAU,UAAU,UAAU,cAAc,EAAG,QAAO;AAC3D,QAAI,CAAC,kBAAkB,UAAU,UAAU,gBAAgB,YAAY,EAAG,QAAO;AACjF,eAAW,KAAK,UAAU,UAAU;AAClC,UAAI,CAAC,UAAU,SAAS,CAAC,EAAG,QAAO;AACnC,UAAI,CAAC,qBAAqB,UAAU,SAAS,CAAC,GAAG,UAAU,SAAS,CAAC,GAAG,YAAY,GAAG;AACrF,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT,OAAO;AACL,UAAM,UAAU,eAAe,MAAM,GAAG,UAAU,SAAS,MAAM;AACjE,UAAM,OAAO,eAAe,MAAM,UAAU,SAAS,MAAM;AAC3D,QAAI,CAAC,UAAU,UAAU,UAAU,OAAO,EAAG,QAAO;AACpD,QAAI,CAAC,kBAAkB,UAAU,UAAU,SAAS,YAAY,EAAG,QAAO;AAC1E,QAAI,CAAC,UAAU,SAAS,cAAc,EAAG,QAAO;AAChD,WAAO,2BAA2B,UAAU,SAAS,cAAc,GAAG,WAAW,MAAM,YAAY;AAAA,EACrG;AACF;AACA,SAAS,kBAAkB,gBAAgB,gBAAgB,SAAS;AAClE,SAAO,eAAe,MAAM,CAAC,kBAAkB,MAAM;AACnD,WAAO,gBAAgB,OAAO,EAAE,eAAe,CAAC,EAAE,YAAY,iBAAiB,UAAU;AAAA,EAC3F,CAAC;AACH;AA+BA,IAAM,UAAN,MAAc;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA,YACA,OAAO,IAAI,gBAAgB,CAAC,GAAG,CAAC,CAAC,GACjC,cAAc,CAAC,GACf,WAAW,MAAM;AACf,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,KAAK,SAAS,SAAS,GAAG;AAC5B,cAAM,IAAI,aAAc,MAAsD,2JAAgK;AAAA,MAChP;AAAA,IACF;AAAA,EACF;AAAA,EACA,IAAI,gBAAgB;AAClB,SAAK,mBAAmB,kBAAkB,KAAK,WAAW;AAC1D,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,mBAAmB,UAAU,IAAI;AAAA,EAC1C;AACF;AAUA,IAAM,kBAAN,MAAsB;AAAA,EACpB;AAAA,EACA;AAAA;AAAA,EAEA,SAAS;AAAA,EACT,YACA,UACA,UAAU;AACR,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,WAAO,OAAO,QAAQ,EAAE,QAAQ,OAAK,EAAE,SAAS,IAAI;AAAA,EACtD;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA;AAAA,EAEA,IAAI,mBAAmB;AACrB,WAAO,OAAO,KAAK,KAAK,QAAQ,EAAE;AAAA,EACpC;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,eAAe,IAAI;AAAA,EAC5B;AACF;AA2BA,IAAM,aAAN,MAAiB;AAAA,EACf;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA,YACA,MACA,YAAY;AACV,SAAK,OAAO;AACZ,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,IAAI,eAAe;AACjB,SAAK,kBAAkB,kBAAkB,KAAK,UAAU;AACxD,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,cAAc,IAAI;AAAA,EAC3B;AACF;AACA,SAAS,cAAc,IAAI,IAAI;AAC7B,SAAO,UAAU,IAAI,EAAE,KAAK,GAAG,MAAM,CAAC,GAAG,MAAM,aAAa,EAAE,YAAY,GAAG,CAAC,EAAE,UAAU,CAAC;AAC7F;AACA,SAAS,UAAU,IAAI,IAAI;AACzB,MAAI,GAAG,WAAW,GAAG,OAAQ,QAAO;AACpC,SAAO,GAAG,MAAM,CAAC,GAAG,MAAM,EAAE,SAAS,GAAG,CAAC,EAAE,IAAI;AACjD;AACA,SAAS,qBAAqB,SAAS,IAAI;AACzC,MAAI,MAAM,CAAC;AACX,SAAO,QAAQ,QAAQ,QAAQ,EAAE,QAAQ,CAAC,CAAC,aAAa,KAAK,MAAM;AACjE,QAAI,gBAAgB,gBAAgB;AAClC,YAAM,IAAI,OAAO,GAAG,OAAO,WAAW,CAAC;AAAA,IACzC;AAAA,EACF,CAAC;AACD,SAAO,QAAQ,QAAQ,QAAQ,EAAE,QAAQ,CAAC,CAAC,aAAa,KAAK,MAAM;AACjE,QAAI,gBAAgB,gBAAgB;AAClC,YAAM,IAAI,OAAO,GAAG,OAAO,WAAW,CAAC;AAAA,IACzC;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAaA,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,OAAO,MAAM,IAAI,qBAAqB,GAAG;AAAA,IAClD,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,YAAY,MAAM,IAAI,qBAAqB;AAAA,IAC7C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAmBH,IAAM,uBAAN,MAA2B;AAAA;AAAA,EAEzB,MAAM,KAAK;AACT,UAAM,IAAI,IAAI,UAAU,GAAG;AAC3B,WAAO,IAAI,QAAQ,EAAE,iBAAiB,GAAG,EAAE,iBAAiB,GAAG,EAAE,cAAc,CAAC;AAAA,EAClF;AAAA;AAAA,EAEA,UAAUA,OAAM;AACd,UAAM,UAAU,IAAI,iBAAiBA,MAAK,MAAM,IAAI,CAAC;AACrD,UAAM,QAAQ,qBAAqBA,MAAK,WAAW;AACnD,UAAM,WAAW,OAAOA,MAAK,aAAa,WAAW,IAAI,kBAAkBA,MAAK,QAAQ,CAAC,KAAK;AAC9F,WAAO,GAAG,OAAO,GAAG,KAAK,GAAG,QAAQ;AAAA,EACtC;AACF;AACA,IAAM,qBAAqB,IAAI,qBAAqB;AACpD,SAAS,eAAe,SAAS;AAC/B,SAAO,QAAQ,SAAS,IAAI,OAAK,cAAc,CAAC,CAAC,EAAE,KAAK,GAAG;AAC7D;AACA,SAAS,iBAAiB,SAAS,MAAM;AACvC,MAAI,CAAC,QAAQ,YAAY,GAAG;AAC1B,WAAO,eAAe,OAAO;AAAA,EAC/B;AACA,MAAI,MAAM;AACR,UAAM,UAAU,QAAQ,SAAS,cAAc,IAAI,iBAAiB,QAAQ,SAAS,cAAc,GAAG,KAAK,IAAI;AAC/G,UAAM,WAAW,CAAC;AAClB,WAAO,QAAQ,QAAQ,QAAQ,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM;AACnD,UAAI,MAAM,gBAAgB;AACxB,iBAAS,KAAK,GAAG,CAAC,IAAI,iBAAiB,GAAG,KAAK,CAAC,EAAE;AAAA,MACpD;AAAA,IACF,CAAC;AACD,WAAO,SAAS,SAAS,IAAI,GAAG,OAAO,IAAI,SAAS,KAAK,IAAI,CAAC,MAAM;AAAA,EACtE,OAAO;AACL,UAAM,WAAW,qBAAqB,SAAS,CAAC,GAAG,MAAM;AACvD,UAAI,MAAM,gBAAgB;AACxB,eAAO,CAAC,iBAAiB,QAAQ,SAAS,cAAc,GAAG,KAAK,CAAC;AAAA,MACnE;AACA,aAAO,CAAC,GAAG,CAAC,IAAI,iBAAiB,GAAG,KAAK,CAAC,EAAE;AAAA,IAC9C,CAAC;AAED,QAAI,OAAO,KAAK,QAAQ,QAAQ,EAAE,WAAW,KAAK,QAAQ,SAAS,cAAc,KAAK,MAAM;AAC1F,aAAO,GAAG,eAAe,OAAO,CAAC,IAAI,SAAS,CAAC,CAAC;AAAA,IAClD;AACA,WAAO,GAAG,eAAe,OAAO,CAAC,KAAK,SAAS,KAAK,IAAI,CAAC;AAAA,EAC3D;AACF;AAOA,SAAS,gBAAgB,GAAG;AAC1B,SAAO,mBAAmB,CAAC,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,SAAS,GAAG,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,SAAS,GAAG;AACnH;AAOA,SAAS,eAAe,GAAG;AACzB,SAAO,gBAAgB,CAAC,EAAE,QAAQ,SAAS,GAAG;AAChD;AAOA,SAAS,kBAAkB,GAAG;AAC5B,SAAO,UAAU,CAAC;AACpB;AAQA,SAAS,iBAAiB,GAAG;AAC3B,SAAO,gBAAgB,CAAC,EAAE,QAAQ,OAAO,KAAK,EAAE,QAAQ,OAAO,KAAK,EAAE,QAAQ,SAAS,GAAG;AAC5F;AACA,SAAS,OAAO,GAAG;AACjB,SAAO,mBAAmB,CAAC;AAC7B;AAGA,SAAS,YAAY,GAAG;AACtB,SAAO,OAAO,EAAE,QAAQ,OAAO,KAAK,CAAC;AACvC;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,GAAG,iBAAiB,KAAK,IAAI,CAAC,GAAG,sBAAsB,KAAK,UAAU,CAAC;AAChF;AACA,SAAS,sBAAsB,QAAQ;AACrC,SAAO,OAAO,QAAQ,MAAM,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,IAAI,iBAAiB,GAAG,CAAC,IAAI,iBAAiB,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE;AACrH;AACA,SAAS,qBAAqB,QAAQ;AACpC,QAAM,YAAY,OAAO,QAAQ,MAAM,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM;AAC9D,WAAO,MAAM,QAAQ,KAAK,IAAI,MAAM,IAAI,OAAK,GAAG,eAAe,IAAI,CAAC,IAAI,eAAe,CAAC,CAAC,EAAE,EAAE,KAAK,GAAG,IAAI,GAAG,eAAe,IAAI,CAAC,IAAI,eAAe,KAAK,CAAC;AAAA,EAC3J,CAAC,EAAE,OAAO,OAAK,CAAC;AAChB,SAAO,UAAU,SAAS,IAAI,UAAU,KAAK,GAAG,CAAC,KAAK;AACxD;AACA,IAAM,aAAa;AACnB,SAAS,cAAc,KAAK;AAC1B,QAAMC,SAAQ,IAAI,MAAM,UAAU;AAClC,SAAOA,SAAQA,OAAM,CAAC,IAAI;AAC5B;AACA,IAAM,0BAA0B;AAChC,SAAS,uBAAuB,KAAK;AACnC,QAAMA,SAAQ,IAAI,MAAM,uBAAuB;AAC/C,SAAOA,SAAQA,OAAM,CAAC,IAAI;AAC5B;AACA,IAAM,iBAAiB;AAEvB,SAAS,iBAAiB,KAAK;AAC7B,QAAMA,SAAQ,IAAI,MAAM,cAAc;AACtC,SAAOA,SAAQA,OAAM,CAAC,IAAI;AAC5B;AACA,IAAM,uBAAuB;AAE7B,SAAS,wBAAwB,KAAK;AACpC,QAAMA,SAAQ,IAAI,MAAM,oBAAoB;AAC5C,SAAOA,SAAQA,OAAM,CAAC,IAAI;AAC5B;AACA,IAAM,YAAN,MAAgB;AAAA,EACd;AAAA,EACA;AAAA,EACA,YAAY,KAAK;AACf,SAAK,MAAM;AACX,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,mBAAmB;AACjB,SAAK,gBAAgB,GAAG;AACxB,QAAI,KAAK,cAAc,MAAM,KAAK,eAAe,GAAG,KAAK,KAAK,eAAe,GAAG,GAAG;AACjF,aAAO,IAAI,gBAAgB,CAAC,GAAG,CAAC,CAAC;AAAA,IACnC;AAEA,WAAO,IAAI,gBAAgB,CAAC,GAAG,KAAK,cAAc,CAAC;AAAA,EACrD;AAAA,EACA,mBAAmB;AACjB,UAAM,SAAS,CAAC;AAChB,QAAI,KAAK,gBAAgB,GAAG,GAAG;AAC7B,SAAG;AACD,aAAK,gBAAgB,MAAM;AAAA,MAC7B,SAAS,KAAK,gBAAgB,GAAG;AAAA,IACnC;AACA,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK,gBAAgB,GAAG,IAAI,mBAAmB,KAAK,SAAS,IAAI;AAAA,EAC1E;AAAA,EACA,gBAAgB;AACd,QAAI,KAAK,cAAc,IAAI;AACzB,aAAO,CAAC;AAAA,IACV;AACA,SAAK,gBAAgB,GAAG;AACxB,UAAM,WAAW,CAAC;AAClB,QAAI,CAAC,KAAK,eAAe,GAAG,GAAG;AAC7B,eAAS,KAAK,KAAK,aAAa,CAAC;AAAA,IACnC;AACA,WAAO,KAAK,eAAe,GAAG,KAAK,CAAC,KAAK,eAAe,IAAI,KAAK,CAAC,KAAK,eAAe,IAAI,GAAG;AAC3F,WAAK,QAAQ,GAAG;AAChB,eAAS,KAAK,KAAK,aAAa,CAAC;AAAA,IACnC;AACA,QAAI,WAAW,CAAC;AAChB,QAAI,KAAK,eAAe,IAAI,GAAG;AAC7B,WAAK,QAAQ,GAAG;AAChB,iBAAW,KAAK,YAAY,IAAI;AAAA,IAClC;AACA,QAAI,MAAM,CAAC;AACX,QAAI,KAAK,eAAe,GAAG,GAAG;AAC5B,YAAM,KAAK,YAAY,KAAK;AAAA,IAC9B;AACA,QAAI,SAAS,SAAS,KAAK,OAAO,KAAK,QAAQ,EAAE,SAAS,GAAG;AAC3D,UAAI,cAAc,IAAI,IAAI,gBAAgB,UAAU,QAAQ;AAAA,IAC9D;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA,EAGA,eAAe;AACb,UAAM,OAAO,cAAc,KAAK,SAAS;AACzC,QAAI,SAAS,MAAM,KAAK,eAAe,GAAG,GAAG;AAC3C,YAAM,IAAI,aAAc,OAAqD,OAAO,cAAc,eAAe,cAAc,mDAAmD,KAAK,SAAS,IAAI;AAAA,IACtM;AACA,SAAK,QAAQ,IAAI;AACjB,WAAO,IAAI,WAAW,OAAO,IAAI,GAAG,KAAK,kBAAkB,CAAC;AAAA,EAC9D;AAAA,EACA,oBAAoB;AAClB,UAAM,SAAS,CAAC;AAChB,WAAO,KAAK,gBAAgB,GAAG,GAAG;AAChC,WAAK,WAAW,MAAM;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW,QAAQ;AACjB,UAAM,MAAM,uBAAuB,KAAK,SAAS;AACjD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,SAAK,QAAQ,GAAG;AAChB,QAAI,QAAQ;AACZ,QAAI,KAAK,gBAAgB,GAAG,GAAG;AAC7B,YAAM,aAAa,cAAc,KAAK,SAAS;AAC/C,UAAI,YAAY;AACd,gBAAQ;AACR,aAAK,QAAQ,KAAK;AAAA,MACpB;AAAA,IACF;AACA,WAAO,OAAO,GAAG,CAAC,IAAI,OAAO,KAAK;AAAA,EACpC;AAAA;AAAA,EAEA,gBAAgB,QAAQ;AACtB,UAAM,MAAM,iBAAiB,KAAK,SAAS;AAC3C,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,SAAK,QAAQ,GAAG;AAChB,QAAI,QAAQ;AACZ,QAAI,KAAK,gBAAgB,GAAG,GAAG;AAC7B,YAAM,aAAa,wBAAwB,KAAK,SAAS;AACzD,UAAI,YAAY;AACd,gBAAQ;AACR,aAAK,QAAQ,KAAK;AAAA,MACpB;AAAA,IACF;AACA,UAAM,aAAa,YAAY,GAAG;AAClC,UAAM,aAAa,YAAY,KAAK;AACpC,QAAI,OAAO,eAAe,UAAU,GAAG;AAErC,UAAI,aAAa,OAAO,UAAU;AAClC,UAAI,CAAC,MAAM,QAAQ,UAAU,GAAG;AAC9B,qBAAa,CAAC,UAAU;AACxB,eAAO,UAAU,IAAI;AAAA,MACvB;AACA,iBAAW,KAAK,UAAU;AAAA,IAC5B,OAAO;AAEL,aAAO,UAAU,IAAI;AAAA,IACvB;AAAA,EACF;AAAA;AAAA,EAEA,YAAY,cAAc;AACxB,UAAM,WAAW,CAAC;AAClB,SAAK,QAAQ,GAAG;AAChB,WAAO,CAAC,KAAK,gBAAgB,GAAG,KAAK,KAAK,UAAU,SAAS,GAAG;AAC9D,YAAM,OAAO,cAAc,KAAK,SAAS;AACzC,YAAM,OAAO,KAAK,UAAU,KAAK,MAAM;AAGvC,UAAI,SAAS,OAAO,SAAS,OAAO,SAAS,KAAK;AAChD,cAAM,IAAI,aAAc,OAA6C,OAAO,cAAc,eAAe,cAAc,qBAAqB,KAAK,GAAG,GAAG;AAAA,MACzJ;AACA,UAAI,aAAa;AACjB,UAAI,KAAK,QAAQ,GAAG,IAAI,IAAI;AAC1B,qBAAa,KAAK,MAAM,GAAG,KAAK,QAAQ,GAAG,CAAC;AAC5C,aAAK,QAAQ,UAAU;AACvB,aAAK,QAAQ,GAAG;AAAA,MAClB,WAAW,cAAc;AACvB,qBAAa;AAAA,MACf;AACA,YAAM,WAAW,KAAK,cAAc;AACpC,eAAS,UAAU,IAAI,OAAO,KAAK,QAAQ,EAAE,WAAW,IAAI,SAAS,cAAc,IAAI,IAAI,gBAAgB,CAAC,GAAG,QAAQ;AACvH,WAAK,gBAAgB,IAAI;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AAAA,EACA,eAAe,KAAK;AAClB,WAAO,KAAK,UAAU,WAAW,GAAG;AAAA,EACtC;AAAA;AAAA,EAEA,gBAAgB,KAAK;AACnB,QAAI,KAAK,eAAe,GAAG,GAAG;AAC5B,WAAK,YAAY,KAAK,UAAU,UAAU,IAAI,MAAM;AACpD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,KAAK;AACX,QAAI,CAAC,KAAK,gBAAgB,GAAG,GAAG;AAC9B,YAAM,IAAI,aAAc,OAAsD,OAAO,cAAc,eAAe,cAAc,aAAa,GAAG,IAAI;AAAA,IACtJ;AAAA,EACF;AACF;AACA,SAAS,WAAW,eAAe;AACjC,SAAO,cAAc,SAAS,SAAS,IAAI,IAAI,gBAAgB,CAAC,GAAG;AAAA,IACjE,CAAC,cAAc,GAAG;AAAA,EACpB,CAAC,IAAI;AACP;AAWA,SAAS,mBAAmB,cAAc;AACxC,QAAM,cAAc,CAAC;AACrB,aAAW,CAAC,aAAa,KAAK,KAAK,OAAO,QAAQ,aAAa,QAAQ,GAAG;AACxE,UAAM,iBAAiB,mBAAmB,KAAK;AAE/C,QAAI,gBAAgB,kBAAkB,eAAe,SAAS,WAAW,KAAK,eAAe,YAAY,GAAG;AAC1G,iBAAW,CAAC,kBAAkB,UAAU,KAAK,OAAO,QAAQ,eAAe,QAAQ,GAAG;AACpF,oBAAY,gBAAgB,IAAI;AAAA,MAClC;AAAA,IACF,WACS,eAAe,SAAS,SAAS,KAAK,eAAe,YAAY,GAAG;AAC3E,kBAAY,WAAW,IAAI;AAAA,IAC7B;AAAA,EACF;AACA,QAAM,IAAI,IAAI,gBAAgB,aAAa,UAAU,WAAW;AAChE,SAAO,qBAAqB,CAAC;AAC/B;AASA,SAAS,qBAAqB,GAAG;AAC/B,MAAI,EAAE,qBAAqB,KAAK,EAAE,SAAS,cAAc,GAAG;AAC1D,UAAM,IAAI,EAAE,SAAS,cAAc;AACnC,WAAO,IAAI,gBAAgB,EAAE,SAAS,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ;AAAA,EACtE;AACA,SAAO;AACT;AACA,SAAS,UAAU,GAAG;AACpB,SAAO,aAAa;AACtB;AAqDA,SAAS,0BAA0B,YAAY,UAAU,cAAc,MAAM,WAAW,MAAM;AAC5F,QAAM,4BAA4B,4BAA4B,UAAU;AACxE,SAAO,8BAA8B,2BAA2B,UAAU,aAAa,QAAQ;AACjG;AACA,SAAS,4BAA4B,OAAO;AAC1C,MAAI;AACJ,WAAS,qCAAqC,cAAc;AAC1D,UAAM,eAAe,CAAC;AACtB,eAAW,iBAAiB,aAAa,UAAU;AACjD,YAAM,OAAO,qCAAqC,aAAa;AAC/D,mBAAa,cAAc,MAAM,IAAI;AAAA,IACvC;AACA,UAAM,eAAe,IAAI,gBAAgB,aAAa,KAAK,YAAY;AACvE,QAAI,iBAAiB,OAAO;AAC1B,oBAAc;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AACA,QAAM,gBAAgB,qCAAqC,MAAM,IAAI;AACrE,QAAM,mBAAmB,WAAW,aAAa;AACjD,SAAO,eAAe;AACxB;AACA,SAAS,8BAA8B,YAAY,UAAU,aAAa,UAAU;AAClF,MAAI,OAAO;AACX,SAAO,KAAK,QAAQ;AAClB,WAAO,KAAK;AAAA,EACd;AAIA,MAAI,SAAS,WAAW,GAAG;AACzB,WAAO,KAAK,MAAM,MAAM,MAAM,aAAa,QAAQ;AAAA,EACrD;AACA,QAAM,MAAM,kBAAkB,QAAQ;AACtC,MAAI,IAAI,OAAO,GAAG;AAChB,WAAO,KAAK,MAAM,MAAM,IAAI,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa,QAAQ;AAAA,EAC5E;AACA,QAAM,WAAW,mCAAmC,KAAK,MAAM,UAAU;AACzE,QAAM,kBAAkB,SAAS,kBAAkB,2BAA2B,SAAS,cAAc,SAAS,OAAO,IAAI,QAAQ,IAAI,mBAAmB,SAAS,cAAc,SAAS,OAAO,IAAI,QAAQ;AAC3M,SAAO,KAAK,MAAM,SAAS,cAAc,iBAAiB,aAAa,QAAQ;AACjF;AACA,SAAS,eAAe,SAAS;AAC/B,SAAO,OAAO,YAAY,YAAY,WAAW,QAAQ,CAAC,QAAQ,WAAW,CAAC,QAAQ;AACxF;AAKA,SAAS,qBAAqB,SAAS;AACrC,SAAO,OAAO,YAAY,YAAY,WAAW,QAAQ,QAAQ;AACnE;AACA,SAAS,KAAK,SAAS,iBAAiB,iBAAiB,aAAa,UAAU;AAC9E,MAAI,KAAK,CAAC;AACV,MAAI,aAAa;AACf,WAAO,QAAQ,WAAW,EAAE,QAAQ,CAAC,CAAC,MAAM,KAAK,MAAM;AACrD,SAAG,IAAI,IAAI,MAAM,QAAQ,KAAK,IAAI,MAAM,IAAI,OAAK,GAAG,CAAC,EAAE,IAAI,GAAG,KAAK;AAAA,IACrE,CAAC;AAAA,EACH;AACA,MAAI;AACJ,MAAI,YAAY,iBAAiB;AAC/B,oBAAgB;AAAA,EAClB,OAAO;AACL,oBAAgB,eAAe,SAAS,iBAAiB,eAAe;AAAA,EAC1E;AACA,QAAM,UAAU,WAAW,mBAAmB,aAAa,CAAC;AAC5D,SAAO,IAAI,QAAQ,SAAS,IAAI,QAAQ;AAC1C;AAQA,SAAS,eAAe,SAAS,YAAY,YAAY;AACvD,QAAM,WAAW,CAAC;AAClB,SAAO,QAAQ,QAAQ,QAAQ,EAAE,QAAQ,CAAC,CAAC,YAAY,CAAC,MAAM;AAC5D,QAAI,MAAM,YAAY;AACpB,eAAS,UAAU,IAAI;AAAA,IACzB,OAAO;AACL,eAAS,UAAU,IAAI,eAAe,GAAG,YAAY,UAAU;AAAA,IACjE;AAAA,EACF,CAAC;AACD,SAAO,IAAI,gBAAgB,QAAQ,UAAU,QAAQ;AACvD;AACA,IAAM,aAAN,MAAiB;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,YAAY,oBAAoB,UAAU;AACpD,SAAK,aAAa;AAClB,SAAK,qBAAqB;AAC1B,SAAK,WAAW;AAChB,QAAI,cAAc,SAAS,SAAS,KAAK,eAAe,SAAS,CAAC,CAAC,GAAG;AACpE,YAAM,IAAI,aAAc,OAAyD,OAAO,cAAc,eAAe,cAAc,4CAA4C;AAAA,IACjL;AACA,UAAM,gBAAgB,SAAS,KAAK,oBAAoB;AACxD,QAAI,iBAAiB,kBAAkB,KAAK,QAAQ,GAAG;AACrD,YAAM,IAAI,aAAc,OAAwD,OAAO,cAAc,eAAe,cAAc,yCAAyC;AAAA,IAC7K;AAAA,EACF;AAAA,EACA,SAAS;AACP,WAAO,KAAK,cAAc,KAAK,SAAS,WAAW,KAAK,KAAK,SAAS,CAAC,KAAK;AAAA,EAC9E;AACF;AAEA,SAAS,kBAAkB,UAAU;AACnC,MAAI,OAAO,SAAS,CAAC,MAAM,YAAY,SAAS,WAAW,KAAK,SAAS,CAAC,MAAM,KAAK;AACnF,WAAO,IAAI,WAAW,MAAM,GAAG,QAAQ;AAAA,EACzC;AACA,MAAI,qBAAqB;AACzB,MAAI,aAAa;AACjB,QAAM,MAAM,SAAS,OAAO,CAACC,MAAK,KAAK,WAAW;AAChD,QAAI,OAAO,QAAQ,YAAY,OAAO,MAAM;AAC1C,UAAI,IAAI,SAAS;AACf,cAAM,UAAU,CAAC;AACjB,eAAO,QAAQ,IAAI,OAAO,EAAE,QAAQ,CAAC,CAAC,MAAMC,SAAQ,MAAM;AACxD,kBAAQ,IAAI,IAAI,OAAOA,cAAa,WAAWA,UAAS,MAAM,GAAG,IAAIA;AAAA,QACvE,CAAC;AACD,eAAO,CAAC,GAAGD,MAAK;AAAA,UACd;AAAA,QACF,CAAC;AAAA,MACH;AACA,UAAI,IAAI,aAAa;AACnB,eAAO,CAAC,GAAGA,MAAK,IAAI,WAAW;AAAA,MACjC;AAAA,IACF;AACA,QAAI,EAAE,OAAO,QAAQ,WAAW;AAC9B,aAAO,CAAC,GAAGA,MAAK,GAAG;AAAA,IACrB;AACA,QAAI,WAAW,GAAG;AAChB,UAAI,MAAM,GAAG,EAAE,QAAQ,CAAC,SAAS,cAAc;AAC7C,YAAI,aAAa,KAAK,YAAY,IAAK;AAAA,iBAAU,aAAa,KAAK,YAAY,IAAI;AAEjF,uBAAa;AAAA,QACf,WAAW,YAAY,MAAM;AAE3B;AAAA,QACF,WAAW,WAAW,IAAI;AACxB,UAAAA,KAAI,KAAK,OAAO;AAAA,QAClB;AAAA,MACF,CAAC;AACD,aAAOA;AAAA,IACT;AACA,WAAO,CAAC,GAAGA,MAAK,GAAG;AAAA,EACrB,GAAG,CAAC,CAAC;AACL,SAAO,IAAI,WAAW,YAAY,oBAAoB,GAAG;AAC3D;AACA,IAAM,WAAN,MAAe;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,cAAc,iBAAiB,OAAO;AAChD,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,QAAQ;AAAA,EACf;AACF;AACA,SAAS,mCAAmC,KAAK,MAAM,QAAQ;AAC7D,MAAI,IAAI,YAAY;AAClB,WAAO,IAAI,SAAS,MAAM,MAAM,CAAC;AAAA,EACnC;AACA,MAAI,CAAC,QAAQ;AAKX,WAAO,IAAI,SAAS,MAAM,OAAO,GAAG;AAAA,EACtC;AACA,MAAI,OAAO,WAAW,MAAM;AAC1B,WAAO,IAAI,SAAS,QAAQ,MAAM,CAAC;AAAA,EACrC;AACA,QAAM,WAAW,eAAe,IAAI,SAAS,CAAC,CAAC,IAAI,IAAI;AACvD,QAAM,QAAQ,OAAO,SAAS,SAAS,IAAI;AAC3C,SAAO,iCAAiC,QAAQ,OAAO,IAAI,kBAAkB;AAC/E;AACA,SAAS,iCAAiC,OAAO,OAAO,oBAAoB;AAC1E,MAAI,IAAI;AACR,MAAI,KAAK;AACT,MAAI,KAAK;AACT,SAAO,KAAK,IAAI;AACd,UAAM;AACN,QAAI,EAAE;AACN,QAAI,CAAC,GAAG;AACN,YAAM,IAAI,aAAc,OAAkD,OAAO,cAAc,eAAe,cAAc,yBAAyB;AAAA,IACvJ;AACA,SAAK,EAAE,SAAS;AAAA,EAClB;AACA,SAAO,IAAI,SAAS,GAAG,OAAO,KAAK,EAAE;AACvC;AACA,SAAS,WAAW,UAAU;AAC5B,MAAI,qBAAqB,SAAS,CAAC,CAAC,GAAG;AACrC,WAAO,SAAS,CAAC,EAAE;AAAA,EACrB;AACA,SAAO;AAAA,IACL,CAAC,cAAc,GAAG;AAAA,EACpB;AACF;AACA,SAAS,mBAAmB,cAAc,YAAY,UAAU;AAC9D,mBAAiB,IAAI,gBAAgB,CAAC,GAAG,CAAC,CAAC;AAC3C,MAAI,aAAa,SAAS,WAAW,KAAK,aAAa,YAAY,GAAG;AACpE,WAAO,2BAA2B,cAAc,YAAY,QAAQ;AAAA,EACtE;AACA,QAAM,IAAI,aAAa,cAAc,YAAY,QAAQ;AACzD,QAAM,iBAAiB,SAAS,MAAM,EAAE,YAAY;AACpD,MAAI,EAAE,SAAS,EAAE,YAAY,aAAa,SAAS,QAAQ;AACzD,UAAM,IAAI,IAAI,gBAAgB,aAAa,SAAS,MAAM,GAAG,EAAE,SAAS,GAAG,CAAC,CAAC;AAC7E,MAAE,SAAS,cAAc,IAAI,IAAI,gBAAgB,aAAa,SAAS,MAAM,EAAE,SAAS,GAAG,aAAa,QAAQ;AAChH,WAAO,2BAA2B,GAAG,GAAG,cAAc;AAAA,EACxD,WAAW,EAAE,SAAS,eAAe,WAAW,GAAG;AACjD,WAAO,IAAI,gBAAgB,aAAa,UAAU,CAAC,CAAC;AAAA,EACtD,WAAW,EAAE,SAAS,CAAC,aAAa,YAAY,GAAG;AACjD,WAAO,sBAAsB,cAAc,YAAY,QAAQ;AAAA,EACjE,WAAW,EAAE,OAAO;AAClB,WAAO,2BAA2B,cAAc,GAAG,cAAc;AAAA,EACnE,OAAO;AACL,WAAO,sBAAsB,cAAc,YAAY,QAAQ;AAAA,EACjE;AACF;AACA,SAAS,2BAA2B,cAAc,YAAY,UAAU;AACtE,MAAI,SAAS,WAAW,GAAG;AACzB,WAAO,IAAI,gBAAgB,aAAa,UAAU,CAAC,CAAC;AAAA,EACtD,OAAO;AACL,UAAM,UAAU,WAAW,QAAQ;AACnC,UAAM,WAAW,CAAC;AAsBlB,QAAI,OAAO,KAAK,OAAO,EAAE,KAAK,OAAK,MAAM,cAAc,KAAK,aAAa,SAAS,cAAc,KAAK,aAAa,qBAAqB,KAAK,aAAa,SAAS,cAAc,EAAE,SAAS,WAAW,GAAG;AACvM,YAAM,uBAAuB,2BAA2B,aAAa,SAAS,cAAc,GAAG,YAAY,QAAQ;AACnH,aAAO,IAAI,gBAAgB,aAAa,UAAU,qBAAqB,QAAQ;AAAA,IACjF;AACA,WAAO,QAAQ,OAAO,EAAE,QAAQ,CAAC,CAAC,QAAQC,SAAQ,MAAM;AACtD,UAAI,OAAOA,cAAa,UAAU;AAChC,QAAAA,YAAW,CAACA,SAAQ;AAAA,MACtB;AACA,UAAIA,cAAa,MAAM;AACrB,iBAAS,MAAM,IAAI,mBAAmB,aAAa,SAAS,MAAM,GAAG,YAAYA,SAAQ;AAAA,MAC3F;AAAA,IACF,CAAC;AACD,WAAO,QAAQ,aAAa,QAAQ,EAAE,QAAQ,CAAC,CAAC,aAAa,KAAK,MAAM;AACtE,UAAI,QAAQ,WAAW,MAAM,QAAW;AACtC,iBAAS,WAAW,IAAI;AAAA,MAC1B;AAAA,IACF,CAAC;AACD,WAAO,IAAI,gBAAgB,aAAa,UAAU,QAAQ;AAAA,EAC5D;AACF;AACA,SAAS,aAAa,cAAc,YAAY,UAAU;AACxD,MAAI,sBAAsB;AAC1B,MAAI,mBAAmB;AACvB,QAAMC,WAAU;AAAA,IACd,OAAO;AAAA,IACP,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AACA,SAAO,mBAAmB,aAAa,SAAS,QAAQ;AACtD,QAAI,uBAAuB,SAAS,OAAQ,QAAOA;AACnD,UAAM,OAAO,aAAa,SAAS,gBAAgB;AACnD,UAAM,UAAU,SAAS,mBAAmB;AAI5C,QAAI,qBAAqB,OAAO,GAAG;AACjC;AAAA,IACF;AACA,UAAM,OAAO,GAAG,OAAO;AACvB,UAAM,OAAO,sBAAsB,SAAS,SAAS,IAAI,SAAS,sBAAsB,CAAC,IAAI;AAC7F,QAAI,mBAAmB,KAAK,SAAS,OAAW;AAChD,QAAI,QAAQ,QAAQ,OAAO,SAAS,YAAY,KAAK,YAAY,QAAW;AAC1E,UAAI,CAAC,QAAQ,MAAM,MAAM,IAAI,EAAG,QAAOA;AACvC,6BAAuB;AAAA,IACzB,OAAO;AACL,UAAI,CAAC,QAAQ,MAAM,CAAC,GAAG,IAAI,EAAG,QAAOA;AACrC;AAAA,IACF;AACA;AAAA,EACF;AACA,SAAO;AAAA,IACL,OAAO;AAAA,IACP,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AACF;AACA,SAAS,sBAAsB,cAAc,YAAY,UAAU;AACjE,QAAM,QAAQ,aAAa,SAAS,MAAM,GAAG,UAAU;AACvD,MAAI,IAAI;AACR,SAAO,IAAI,SAAS,QAAQ;AAC1B,UAAM,UAAU,SAAS,CAAC;AAC1B,QAAI,qBAAqB,OAAO,GAAG;AACjC,YAAM,WAAW,yBAAyB,QAAQ,OAAO;AACzD,aAAO,IAAI,gBAAgB,OAAO,QAAQ;AAAA,IAC5C;AAEA,QAAI,MAAM,KAAK,eAAe,SAAS,CAAC,CAAC,GAAG;AAC1C,YAAM,IAAI,aAAa,SAAS,UAAU;AAC1C,YAAM,KAAK,IAAI,WAAW,EAAE,MAAM,UAAU,SAAS,CAAC,CAAC,CAAC,CAAC;AACzD;AACA;AAAA,IACF;AACA,UAAM,OAAO,qBAAqB,OAAO,IAAI,QAAQ,QAAQ,cAAc,IAAI,GAAG,OAAO;AACzF,UAAM,OAAO,IAAI,SAAS,SAAS,IAAI,SAAS,IAAI,CAAC,IAAI;AACzD,QAAI,QAAQ,QAAQ,eAAe,IAAI,GAAG;AACxC,YAAM,KAAK,IAAI,WAAW,MAAM,UAAU,IAAI,CAAC,CAAC;AAChD,WAAK;AAAA,IACP,OAAO;AACL,YAAM,KAAK,IAAI,WAAW,MAAM,CAAC,CAAC,CAAC;AACnC;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,gBAAgB,OAAO,CAAC,CAAC;AACtC;AACA,SAAS,yBAAyB,SAAS;AACzC,QAAM,WAAW,CAAC;AAClB,SAAO,QAAQ,OAAO,EAAE,QAAQ,CAAC,CAAC,QAAQ,QAAQ,MAAM;AACtD,QAAI,OAAO,aAAa,UAAU;AAChC,iBAAW,CAAC,QAAQ;AAAA,IACtB;AACA,QAAI,aAAa,MAAM;AACrB,eAAS,MAAM,IAAI,sBAAsB,IAAI,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,QAAQ;AAAA,IACnF;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,UAAU,QAAQ;AACzB,QAAM,MAAM,CAAC;AACb,SAAO,QAAQ,MAAM,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;AAC1D,SAAO;AACT;AACA,SAAS,QAAQ,MAAM,QAAQ,SAAS;AACtC,SAAO,QAAQ,QAAQ,QAAQ,aAAa,QAAQ,QAAQ,UAAU;AACxE;AACA,IAAM,wBAAwB;AAM9B,IAAI;AAAA,CACH,SAAUC,YAAW;AACpB,EAAAA,WAAUA,WAAU,iBAAiB,IAAI,CAAC,IAAI;AAC9C,EAAAA,WAAUA,WAAU,eAAe,IAAI,CAAC,IAAI;AAC5C,EAAAA,WAAUA,WAAU,kBAAkB,IAAI,CAAC,IAAI;AAC/C,EAAAA,WAAUA,WAAU,iBAAiB,IAAI,CAAC,IAAI;AAC9C,EAAAA,WAAUA,WAAU,kBAAkB,IAAI,CAAC,IAAI;AAC/C,EAAAA,WAAUA,WAAU,cAAc,IAAI,CAAC,IAAI;AAC3C,EAAAA,WAAUA,WAAU,YAAY,IAAI,CAAC,IAAI;AACzC,EAAAA,WAAUA,WAAU,kBAAkB,IAAI,CAAC,IAAI;AAC/C,EAAAA,WAAUA,WAAU,gBAAgB,IAAI,CAAC,IAAI;AAC7C,EAAAA,WAAUA,WAAU,sBAAsB,IAAI,CAAC,IAAI;AACnD,EAAAA,WAAUA,WAAU,oBAAoB,IAAI,EAAE,IAAI;AAClD,EAAAA,WAAUA,WAAU,sBAAsB,IAAI,EAAE,IAAI;AACpD,EAAAA,WAAUA,WAAU,oBAAoB,IAAI,EAAE,IAAI;AAClD,EAAAA,WAAUA,WAAU,iBAAiB,IAAI,EAAE,IAAI;AAC/C,EAAAA,WAAUA,WAAU,eAAe,IAAI,EAAE,IAAI;AAC7C,EAAAA,WAAUA,WAAU,QAAQ,IAAI,EAAE,IAAI;AACtC,EAAAA,WAAUA,WAAU,mBAAmB,IAAI,EAAE,IAAI;AACnD,GAAG,cAAc,YAAY,CAAC,EAAE;AAyBhC,IAAM,cAAN,MAAkB;AAAA,EAChB;AAAA,EACA;AAAA,EACA,YACA,IACA,KAAK;AACH,SAAK,KAAK;AACV,SAAK,MAAM;AAAA,EACb;AACF;AAMA,IAAM,kBAAN,cAA8B,YAAY;AAAA,EACxC,OAAO,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA;AAAA,EACA,YACA,IACA,KACA,oBAAoB,cACpB,gBAAgB,MAAM;AACpB,UAAM,IAAI,GAAG;AACb,SAAK,oBAAoB;AACzB,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,uBAAuB,KAAK,EAAE,WAAW,KAAK,GAAG;AAAA,EAC1D;AACF;AAUA,IAAM,gBAAN,cAA4B,YAAY;AAAA,EACtC;AAAA,EACA,OAAO,UAAU;AAAA,EACjB,YACA,IACA,KACA,mBAAmB;AACjB,UAAM,IAAI,GAAG;AACb,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,qBAAqB,KAAK,EAAE,WAAW,KAAK,GAAG,0BAA0B,KAAK,iBAAiB;AAAA,EACxG;AACF;AAOA,IAAI;AAAA,CACH,SAAUC,6BAA4B;AAIrC,EAAAA,4BAA2BA,4BAA2B,UAAU,IAAI,CAAC,IAAI;AAIzE,EAAAA,4BAA2BA,4BAA2B,2BAA2B,IAAI,CAAC,IAAI;AAI1F,EAAAA,4BAA2BA,4BAA2B,oBAAoB,IAAI,CAAC,IAAI;AAInF,EAAAA,4BAA2BA,4BAA2B,eAAe,IAAI,CAAC,IAAI;AAChF,GAAG,+BAA+B,6BAA6B,CAAC,EAAE;AAOlE,IAAI;AAAA,CACH,SAAUC,wBAAuB;AAIhC,EAAAA,uBAAsBA,uBAAsB,0BAA0B,IAAI,CAAC,IAAI;AAO/E,EAAAA,uBAAsBA,uBAAsB,8BAA8B,IAAI,CAAC,IAAI;AACrF,GAAG,0BAA0B,wBAAwB,CAAC,EAAE;AAYxD,IAAM,mBAAN,cAA+B,YAAY;AAAA,EACzC;AAAA,EACA;AAAA,EACA,OAAO,UAAU;AAAA,EACjB,YACA,IACA,KAKA,QAMA,MAAM;AACJ,UAAM,IAAI,GAAG;AACb,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACd;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,wBAAwB,KAAK,EAAE,WAAW,KAAK,GAAG;AAAA,EAC3D;AACF;AASA,IAAM,oBAAN,cAAgC,YAAY;AAAA,EAC1C;AAAA,EACA;AAAA,EACA,OAAO,UAAU;AAAA,EACjB,YACA,IACA,KAKA,QAMA,MAAM;AACJ,UAAM,IAAI,GAAG;AACb,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACd;AACF;AAUA,IAAM,kBAAN,cAA8B,YAAY;AAAA,EACxC;AAAA,EACA;AAAA,EACA,OAAO,UAAU;AAAA,EACjB,YACA,IACA,KACA,OAOA,QAAQ;AACN,UAAM,IAAI,GAAG;AACb,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,uBAAuB,KAAK,EAAE,WAAW,KAAK,GAAG,aAAa,KAAK,KAAK;AAAA,EACjF;AACF;AAMA,IAAM,mBAAN,cAA+B,YAAY;AAAA,EACzC;AAAA,EACA;AAAA,EACA,OAAO,UAAU;AAAA,EACjB,YACA,IACA,KACA,mBACA,OAAO;AACL,UAAM,IAAI,GAAG;AACb,SAAK,oBAAoB;AACzB,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,wBAAwB,KAAK,EAAE,WAAW,KAAK,GAAG,0BAA0B,KAAK,iBAAiB,aAAa,KAAK,KAAK;AAAA,EAClI;AACF;AAQA,IAAM,mBAAN,cAA+B,YAAY;AAAA,EACzC;AAAA,EACA;AAAA,EACA,OAAO,UAAU;AAAA,EACjB,YACA,IACA,KACA,mBACA,OAAO;AACL,UAAM,IAAI,GAAG;AACb,SAAK,oBAAoB;AACzB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW;AACT,WAAO,wBAAwB,KAAK,EAAE,WAAW,KAAK,GAAG,0BAA0B,KAAK,iBAAiB,aAAa,KAAK,KAAK;AAAA,EAClI;AACF;AAQA,IAAM,iBAAN,cAA6B,YAAY;AAAA,EACvC;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO,UAAU;AAAA,EACjB,YACA,IACA,KACA,mBACA,OACA,gBAAgB;AACd,UAAM,IAAI,GAAG;AACb,SAAK,oBAAoB;AACzB,SAAK,QAAQ;AACb,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,WAAW;AACT,WAAO,sBAAsB,KAAK,EAAE,WAAW,KAAK,GAAG,0BAA0B,KAAK,iBAAiB,aAAa,KAAK,KAAK,qBAAqB,KAAK,cAAc;AAAA,EACxK;AACF;AAWA,IAAM,eAAN,cAA2B,YAAY;AAAA,EACrC;AAAA,EACA;AAAA,EACA,OAAO,UAAU;AAAA,EACjB,YACA,IACA,KACA,mBACA,OAAO;AACL,UAAM,IAAI,GAAG;AACb,SAAK,oBAAoB;AACzB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW;AACT,WAAO,oBAAoB,KAAK,EAAE,WAAW,KAAK,GAAG,0BAA0B,KAAK,iBAAiB,aAAa,KAAK,KAAK;AAAA,EAC9H;AACF;AAOA,IAAM,aAAN,cAAyB,YAAY;AAAA,EACnC;AAAA,EACA;AAAA,EACA,OAAO,UAAU;AAAA,EACjB,YACA,IACA,KACA,mBACA,OAAO;AACL,UAAM,IAAI,GAAG;AACb,SAAK,oBAAoB;AACzB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW;AACT,WAAO,kBAAkB,KAAK,EAAE,WAAW,KAAK,GAAG,0BAA0B,KAAK,iBAAiB,aAAa,KAAK,KAAK;AAAA,EAC5H;AACF;AAQA,IAAM,uBAAN,MAA2B;AAAA,EACzB;AAAA,EACA,OAAO,UAAU;AAAA,EACjB,YACA,OAAO;AACL,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW;AACT,WAAO,8BAA8B,KAAK,MAAM,IAAI;AAAA,EACtD;AACF;AAQA,IAAM,qBAAN,MAAyB;AAAA,EACvB;AAAA,EACA,OAAO,UAAU;AAAA,EACjB,YACA,OAAO;AACL,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW;AACT,WAAO,4BAA4B,KAAK,MAAM,IAAI;AAAA,EACpD;AACF;AASA,IAAM,uBAAN,MAA2B;AAAA,EACzB;AAAA,EACA,OAAO,UAAU;AAAA,EACjB,YACA,UAAU;AACR,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,WAAW;AACT,UAAM,OAAO,KAAK,SAAS,eAAe,KAAK,SAAS,YAAY,QAAQ;AAC5E,WAAO,+BAA+B,IAAI;AAAA,EAC5C;AACF;AAQA,IAAM,qBAAN,MAAyB;AAAA,EACvB;AAAA,EACA,OAAO,UAAU;AAAA,EACjB,YACA,UAAU;AACR,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,WAAW;AACT,UAAM,OAAO,KAAK,SAAS,eAAe,KAAK,SAAS,YAAY,QAAQ;AAC5E,WAAO,6BAA6B,IAAI;AAAA,EAC1C;AACF;AASA,IAAM,kBAAN,MAAsB;AAAA,EACpB;AAAA,EACA,OAAO,UAAU;AAAA,EACjB,YACA,UAAU;AACR,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,WAAW;AACT,UAAM,OAAO,KAAK,SAAS,eAAe,KAAK,SAAS,YAAY,QAAQ;AAC5E,WAAO,0BAA0B,IAAI;AAAA,EACvC;AACF;AASA,IAAM,gBAAN,MAAoB;AAAA,EAClB;AAAA,EACA,OAAO,UAAU;AAAA,EACjB,YACA,UAAU;AACR,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,WAAW;AACT,UAAM,OAAO,KAAK,SAAS,eAAe,KAAK,SAAS,YAAY,QAAQ;AAC5E,WAAO,wBAAwB,IAAI;AAAA,EACrC;AACF;AAMA,IAAM,SAAN,MAAa;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO,UAAU;AAAA,EACjB,YACA,aACA,UACA,QAAQ;AACN,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,WAAW;AACT,UAAM,MAAM,KAAK,WAAW,GAAG,KAAK,SAAS,CAAC,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,KAAK;AACzE,WAAO,mBAAmB,KAAK,MAAM,iBAAiB,GAAG;AAAA,EAC3D;AACF;AACA,IAAM,uBAAN,MAA2B;AAAC;AAC5B,IAAM,kBAAN,MAAsB;AAAA,EACpB;AAAA,EACA;AAAA,EACA,YAAY,KAAK,2BAA2B;AAC1C,SAAK,MAAM;AACX,SAAK,4BAA4B;AAAA,EACnC;AACF;AACA,SAAS,eAAe,aAAa;AACnC,UAAQ,YAAY,MAAM;AAAA,IACxB,KAAK,UAAU;AACb,aAAO,wBAAwB,YAAY,SAAS,aAAa,QAAQ,EAAE;AAAA,IAC7E,KAAK,UAAU;AACb,aAAO,0BAA0B,YAAY,SAAS,aAAa,QAAQ,EAAE;AAAA,IAC/E,KAAK,UAAU;AACb,aAAO,6BAA6B,YAAY,SAAS,aAAa,QAAQ,EAAE;AAAA,IAClF,KAAK,UAAU;AACb,aAAO,+BAA+B,YAAY,SAAS,aAAa,QAAQ,EAAE;AAAA,IACpF,KAAK,UAAU;AACb,aAAO,sBAAsB,YAAY,EAAE,WAAW,YAAY,GAAG,0BAA0B,YAAY,iBAAiB,aAAa,YAAY,KAAK,qBAAqB,YAAY,cAAc;AAAA,IAC3M,KAAK,UAAU;AACb,aAAO,wBAAwB,YAAY,EAAE,WAAW,YAAY,GAAG,0BAA0B,YAAY,iBAAiB,aAAa,YAAY,KAAK;AAAA,IAC9J,KAAK,UAAU;AACb,aAAO,wBAAwB,YAAY,EAAE,WAAW,YAAY,GAAG;AAAA,IACzE,KAAK,UAAU;AACb,aAAO,yBAAyB,YAAY,EAAE,WAAW,YAAY,GAAG;AAAA,IAC1E,KAAK,UAAU;AACb,aAAO,qBAAqB,YAAY,EAAE,WAAW,YAAY,GAAG,0BAA0B,YAAY,iBAAiB;AAAA,IAC7H,KAAK,UAAU;AACb,aAAO,uBAAuB,YAAY,EAAE,WAAW,YAAY,GAAG,aAAa,YAAY,KAAK;AAAA,IACtG,KAAK,UAAU;AACb,aAAO,uBAAuB,YAAY,EAAE,WAAW,YAAY,GAAG;AAAA,IACxE,KAAK,UAAU;AACb,aAAO,kBAAkB,YAAY,EAAE,WAAW,YAAY,GAAG,0BAA0B,YAAY,iBAAiB,aAAa,YAAY,KAAK;AAAA,IACxJ,KAAK,UAAU;AACb,aAAO,oBAAoB,YAAY,EAAE,WAAW,YAAY,GAAG,0BAA0B,YAAY,iBAAiB,aAAa,YAAY,KAAK;AAAA,IAC1J,KAAK,UAAU;AACb,aAAO,4BAA4B,YAAY,MAAM,IAAI;AAAA,IAC3D,KAAK,UAAU;AACb,aAAO,8BAA8B,YAAY,MAAM,IAAI;AAAA,IAC7D,KAAK,UAAU;AACb,aAAO,wBAAwB,YAAY,EAAE,WAAW,YAAY,GAAG,0BAA0B,YAAY,iBAAiB,aAAa,YAAY,KAAK;AAAA,IAC9J,KAAK,UAAU;AACb,YAAM,MAAM,YAAY,WAAW,GAAG,YAAY,SAAS,CAAC,CAAC,KAAK,YAAY,SAAS,CAAC,CAAC,KAAK;AAC9F,aAAO,mBAAmB,YAAY,MAAM,iBAAiB,GAAG;AAAA,EACpE;AACF;AAUA,SAAS,iCAAiC,OAAO,iBAAiB;AAChE,MAAI,MAAM,aAAa,CAAC,MAAM,WAAW;AACvC,UAAM,YAAY,0BAA0B,MAAM,WAAW,iBAAiB,UAAU,MAAM,IAAI,EAAE;AAAA,EACtG;AACA,SAAO,MAAM,aAAa;AAC5B;AACA,SAAS,eAAe,QAAQ,aAAa,IAAI,8BAA8B,OAAO;AAEpF,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAM,QAAQ,OAAO,CAAC;AACtB,UAAM,WAAW,YAAY,YAAY,KAAK;AAC9C,iBAAa,OAAO,UAAU,2BAA2B;AAAA,EAC3D;AACF;AACA,SAAS,iBAAiB,UAAU,WAAW;AAC7C,MAAI,aAAa,WAAY,SAAS,GAAG;AACvC,UAAM,IAAI,aAAc,MAAkD,mCAAmC,QAAQ,6HAAkI;AAAA,EACzP,WAAW,aAAa,CAAC,aAAa,SAAS,GAAG;AAChD,UAAM,IAAI,aAAc,MAAkD,mCAAmC,QAAQ,sCAAsC;AAAA,EAC7J;AACF;AACA,SAAS,aAAa,OAAO,UAAU,6BAA6B;AAClE,MAAI,OAAO,cAAc,eAAe,WAAW;AACjD,QAAI,CAAC,OAAO;AACV,YAAM,IAAI,aAAc,MAAkD;AAAA,wCACxC,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAS3C;AAAA,IACD;AACA,QAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,YAAM,IAAI,aAAc,MAAkD,mCAAmC,QAAQ,8BAA8B;AAAA,IACrJ;AACA,QAAI,CAAC,MAAM,cAAc,CAAC,MAAM,aAAa,CAAC,MAAM,iBAAiB,CAAC,MAAM,YAAY,CAAC,MAAM,gBAAgB,MAAM,UAAU,MAAM,WAAW,gBAAgB;AAC9J,YAAM,IAAI,aAAc,MAAkD,mCAAmC,QAAQ,0FAA0F;AAAA,IACjN;AACA,QAAI,MAAM,cAAc,MAAM,UAAU;AACtC,YAAM,IAAI,aAAc,MAAkD,mCAAmC,QAAQ,oDAAoD;AAAA,IAC3K;AACA,QAAI,MAAM,cAAc,MAAM,cAAc;AAC1C,YAAM,IAAI,aAAc,MAAkD,mCAAmC,QAAQ,wDAAwD;AAAA,IAC/K;AACA,QAAI,MAAM,YAAY,MAAM,cAAc;AACxC,YAAM,IAAI,aAAc,MAAkD,mCAAmC,QAAQ,sDAAsD;AAAA,IAC7K;AACA,QAAI,MAAM,eAAe,MAAM,aAAa,MAAM,gBAAgB;AAChE,YAAM,IAAI,aAAc,MAAkD,mCAAmC,QAAQ,mEAAmE;AAAA,IAC1L;AACA,QAAI,MAAM,aAAa,MAAM,eAAe;AAC1C,YAAM,IAAI,aAAc,MAAkD,mCAAmC,QAAQ,wDAAwD;AAAA,IAC/K;AACA,QAAI,MAAM,cAAc,MAAM,aAAa;AACzC,YAAM,IAAI,aAAc,MAAkD,mCAAmC,QAAQ,kIAAuI;AAAA,IAC9P;AACA,QAAI,MAAM,QAAQ,MAAM,SAAS;AAC/B,YAAM,IAAI,aAAc,MAAkD,mCAAmC,QAAQ,6CAA6C;AAAA,IACpK;AACA,QAAI,MAAM,eAAe,UAAU,CAAC,MAAM,aAAa,CAAC,MAAM,iBAAiB,CAAC,MAAM,YAAY,CAAC,MAAM,cAAc;AACrH,YAAM,IAAI,aAAc,MAAkD,mCAAmC,QAAQ,0GAA0G;AAAA,IACjO;AACA,QAAI,MAAM,SAAS,UAAU,MAAM,YAAY,QAAQ;AACrD,YAAM,IAAI,aAAc,MAAkD,mCAAmC,QAAQ,0DAA0D;AAAA,IACjL;AACA,QAAI,OAAO,MAAM,SAAS,YAAY,MAAM,KAAK,OAAO,CAAC,MAAM,KAAK;AAClE,YAAM,IAAI,aAAc,MAAkD,mCAAmC,QAAQ,mCAAmC;AAAA,IAC1J;AACA,QAAI,MAAM,SAAS,MAAM,MAAM,eAAe,UAAU,MAAM,cAAc,QAAQ;AAClF,YAAM,MAAM;AACZ,YAAM,IAAI,aAAc,MAAkD,2CAA2C,QAAQ,mBAAmB,MAAM,UAAU,oCAAoC,GAAG,EAAE;AAAA,IAC3M;AACA,QAAI,6BAA6B;AAC/B,uBAAiB,UAAU,MAAM,SAAS;AAAA,IAC5C;AAAA,EACF;AACA,MAAI,MAAM,UAAU;AAClB,mBAAe,MAAM,UAAU,UAAU,2BAA2B;AAAA,EACtE;AACF;AACA,SAAS,YAAY,YAAY,cAAc;AAC7C,MAAI,CAAC,cAAc;AACjB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,cAAc,CAAC,aAAa,MAAM;AACrC,WAAO;AAAA,EACT,WAAW,cAAc,CAAC,aAAa,MAAM;AAC3C,WAAO,GAAG,UAAU;AAAA,EACtB,WAAW,CAAC,cAAc,aAAa,MAAM;AAC3C,WAAO,aAAa;AAAA,EACtB,OAAO;AACL,WAAO,GAAG,UAAU,IAAI,aAAa,IAAI;AAAA,EAC3C;AACF;AAEA,SAAS,UAAU,OAAO;AACxB,SAAO,MAAM,UAAU;AACzB;AAKA,SAAS,sBAAsB,QAAQ,YAAY;AACjD,QAAM,eAAe,OAAO,OAAO,OAAK,UAAU,CAAC,MAAM,UAAU;AACnE,eAAa,KAAK,GAAG,OAAO,OAAO,OAAK,UAAU,CAAC,MAAM,UAAU,CAAC;AACpE,SAAO;AACT;AAaA,SAAS,wBAAwB,UAAU;AACzC,MAAI,CAAC,SAAU,QAAO;AAItB,MAAI,SAAS,aAAa,WAAW;AACnC,WAAO,SAAS,YAAY;AAAA,EAC9B;AACA,WAAS,IAAI,SAAS,QAAQ,GAAG,IAAI,EAAE,QAAQ;AAC7C,UAAM,QAAQ,EAAE;AAKhB,QAAI,OAAO,gBAAiB,QAAO,MAAM;AACzC,QAAI,OAAO,UAAW,QAAO,MAAM;AAAA,EACrC;AACA,SAAO;AACT;AAOA,IAAM,gBAAN,MAAoB;AAAA,EAClB;AAAA,EACA,SAAS;AAAA,EACT,QAAQ;AAAA,EACR;AAAA,EACA,YAAY;AAAA,EACZ,IAAI,WAAW;AACb,WAAO,wBAAwB,KAAK,OAAO,QAAQ,KAAK,KAAK;AAAA,EAC/D;AAAA,EACA,YAAY,cAAc;AACxB,SAAK,eAAe;AACpB,SAAK,WAAW,IAAI,uBAAuB,KAAK,YAAY;AAAA,EAC9D;AACF;AAMA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B;AAAA;AAAA,EAEA,WAAW,oBAAI,IAAI;AAAA;AAAA,EAEnB,YAAY,cAAc;AACxB,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA,EAEA,qBAAqB,WAAW,QAAQ;AACtC,UAAM,UAAU,KAAK,mBAAmB,SAAS;AACjD,YAAQ,SAAS;AACjB,SAAK,SAAS,IAAI,WAAW,OAAO;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB,WAAW;AAChC,UAAM,UAAU,KAAK,WAAW,SAAS;AACzC,QAAI,SAAS;AACX,cAAQ,SAAS;AACjB,cAAQ,YAAY;AAAA,IACtB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB;AACpB,UAAM,WAAW,KAAK;AACtB,SAAK,WAAW,oBAAI,IAAI;AACxB,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB,UAAU;AAC3B,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,mBAAmB,WAAW;AAC5B,QAAI,UAAU,KAAK,WAAW,SAAS;AACvC,QAAI,CAAC,SAAS;AACZ,gBAAU,IAAI,cAAc,KAAK,YAAY;AAC7C,WAAK,SAAS,IAAI,WAAW,OAAO;AAAA,IACtC;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW,WAAW;AACpB,WAAO,KAAK,SAAS,IAAI,SAAS,KAAK;AAAA,EACzC;AAAA,EACA,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAA2B,SAAY,mBAAmB,CAAC;AAAA,EAC9F;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,wBAAuB;AAAA,IAChC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,OAAN,MAAW;AAAA;AAAA,EAET;AAAA,EACA,YAAY,MAAM;AAChB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,GAAG;AACR,UAAM,IAAI,KAAK,aAAa,CAAC;AAC7B,WAAO,EAAE,SAAS,IAAI,EAAE,EAAE,SAAS,CAAC,IAAI;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,GAAG;AACV,UAAM,IAAI,SAAS,GAAG,KAAK,KAAK;AAChC,WAAO,IAAI,EAAE,SAAS,IAAI,CAAAC,OAAKA,GAAE,KAAK,IAAI,CAAC;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,GAAG;AACZ,UAAM,IAAI,SAAS,GAAG,KAAK,KAAK;AAChC,WAAO,KAAK,EAAE,SAAS,SAAS,IAAI,EAAE,SAAS,CAAC,EAAE,QAAQ;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,GAAG;AACV,UAAM,IAAI,SAAS,GAAG,KAAK,KAAK;AAChC,QAAI,EAAE,SAAS,EAAG,QAAO,CAAC;AAC1B,UAAM,IAAI,EAAE,EAAE,SAAS,CAAC,EAAE,SAAS,IAAI,CAAAC,OAAKA,GAAE,KAAK;AACnD,WAAO,EAAE,OAAO,QAAM,OAAO,CAAC;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,GAAG;AACd,WAAO,SAAS,GAAG,KAAK,KAAK,EAAE,IAAI,OAAK,EAAE,KAAK;AAAA,EACjD;AACF;AAEA,SAAS,SAAS,OAAO,MAAM;AAC7B,MAAI,UAAU,KAAK,MAAO,QAAO;AACjC,aAAW,SAAS,KAAK,UAAU;AACjC,UAAMC,QAAO,SAAS,OAAO,KAAK;AAClC,QAAIA,MAAM,QAAOA;AAAA,EACnB;AACA,SAAO;AACT;AAEA,SAAS,SAAS,OAAO,MAAM;AAC7B,MAAI,UAAU,KAAK,MAAO,QAAO,CAAC,IAAI;AACtC,aAAW,SAAS,KAAK,UAAU;AACjC,UAAM,OAAO,SAAS,OAAO,KAAK;AAClC,QAAI,KAAK,QAAQ;AACf,WAAK,QAAQ,IAAI;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO,CAAC;AACV;AACA,IAAM,WAAN,MAAe;AAAA,EACb;AAAA,EACA;AAAA,EACA,YAAY,OAAO,UAAU;AAC3B,SAAK,QAAQ;AACb,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,WAAW;AACT,WAAO,YAAY,KAAK,KAAK;AAAA,EAC/B;AACF;AAEA,SAAS,kBAAkB,MAAM;AAC/B,QAAMC,OAAM,CAAC;AACb,MAAI,MAAM;AACR,SAAK,SAAS,QAAQ,WAASA,KAAI,MAAM,MAAM,MAAM,IAAI,KAAK;AAAA,EAChE;AACA,SAAOA;AACT;AAiCA,IAAM,cAAN,cAA0B,KAAK;AAAA,EAC7B;AAAA;AAAA,EAEA,YAAY,MACZ,UAAU;AACR,UAAM,IAAI;AACV,SAAK,WAAW;AAChB,mBAAe,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,WAAW;AACT,WAAO,KAAK,SAAS,SAAS;AAAA,EAChC;AACF;AACA,SAAS,iBAAiB,eAAe;AACvC,QAAM,WAAW,yBAAyB,aAAa;AACvD,QAAM,WAAW,IAAI,4BAAgB,CAAC,IAAI,WAAW,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7D,QAAM,cAAc,IAAI,4BAAgB,CAAC,CAAC;AAC1C,QAAM,YAAY,IAAI,4BAAgB,CAAC,CAAC;AACxC,QAAM,mBAAmB,IAAI,4BAAgB,CAAC,CAAC;AAC/C,QAAM,WAAW,IAAI,4BAAgB,EAAE;AACvC,QAAM,YAAY,IAAI,eAAe,UAAU,aAAa,kBAAkB,UAAU,WAAW,gBAAgB,eAAe,SAAS,IAAI;AAC/I,YAAU,WAAW,SAAS;AAC9B,SAAO,IAAI,YAAY,IAAI,SAAS,WAAW,CAAC,CAAC,GAAG,QAAQ;AAC9D;AACA,SAAS,yBAAyB,eAAe;AAC/C,QAAM,cAAc,CAAC;AACrB,QAAM,YAAY,CAAC;AACnB,QAAM,mBAAmB,CAAC;AAC1B,QAAM,WAAW;AACjB,QAAM,YAAY,IAAI,uBAAuB,CAAC,GAAG,aAAa,kBAAkB,UAAU,WAAW,gBAAgB,eAAe,MAAM,CAAC,CAAC;AAC5I,SAAO,IAAI,oBAAoB,IAAI,IAAI,SAAS,WAAW,CAAC,CAAC,CAAC;AAChE;AAoBA,IAAM,iBAAN,MAAqB;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,YACA,YACA,eACA,oBACA,iBACA,aACA,QACA,WAAW,gBAAgB;AACzB,SAAK,aAAa;AAClB,SAAK,gBAAgB;AACrB,SAAK,qBAAqB;AAC1B,SAAK,kBAAkB;AACvB,SAAK,cAAc;AACnB,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,kBAAkB;AACvB,SAAK,QAAQ,KAAK,aAAa,SAAK,sBAAI,OAAK,EAAE,aAAa,CAAC,CAAC,SAAK,gBAAG,MAAS;AAE/E,SAAK,MAAM;AACX,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,OAAO;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,cAAc;AAChB,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA;AAAA,EAEA,IAAI,OAAO;AACT,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK,aAAa,OAAO,IAAI;AAAA,EACtC;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,aAAa,WAAW,IAAI;AAAA,EAC1C;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,SAAS,IAAI;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,eAAe;AACjB,WAAO,KAAK,aAAa,aAAa,IAAI;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,WAAW;AACb,SAAK,cAAc,KAAK,OAAO,SAAK,sBAAI,OAAK,kBAAkB,CAAC,CAAC,CAAC;AAClE,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,gBAAgB;AAClB,SAAK,mBAAmB,KAAK,YAAY,SAAK,sBAAI,OAAK,kBAAkB,CAAC,CAAC,CAAC;AAC5E,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW;AACT,WAAO,KAAK,WAAW,KAAK,SAAS,SAAS,IAAI,UAAU,KAAK,eAAe;AAAA,EAClF;AACF;AAOA,SAAS,aAAa,OAAO,QAAQ,4BAA4B,aAAa;AAC5E,MAAI;AACJ,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,MAAI,WAAW,SAAS,8BAA8B;AAAA,EAEtD,aAAa,SAAS;AAAA,EAEtB,CAAC,OAAO,aAAa,CAAC,OAAO,aAAa,gBAAgB;AACxD,gBAAY;AAAA,MACV,QAAQ,kCACH,OAAO,SACP,MAAM;AAAA,MAEX,MAAM,kCACD,OAAO,OACP,MAAM;AAAA,MAEX,SAAS,gEAOJ,MAAM,OAEN,OAAO,OAEP,aAAa,OAEb,MAAM;AAAA,IAEb;AAAA,EACF,OAAO;AACL,gBAAY;AAAA,MACV,QAAQ,mBACH,MAAM;AAAA,MAEX,MAAM,mBACD,MAAM;AAAA,MAEX,SAAS,kCACJ,MAAM,OACL,MAAM,iBAAiB,CAAC;AAAA,IAEhC;AAAA,EACF;AACA,MAAI,eAAe,eAAe,WAAW,GAAG;AAC9C,cAAU,QAAQ,aAAa,IAAI,YAAY;AAAA,EACjD;AACA,SAAO;AACT;AAwBA,IAAM,yBAAN,MAA6B;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,IAAI,QAAQ;AAGV,WAAO,KAAK,OAAO,aAAa;AAAA,EAClC;AAAA;AAAA,EAEA,YACA,KAoBA,QACA,aACA,UACA,MACA,QACA,WAAW,aAAa,SAAS;AAC/B,SAAK,MAAM;AACX,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,IAAI,OAAO;AACT,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK,aAAa,OAAO,IAAI;AAAA,EACtC;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,aAAa,WAAW,IAAI;AAAA,EAC1C;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,SAAS,IAAI;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,eAAe;AACjB,WAAO,KAAK,aAAa,aAAa,IAAI;AAAA,EAC5C;AAAA,EACA,IAAI,WAAW;AACb,SAAK,cAAc,kBAAkB,KAAK,MAAM;AAChD,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB;AAClB,SAAK,mBAAmB,kBAAkB,KAAK,WAAW;AAC1D,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW;AACT,UAAM,MAAM,KAAK,IAAI,IAAI,aAAW,QAAQ,SAAS,CAAC,EAAE,KAAK,GAAG;AAChE,UAAM,UAAU,KAAK,cAAc,KAAK,YAAY,OAAO;AAC3D,WAAO,cAAc,GAAG,YAAY,OAAO;AAAA,EAC7C;AACF;AA4BA,IAAM,sBAAN,cAAkC,KAAK;AAAA,EACrC;AAAA;AAAA,EAEA,YACA,KAAK,MAAM;AACT,UAAM,IAAI;AACV,SAAK,MAAM;AACX,mBAAe,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,WAAW;AACT,WAAO,cAAc,KAAK,KAAK;AAAA,EACjC;AACF;AACA,SAAS,eAAe,OAAO,MAAM;AACnC,OAAK,MAAM,eAAe;AAC1B,OAAK,SAAS,QAAQ,OAAK,eAAe,OAAO,CAAC,CAAC;AACrD;AACA,SAAS,cAAc,MAAM;AAC3B,QAAM,IAAI,KAAK,SAAS,SAAS,IAAI,MAAM,KAAK,SAAS,IAAI,aAAa,EAAE,KAAK,IAAI,CAAC,QAAQ;AAC9F,SAAO,GAAG,KAAK,KAAK,GAAG,CAAC;AAC1B;AAMA,SAAS,sBAAsB,OAAO;AACpC,MAAI,MAAM,UAAU;AAClB,UAAM,kBAAkB,MAAM;AAC9B,UAAM,eAAe,MAAM;AAC3B,UAAM,WAAW;AACjB,QAAI,CAAC,aAAa,gBAAgB,aAAa,aAAa,WAAW,GAAG;AACxE,YAAM,mBAAmB,KAAK,aAAa,WAAW;AAAA,IACxD;AACA,QAAI,gBAAgB,aAAa,aAAa,UAAU;AACtD,YAAM,gBAAgB,KAAK,aAAa,QAAQ;AAAA,IAClD;AACA,QAAI,CAAC,aAAa,gBAAgB,QAAQ,aAAa,MAAM,GAAG;AAC9D,YAAM,cAAc,KAAK,aAAa,MAAM;AAAA,IAC9C;AACA,QAAI,CAAC,mBAAmB,gBAAgB,KAAK,aAAa,GAAG,GAAG;AAC9D,YAAM,WAAW,KAAK,aAAa,GAAG;AAAA,IACxC;AACA,QAAI,CAAC,aAAa,gBAAgB,MAAM,aAAa,IAAI,GAAG;AAC1D,YAAM,YAAY,KAAK,aAAa,IAAI;AAAA,IAC1C;AAAA,EACF,OAAO;AACL,UAAM,WAAW,MAAM;AAEvB,UAAM,YAAY,KAAK,MAAM,gBAAgB,IAAI;AAAA,EACnD;AACF;AACA,SAAS,0BAA0B,GAAG,GAAG;AACvC,QAAM,iBAAiB,aAAa,EAAE,QAAQ,EAAE,MAAM,KAAK,cAAc,EAAE,KAAK,EAAE,GAAG;AACrF,QAAM,kBAAkB,CAAC,EAAE,WAAW,CAAC,EAAE;AACzC,SAAO,kBAAkB,CAAC,oBAAoB,CAAC,EAAE,UAAU,0BAA0B,EAAE,QAAQ,EAAE,MAAM;AACzG;AACA,SAAS,eAAe,QAAQ;AAC9B,SAAO,OAAO,OAAO,UAAU,YAAY,OAAO,UAAU;AAC9D;AAsBA,IAAM,qBAAqB,IAAI,eAAe,YAAY,sBAAsB,EAAE;AAkDlF,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,YAAY;AAAA;AAAA,EAEZ,IAAI,wBAAwB;AAC1B,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlB,OAAO;AAAA,EACP,iBAAiB,IAAI,aAAa;AAAA,EAClC,mBAAmB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpC,eAAe,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhC,eAAe,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhC,mBAAmB,MAAM,MAAS;AAAA,EAClC,iBAAiB,OAAO,sBAAsB;AAAA,EAC9C,WAAW,OAAO,gBAAgB;AAAA,EAClC,iBAAiB,OAAO,iBAAiB;AAAA,EACzC,cAAc,OAAO,cAAc;AAAA,IACjC,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED,mCAAmC;AAAA;AAAA,EAEnC,YAAY,SAAS;AACnB,QAAI,QAAQ,MAAM,GAAG;AACnB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,QAAQ,MAAM;AAClB,UAAI,aAAa;AAGf;AAAA,MACF;AAEA,UAAI,KAAK,0BAA0B,aAAa,GAAG;AACjD,aAAK,WAAW;AAChB,aAAK,eAAe,uBAAuB,aAAa;AAAA,MAC1D;AAEA,WAAK,yBAAyB;AAAA,IAChC;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AAEZ,QAAI,KAAK,0BAA0B,KAAK,IAAI,GAAG;AAC7C,WAAK,eAAe,uBAAuB,KAAK,IAAI;AAAA,IACtD;AACA,SAAK,aAAa,yBAAyB,IAAI;AAAA,EACjD;AAAA,EACA,0BAA0B,YAAY;AACpC,WAAO,KAAK,eAAe,WAAW,UAAU,GAAG,WAAW;AAAA,EAChE;AAAA;AAAA,EAEA,WAAW;AACT,SAAK,yBAAyB;AAAA,EAChC;AAAA,EACA,2BAA2B;AACzB,SAAK,eAAe,qBAAqB,KAAK,MAAM,IAAI;AACxD,QAAI,KAAK,WAAW;AAClB;AAAA,IACF;AAGA,UAAM,UAAU,KAAK,eAAe,WAAW,KAAK,IAAI;AACxD,QAAI,SAAS,OAAO;AAClB,UAAI,QAAQ,WAAW;AAErB,aAAK,OAAO,QAAQ,WAAW,QAAQ,KAAK;AAAA,MAC9C,OAAO;AAEL,aAAK,aAAa,QAAQ,OAAO,QAAQ,QAAQ;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AACd,QAAI,CAAC,KAAK,UAAW,OAAM,IAAI,aAAc,OAAmD,OAAO,cAAc,eAAe,cAAc,yBAAyB;AAC3K,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,iBAAiB;AACnB,QAAI,CAAC,KAAK,UAAW,OAAM,IAAI,aAAc,OAAmD,OAAO,cAAc,eAAe,cAAc,yBAAyB;AAC3K,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,qBAAqB;AACvB,QAAI,KAAK,iBAAiB;AACxB,aAAO,KAAK,gBAAgB,SAAS;AAAA,IACvC;AACA,WAAO,CAAC;AAAA,EACV;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACP,QAAI,CAAC,KAAK,UAAW,OAAM,IAAI,aAAc,OAAmD,OAAO,cAAc,eAAe,cAAc,yBAAyB;AAC3K,SAAK,SAAS,OAAO;AACrB,UAAM,MAAM,KAAK;AACjB,SAAK,YAAY;AACjB,SAAK,kBAAkB;AACvB,SAAK,aAAa,KAAK,IAAI,QAAQ;AACnC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,KAAK,gBAAgB;AAC1B,SAAK,YAAY;AACjB,SAAK,kBAAkB;AACvB,SAAK,SAAS,OAAO,IAAI,QAAQ;AACjC,SAAK,aAAa,oCAAoC,IAAI;AAC1D,SAAK,aAAa,KAAK,IAAI,QAAQ;AAAA,EACrC;AAAA,EACA,aAAa;AACX,QAAI,KAAK,WAAW;AAClB,YAAM,IAAI,KAAK;AACf,WAAK,UAAU,QAAQ;AACvB,WAAK,YAAY;AACjB,WAAK,kBAAkB;AACvB,WAAK,iBAAiB,KAAK,CAAC;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,aAAa,gBAAgB,qBAAqB;AAChD,QAAI,KAAK,aAAa;AACpB,YAAM,IAAI,aAAc,OAAuD,OAAO,cAAc,eAAe,cAAc,6CAA6C;AAAA,IAChL;AACA,SAAK,kBAAkB;AACvB,UAAM,WAAW,KAAK;AACtB,UAAM,WAAW,eAAe;AAChC,UAAM,YAAY,SAAS;AAC3B,UAAM,gBAAgB,KAAK,eAAe,mBAAmB,KAAK,IAAI,EAAE;AACxE,UAAM,WAAW,IAAI,eAAe,gBAAgB,eAAe,SAAS,UAAU,KAAK,gBAAgB;AAC3G,SAAK,YAAY,SAAS,gBAAgB,WAAW;AAAA,MACnD,OAAO,SAAS;AAAA,MAChB;AAAA,MACA;AAAA,IACF,CAAC;AAGD,SAAK,eAAe,aAAa;AACjC,SAAK,aAAa,oCAAoC,IAAI;AAC1D,SAAK,eAAe,KAAK,KAAK,UAAU,QAAQ;AAAA,EAClD;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,IAC7B,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB,CAAC,GAAG,kBAAkB;AAAA,IAC1C;AAAA,IACA,SAAS;AAAA,MACP,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAClB,cAAc;AAAA,MACd,cAAc;AAAA,IAChB;AAAA,IACA,UAAU,CAAC,QAAQ;AAAA,IACnB,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAqB;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,OAAO,eAAe,QAAQ,YAAY;AACpD,SAAK,QAAQ;AACb,SAAK,gBAAgB;AACrB,SAAK,SAAS;AACd,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,IAAI,OAAO,eAAe;AACxB,QAAI,UAAU,gBAAgB;AAC5B,aAAO,KAAK;AAAA,IACd;AACA,QAAI,UAAU,wBAAwB;AACpC,aAAO,KAAK;AAAA,IACd;AACA,QAAI,UAAU,oBAAoB;AAChC,aAAO,KAAK;AAAA,IACd;AACA,WAAO,KAAK,OAAO,IAAI,OAAO,aAAa;AAAA,EAC7C;AACF;AACA,IAAM,eAAe,IAAI,eAAe,EAAE;AAe1C,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,0BAA0B,oBAAI,IAAI;AAAA,EAClC,oCAAoC,QAAQ;AAC1C,SAAK,yBAAyB,MAAM;AACpC,SAAK,qBAAqB,MAAM;AAAA,EAClC;AAAA,EACA,yBAAyB,QAAQ;AAC/B,SAAK,wBAAwB,IAAI,MAAM,GAAG,YAAY;AACtD,SAAK,wBAAwB,OAAO,MAAM;AAAA,EAC5C;AAAA,EACA,qBAAqB,QAAQ;AAC3B,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,uBAAmB,2BAAc,CAAC,eAAe,aAAa,eAAe,QAAQ,eAAe,IAAI,CAAC,EAAE,SAAK,4BAAU,CAAC,CAAC,aAAa,QAAQ,IAAI,GAAG,UAAU;AACtK,aAAO,iDACF,cACA,SACA;AAIL,UAAI,UAAU,GAAG;AACf,mBAAO,gBAAG,IAAI;AAAA,MAChB;AAIA,aAAO,QAAQ,QAAQ,IAAI;AAAA,IAC7B,CAAC,CAAC,EAAE,UAAU,UAAQ;AAGpB,UAAI,CAAC,OAAO,eAAe,CAAC,OAAO,yBAAyB,OAAO,mBAAmB,kBAAkB,eAAe,cAAc,MAAM;AACzI,aAAK,yBAAyB,MAAM;AACpC;AAAA,MACF;AACA,YAAM,SAAS,qBAAqB,eAAe,SAAS;AAC5D,UAAI,CAAC,QAAQ;AACX,aAAK,yBAAyB,MAAM;AACpC;AAAA,MACF;AACA,iBAAW;AAAA,QACT;AAAA,MACF,KAAK,OAAO,QAAQ;AAClB,eAAO,sBAAsB,SAAS,cAAc,KAAK,YAAY,CAAC;AAAA,MACxE;AAAA,IACF,CAAC;AACD,SAAK,wBAAwB,IAAI,QAAQ,gBAAgB;AAAA,EAC3D;AAAA,EACA,OAAO,OAAO,SAAS,mCAAmC,mBAAmB;AAC3E,WAAO,KAAK,qBAAqB,6BAA4B;AAAA,EAC/D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,4BAA2B;AAAA,EACtC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAWH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,UAAU,CAAC,mBAAmB;AAAA,IAC9B,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,+BAA+B,IAAI,KAAK;AACzD,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,GAAG,eAAe;AAAA,MACjC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,YAAY;AAAA,IAC3B,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,SAAS,CAAC,YAAY;AAAA;AAAA,MAEtB,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAIH,SAAS,kBAAkB,GAAG;AAC5B,QAAM,WAAW,EAAE,YAAY,EAAE,SAAS,IAAI,iBAAiB;AAC/D,QAAM,IAAI,WAAW,iCAChB,IADgB;AAAA,IAEnB;AAAA,EACF,KAAI,mBACC;AAEL,MAAI,CAAC,EAAE,aAAa,CAAC,EAAE,kBAAkB,YAAY,EAAE,iBAAiB,EAAE,UAAU,EAAE,WAAW,gBAAgB;AAC/G,MAAE,YAAY;AAAA,EAChB;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,oBAAoB,MAAM,WAAW;AAC9D,QAAM,OAAO,WAAW,oBAAoB,KAAK,OAAO,YAAY,UAAU,QAAQ,MAAS;AAC/F,SAAO,IAAI,YAAY,MAAM,IAAI;AACnC;AACA,SAAS,WAAW,oBAAoB,MAAM,WAAW;AAEvD,MAAI,aAAa,mBAAmB,iBAAiB,KAAK,OAAO,UAAU,MAAM,QAAQ,GAAG;AAC1F,UAAM,QAAQ,UAAU;AACxB,UAAM,kBAAkB,KAAK;AAC7B,UAAM,WAAW,sBAAsB,oBAAoB,MAAM,SAAS;AAC1E,WAAO,IAAI,SAAS,OAAO,QAAQ;AAAA,EACrC,OAAO;AACL,QAAI,mBAAmB,aAAa,KAAK,KAAK,GAAG;AAE/C,YAAM,sBAAsB,mBAAmB,SAAS,KAAK,KAAK;AAClE,UAAI,wBAAwB,MAAM;AAChC,cAAMX,QAAO,oBAAoB;AACjC,QAAAA,MAAK,MAAM,kBAAkB,KAAK;AAClC,QAAAA,MAAK,WAAW,KAAK,SAAS,IAAI,OAAK,WAAW,oBAAoB,CAAC,CAAC;AACxE,eAAOA;AAAA,MACT;AAAA,IACF;AACA,UAAM,QAAQ,qBAAqB,KAAK,KAAK;AAC7C,UAAM,WAAW,KAAK,SAAS,IAAI,OAAK,WAAW,oBAAoB,CAAC,CAAC;AACzE,WAAO,IAAI,SAAS,OAAO,QAAQ;AAAA,EACrC;AACF;AACA,SAAS,sBAAsB,oBAAoB,MAAM,WAAW;AAClE,SAAO,KAAK,SAAS,IAAI,WAAS;AAChC,eAAW,KAAK,UAAU,UAAU;AAClC,UAAI,mBAAmB,iBAAiB,MAAM,OAAO,EAAE,MAAM,QAAQ,GAAG;AACtE,eAAO,WAAW,oBAAoB,OAAO,CAAC;AAAA,MAChD;AAAA,IACF;AACA,WAAO,WAAW,oBAAoB,KAAK;AAAA,EAC7C,CAAC;AACH;AACA,SAAS,qBAAqB,GAAG;AAC/B,SAAO,IAAI,eAAe,IAAI,4BAAgB,EAAE,GAAG,GAAG,IAAI,4BAAgB,EAAE,MAAM,GAAG,IAAI,4BAAgB,EAAE,WAAW,GAAG,IAAI,4BAAgB,EAAE,QAAQ,GAAG,IAAI,4BAAgB,EAAE,IAAI,GAAG,EAAE,QAAQ,EAAE,WAAW,CAAC;AACjN;AAiCA,IAAM,kBAAN,MAAsB;AAAA,EACpB;AAAA,EACA;AAAA,EACA,YAAY,YAAY,2BAA2B;AACjD,SAAK,aAAa;AAClB,SAAK,4BAA4B;AAAA,EACnC;AACF;AACA,IAAM,6BAA6B;AACnC,SAAS,2BAA2B,eAAe,UAAU;AAC3D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,UAAU,QAAQ,IAAI;AAAA,IACxB,YAAY;AAAA,IACZ,2BAA2B;AAAA,EAC7B,IAAI;AACJ,QAAM,QAAQ,yBAAyB,aAAa,mBAAmB,cAAc,UAAU,UAAU,CAAC,KAAK,2BAA2B,QAAQ;AAClJ,QAAM,MAAM;AACZ,QAAM,4BAA4B;AAClC,SAAO;AACT;AACA,SAAS,yBAAyB,SAAS,MAAM;AAC/C,QAAM,QAAQ,IAAI,MAAM,6BAA6B,WAAW,EAAE,EAAE;AACpE,QAAM,0BAA0B,IAAI;AACpC,QAAM,mBAAmB;AACzB,SAAO;AACT;AACA,SAAS,sCAAsC,OAAO;AACpD,SAAO,2BAA2B,KAAK,KAAK,UAAU,MAAM,GAAG;AACjE;AACA,SAAS,2BAA2B,OAAO;AACzC,SAAO,CAAC,CAAC,SAAS,MAAM,0BAA0B;AACpD;AACA,IAAI,qCAAqC;AACzC,IAAM,iBAAiB,CAAC,cAAc,oBAAoB,cAAc,4BAAwB,sBAAI,OAAK;AACvG,MAAI,eAAe,oBAAoB,EAAE,mBAAmB,EAAE,oBAAoB,cAAc,mBAAmB,EAAE,SAAS,YAAY;AAC1I,SAAO;AACT,CAAC;AACD,IAAM,iBAAN,MAAqB;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,oBAAoB,aAAa,WAAW,cAAc,qBAAqB;AACzF,SAAK,qBAAqB;AAC1B,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,SAAS,gBAAgB;AACvB,UAAM,aAAa,KAAK,YAAY;AACpC,UAAM,WAAW,KAAK,YAAY,KAAK,UAAU,QAAQ;AACzD,SAAK,sBAAsB,YAAY,UAAU,cAAc;AAC/D,0BAAsB,KAAK,YAAY,IAAI;AAC3C,SAAK,oBAAoB,YAAY,UAAU,cAAc;AAAA,EAC/D;AAAA;AAAA,EAEA,sBAAsB,YAAY,UAAU,UAAU;AACpD,UAAM,WAAW,kBAAkB,QAAQ;AAE3C,eAAW,SAAS,QAAQ,iBAAe;AACzC,YAAM,kBAAkB,YAAY,MAAM;AAC1C,WAAK,iBAAiB,aAAa,SAAS,eAAe,GAAG,QAAQ;AACtE,aAAO,SAAS,eAAe;AAAA,IACjC,CAAC;AAED,WAAO,OAAO,QAAQ,EAAE,QAAQ,OAAK;AACnC,WAAK,8BAA8B,GAAG,QAAQ;AAAA,IAChD,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB,YAAY,UAAU,eAAe;AACpD,UAAM,SAAS,WAAW;AAC1B,UAAM,OAAO,WAAW,SAAS,QAAQ;AACzC,QAAI,WAAW,MAAM;AAEnB,UAAI,OAAO,WAAW;AAEpB,cAAM,UAAU,cAAc,WAAW,OAAO,MAAM;AACtD,YAAI,SAAS;AACX,eAAK,sBAAsB,YAAY,UAAU,QAAQ,QAAQ;AAAA,QACnE;AAAA,MACF,OAAO;AAEL,aAAK,sBAAsB,YAAY,UAAU,aAAa;AAAA,MAChE;AAAA,IACF,OAAO;AACL,UAAI,MAAM;AAER,aAAK,8BAA8B,UAAU,aAAa;AAAA,MAC5D;AAAA,IACF;AAAA,EACF;AAAA,EACA,8BAA8B,OAAO,gBAAgB;AAGnD,QAAI,MAAM,MAAM,aAAa,KAAK,mBAAmB,aAAa,MAAM,MAAM,QAAQ,GAAG;AACvF,WAAK,2BAA2B,OAAO,cAAc;AAAA,IACvD,OAAO;AACL,WAAK,yBAAyB,OAAO,cAAc;AAAA,IACrD;AAAA,EACF;AAAA,EACA,2BAA2B,OAAO,gBAAgB;AAChD,UAAM,UAAU,eAAe,WAAW,MAAM,MAAM,MAAM;AAC5D,UAAM,WAAW,WAAW,MAAM,MAAM,YAAY,QAAQ,WAAW;AACvE,UAAM,WAAW,kBAAkB,KAAK;AACxC,eAAW,YAAY,OAAO,OAAO,QAAQ,GAAG;AAC9C,WAAK,8BAA8B,UAAU,QAAQ;AAAA,IACvD;AACA,QAAI,WAAW,QAAQ,QAAQ;AAC7B,YAAM,eAAe,QAAQ,OAAO,OAAO;AAC3C,YAAMY,YAAW,QAAQ,SAAS,oBAAoB;AACtD,WAAK,mBAAmB,MAAM,MAAM,MAAM,UAAU;AAAA,QAClD;AAAA,QACA;AAAA,QACA,UAAAA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,yBAAyB,OAAO,gBAAgB;AAC9C,UAAM,UAAU,eAAe,WAAW,MAAM,MAAM,MAAM;AAG5D,UAAM,WAAW,WAAW,MAAM,MAAM,YAAY,QAAQ,WAAW;AACvE,UAAM,WAAW,kBAAkB,KAAK;AACxC,eAAW,YAAY,OAAO,OAAO,QAAQ,GAAG;AAC9C,WAAK,8BAA8B,UAAU,QAAQ;AAAA,IACvD;AACA,QAAI,SAAS;AACX,UAAI,QAAQ,QAAQ;AAElB,gBAAQ,OAAO,WAAW;AAE1B,gBAAQ,SAAS,oBAAoB;AAAA,MACvC;AAIA,cAAQ,YAAY;AACpB,cAAQ,QAAQ;AAAA,IAClB;AAAA,EACF;AAAA,EACA,oBAAoB,YAAY,UAAU,UAAU;AAClD,UAAM,WAAW,kBAAkB,QAAQ;AAC3C,eAAW,SAAS,QAAQ,OAAK;AAC/B,WAAK,eAAe,GAAG,SAAS,EAAE,MAAM,MAAM,GAAG,QAAQ;AACzD,WAAK,aAAa,IAAI,cAAc,EAAE,MAAM,QAAQ,CAAC;AAAA,IACvD,CAAC;AACD,QAAI,WAAW,SAAS,QAAQ;AAC9B,WAAK,aAAa,IAAI,mBAAmB,WAAW,MAAM,QAAQ,CAAC;AAAA,IACrE;AAAA,EACF;AAAA,EACA,eAAe,YAAY,UAAU,gBAAgB;AACnD,UAAM,SAAS,WAAW;AAC1B,UAAM,OAAO,WAAW,SAAS,QAAQ;AACzC,0BAAsB,MAAM;AAE5B,QAAI,WAAW,MAAM;AACnB,UAAI,OAAO,WAAW;AAEpB,cAAM,UAAU,eAAe,mBAAmB,OAAO,MAAM;AAC/D,aAAK,oBAAoB,YAAY,UAAU,QAAQ,QAAQ;AAAA,MACjE,OAAO;AAEL,aAAK,oBAAoB,YAAY,UAAU,cAAc;AAAA,MAC/D;AAAA,IACF,OAAO;AACL,UAAI,OAAO,WAAW;AAEpB,cAAM,UAAU,eAAe,mBAAmB,OAAO,MAAM;AAC/D,YAAI,KAAK,mBAAmB,aAAa,OAAO,QAAQ,GAAG;AACzD,gBAAM,SAAS,KAAK,mBAAmB,SAAS,OAAO,QAAQ;AAC/D,eAAK,mBAAmB,MAAM,OAAO,UAAU,IAAI;AACnD,kBAAQ,SAAS,mBAAmB,OAAO,QAAQ;AACnD,kBAAQ,YAAY,OAAO;AAC3B,kBAAQ,QAAQ,OAAO,MAAM;AAC7B,cAAI,QAAQ,QAAQ;AAGlB,oBAAQ,OAAO,OAAO,OAAO,cAAc,OAAO,MAAM,KAAK;AAAA,UAC/D;AACA,gCAAsB,OAAO,MAAM,KAAK;AACxC,eAAK,oBAAoB,YAAY,MAAM,QAAQ,QAAQ;AAAA,QAC7D,OAAO;AACL,kBAAQ,YAAY;AACpB,kBAAQ,QAAQ;AAChB,cAAI,QAAQ,QAAQ;AAGlB,oBAAQ,OAAO,aAAa,QAAQ,QAAQ,QAAQ;AAAA,UACtD;AACA,eAAK,oBAAoB,YAAY,MAAM,QAAQ,QAAQ;AAAA,QAC7D;AAAA,MACF,OAAO;AAEL,aAAK,oBAAoB,YAAY,MAAM,cAAc;AAAA,MAC3D;AAAA,IACF;AACA,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,YAAM,UAAU,eAAe,mBAAmB,OAAO,MAAM;AAC/D,YAAM,SAAS,QAAQ;AACvB,UAAI,UAAU,KAAK,uBAAuB,CAAC,OAAO,oCAAoC,CAAC,oCAAoC;AACzH,gBAAQ,KAAK,0IAA+I;AAC5J,6CAAqC;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,cAAN,MAAkB;AAAA,EAChB;AAAA,EACA;AAAA,EACA,YAAY,MAAM;AAChB,SAAK,OAAO;AACZ,SAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC;AAAA,EAC7C;AACF;AACA,IAAM,gBAAN,MAAoB;AAAA,EAClB;AAAA,EACA;AAAA,EACA,YAAY,WAAW,OAAO;AAC5B,SAAK,YAAY;AACjB,SAAK,QAAQ;AAAA,EACf;AACF;AACA,SAAS,kBAAkB,QAAQ,MAAM,gBAAgB;AACvD,QAAM,aAAa,OAAO;AAC1B,QAAM,WAAW,OAAO,KAAK,QAAQ;AACrC,SAAO,oBAAoB,YAAY,UAAU,gBAAgB,CAAC,WAAW,KAAK,CAAC;AACrF;AACA,SAAS,oBAAoB,GAAG;AAC9B,QAAM,mBAAmB,EAAE,cAAc,EAAE,YAAY,mBAAmB;AAC1E,MAAI,CAAC,oBAAoB,iBAAiB,WAAW,EAAG,QAAO;AAC/D,SAAO;AAAA,IACL,MAAM;AAAA,IACN,QAAQ;AAAA,EACV;AACF;AACA,SAAS,2BAA2B,iBAAiB,UAAU;AAC7D,QAAM,YAAY,OAAO;AACzB,QAAM,SAAS,SAAS,IAAI,iBAAiB,SAAS;AACtD,MAAI,WAAW,WAAW;AACxB,QAAI,OAAO,oBAAoB,cAAc,CAAC,aAAc,eAAe,GAAG;AAE5E,aAAO;AAAA,IACT,OAAO;AAEL,aAAO,SAAS,IAAI,eAAe;AAAA,IACrC;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,oBAAoB,YAAY,UAAU,UAAU,YAAY,SAAS;AAAA,EAChF,qBAAqB,CAAC;AAAA,EACtB,mBAAmB,CAAC;AACtB,GAAG;AACD,QAAM,eAAe,kBAAkB,QAAQ;AAE/C,aAAW,SAAS,QAAQ,OAAK;AAC/B,mBAAe,GAAG,aAAa,EAAE,MAAM,MAAM,GAAG,UAAU,WAAW,OAAO,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM;AAC9F,WAAO,aAAa,EAAE,MAAM,MAAM;AAAA,EACpC,CAAC;AAED,SAAO,QAAQ,YAAY,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM,8BAA8B,GAAG,SAAS,WAAW,CAAC,GAAG,MAAM,CAAC;AACjH,SAAO;AACT;AACA,SAAS,eAAe,YAAY,UAAU,gBAAgB,YAAY,SAAS;AAAA,EACjF,qBAAqB,CAAC;AAAA,EACtB,mBAAmB,CAAC;AACtB,GAAG;AACD,QAAM,SAAS,WAAW;AAC1B,QAAM,OAAO,WAAW,SAAS,QAAQ;AACzC,QAAM,UAAU,iBAAiB,eAAe,WAAW,WAAW,MAAM,MAAM,IAAI;AAEtF,MAAI,QAAQ,OAAO,gBAAgB,KAAK,aAAa;AACnD,UAAM,YAAY,4BAA4B,MAAM,QAAQ,OAAO,YAAY,qBAAqB;AACpG,QAAI,WAAW;AACb,aAAO,kBAAkB,KAAK,IAAI,YAAY,UAAU,CAAC;AAAA,IAC3D,OAAO;AAEL,aAAO,OAAO,KAAK;AACnB,aAAO,gBAAgB,KAAK;AAAA,IAC9B;AAEA,QAAI,OAAO,WAAW;AACpB,0BAAoB,YAAY,UAAU,UAAU,QAAQ,WAAW,MAAM,YAAY,MAAM;AAAA,IAEjG,OAAO;AACL,0BAAoB,YAAY,UAAU,gBAAgB,YAAY,MAAM;AAAA,IAC9E;AACA,QAAI,aAAa,WAAW,QAAQ,UAAU,QAAQ,OAAO,aAAa;AACxE,aAAO,oBAAoB,KAAK,IAAI,cAAc,QAAQ,OAAO,WAAW,IAAI,CAAC;AAAA,IACnF;AAAA,EACF,OAAO;AACL,QAAI,MAAM;AACR,oCAA8B,UAAU,SAAS,MAAM;AAAA,IACzD;AACA,WAAO,kBAAkB,KAAK,IAAI,YAAY,UAAU,CAAC;AAEzD,QAAI,OAAO,WAAW;AACpB,0BAAoB,YAAY,MAAM,UAAU,QAAQ,WAAW,MAAM,YAAY,MAAM;AAAA,IAE7F,OAAO;AACL,0BAAoB,YAAY,MAAM,gBAAgB,YAAY,MAAM;AAAA,IAC1E;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,4BAA4B,MAAM,QAAQ,MAAM;AACvD,MAAI,OAAO,SAAS,YAAY;AAC9B,WAAO,KAAK,MAAM,MAAM;AAAA,EAC1B;AACA,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO,CAAC,UAAU,KAAK,KAAK,OAAO,GAAG;AAAA,IACxC,KAAK;AACH,aAAO,CAAC,UAAU,KAAK,KAAK,OAAO,GAAG,KAAK,CAAC,aAAa,KAAK,aAAa,OAAO,WAAW;AAAA,IAC/F,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO,CAAC,0BAA0B,MAAM,MAAM,KAAK,CAAC,aAAa,KAAK,aAAa,OAAO,WAAW;AAAA,IACvG,KAAK;AAAA,IACL;AACE,aAAO,CAAC,0BAA0B,MAAM,MAAM;AAAA,EAClD;AACF;AACA,SAAS,8BAA8B,OAAO,SAAS,QAAQ;AAC7D,QAAM,WAAW,kBAAkB,KAAK;AACxC,QAAM,IAAI,MAAM;AAChB,SAAO,QAAQ,QAAQ,EAAE,QAAQ,CAAC,CAAC,WAAW,IAAI,MAAM;AACtD,QAAI,CAAC,EAAE,WAAW;AAChB,oCAA8B,MAAM,SAAS,MAAM;AAAA,IACrD,WAAW,SAAS;AAClB,oCAA8B,MAAM,QAAQ,SAAS,WAAW,SAAS,GAAG,MAAM;AAAA,IACpF,OAAO;AACL,oCAA8B,MAAM,MAAM,MAAM;AAAA,IAClD;AAAA,EACF,CAAC;AACD,MAAI,CAAC,EAAE,WAAW;AAChB,WAAO,oBAAoB,KAAK,IAAI,cAAc,MAAM,CAAC,CAAC;AAAA,EAC5D,WAAW,WAAW,QAAQ,UAAU,QAAQ,OAAO,aAAa;AAClE,WAAO,oBAAoB,KAAK,IAAI,cAAc,QAAQ,OAAO,WAAW,CAAC,CAAC;AAAA,EAChF,OAAO;AACL,WAAO,oBAAoB,KAAK,IAAI,cAAc,MAAM,CAAC,CAAC;AAAA,EAC5D;AACF;AAeA,SAAS,WAAW,GAAG;AACrB,SAAO,OAAO,MAAM;AACtB;AACA,SAAS,UAAU,GAAG;AACpB,SAAO,OAAO,MAAM;AACtB;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,SAAS,WAAW,MAAM,OAAO;AAC1C;AACA,SAAS,cAAc,OAAO;AAC5B,SAAO,SAAS,WAAW,MAAM,WAAW;AAC9C;AACA,SAAS,mBAAmB,OAAO;AACjC,SAAO,SAAS,WAAW,MAAM,gBAAgB;AACnD;AACA,SAAS,gBAAgB,OAAO;AAC9B,SAAO,SAAS,WAAW,MAAM,aAAa;AAChD;AACA,SAAS,WAAW,OAAO;AACzB,SAAO,SAAS,WAAW,MAAM,QAAQ;AAC3C;AACA,SAAS,aAAa,GAAG;AACvB,SAAO,aAAa,0BAAc,GAAG,SAAS;AAChD;AACA,IAAM,gBAA+B,OAAO,eAAe;AAC3D,SAAS,wBAAwB;AAC/B,aAAO,4BAAU,SAAO;AACtB,eAAO,2BAAc,IAAI,IAAI,OAAK,EAAE,SAAK,uBAAK,CAAC,OAAG,4BAAU,aAAa,CAAC,CAAC,CAAC,EAAE,SAAK,sBAAI,aAAW;AAChG,iBAAW,UAAU,SAAS;AAC5B,YAAI,WAAW,MAAM;AAEnB;AAAA,QACF,WAAW,WAAW,eAAe;AAEnC,iBAAO;AAAA,QACT,WAAW,WAAW,SAAS,WAAW,MAAM,GAAG;AAIjD,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACT,CAAC,OAAG,yBAAO,UAAQ,SAAS,aAAa,OAAG,uBAAK,CAAC,CAAC;AAAA,EACrD,CAAC;AACH;AACA,SAAS,WAAW,KAAK;AACvB,SAAO,UAAU,GAAG,KAAK,eAAe;AAC1C;AACA,SAAS,YAAY,UAAU,cAAc;AAC3C,aAAO,2BAAS,OAAK;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,oBAAoB,WAAW,KAAK,kBAAkB,WAAW,GAAG;AACtE,iBAAO,gBAAG,iCACL,IADK;AAAA,QAER,cAAc;AAAA,MAChB,EAAC;AAAA,IACH;AACA,WAAO,uBAAuB,qBAAqB,gBAAgB,iBAAiB,QAAQ,EAAE,SAAK,2BAAS,mBAAiB;AAC3H,aAAO,iBAAiB,UAAU,aAAa,IAAI,qBAAqB,gBAAgB,mBAAmB,UAAU,YAAY,QAAI,gBAAG,aAAa;AAAA,IACvJ,CAAC,OAAG,sBAAI,kBAAiB,iCACpB,IADoB;AAAA,MAEvB;AAAA,IACF,EAAE,CAAC;AAAA,EACL,CAAC;AACH;AACA,SAAS,uBAAuB,QAAQ,WAAW,SAAS,UAAU;AACpE,aAAO,kBAAK,MAAM,EAAE,SAAK,2BAAS,WAAS,iBAAiB,MAAM,WAAW,MAAM,OAAO,SAAS,WAAW,QAAQ,CAAC,OAAG,wBAAM,YAAU;AACxI,WAAO,WAAW;AAAA,EACpB,GAAG,IAAI,CAAC;AACV;AACA,SAAS,qBAAqB,gBAAgB,QAAQ,UAAU,cAAc;AAC5E,aAAO,kBAAK,MAAM,EAAE,SAAK,4BAAU,WAAS;AAC1C,eAAO,oBAAO,yBAAyB,MAAM,MAAM,QAAQ,YAAY,GAAG,oBAAoB,MAAM,OAAO,YAAY,GAAG,oBAAoB,gBAAgB,MAAM,MAAM,QAAQ,GAAG,eAAe,gBAAgB,MAAM,OAAO,QAAQ,CAAC;AAAA,EAC5O,CAAC,OAAG,wBAAM,YAAU;AAClB,WAAO,WAAW;AAAA,EACpB,GAAG,IAAI,CAAC;AACV;AASA,SAAS,oBAAoB,UAAU,cAAc;AACnD,MAAI,aAAa,QAAQ,cAAc;AACrC,iBAAa,IAAI,gBAAgB,QAAQ,CAAC;AAAA,EAC5C;AACA,aAAO,gBAAG,IAAI;AAChB;AASA,SAAS,yBAAyB,UAAU,cAAc;AACxD,MAAI,aAAa,QAAQ,cAAc;AACrC,iBAAa,IAAI,qBAAqB,QAAQ,CAAC;AAAA,EACjD;AACA,aAAO,gBAAG,IAAI;AAChB;AACA,SAAS,eAAe,WAAW,WAAW,UAAU;AACtD,QAAM,cAAc,UAAU,cAAc,UAAU,YAAY,cAAc;AAChF,MAAI,CAAC,eAAe,YAAY,WAAW,EAAG,YAAO,gBAAG,IAAI;AAC5D,QAAM,yBAAyB,YAAY,IAAI,CAAAC,iBAAe;AAC5D,eAAO,mBAAM,MAAM;AACjB,YAAM,kBAAkB,wBAAwB,SAAS,KAAK;AAC9D,YAAM,QAAQ,2BAA2BA,cAAa,eAAe;AACrE,YAAM,WAAW,cAAc,KAAK,IAAI,MAAM,YAAY,WAAW,SAAS,IAAI,sBAAsB,iBAAiB,MAAM,MAAM,WAAW,SAAS,CAAC;AAC1J,aAAO,mBAAmB,QAAQ,EAAE,SAAK,wBAAM,CAAC;AAAA,IAClD,CAAC;AAAA,EACH,CAAC;AACD,aAAO,gBAAG,sBAAsB,EAAE,KAAK,sBAAsB,CAAC;AAChE;AACA,SAAS,oBAAoB,WAAW,MAAM,UAAU;AACtD,QAAM,YAAY,KAAK,KAAK,SAAS,CAAC;AACtC,QAAM,yBAAyB,KAAK,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,QAAQ,EAAE,IAAI,OAAK,oBAAoB,CAAC,CAAC,EAAE,OAAO,OAAK,MAAM,IAAI;AAC/H,QAAM,+BAA+B,uBAAuB,IAAI,OAAK;AACnE,eAAO,mBAAM,MAAM;AACjB,YAAM,eAAe,EAAE,OAAO,IAAI,sBAAoB;AACpD,cAAM,kBAAkB,wBAAwB,EAAE,IAAI,KAAK;AAC3D,cAAM,QAAQ,2BAA2B,kBAAkB,eAAe;AAC1E,cAAM,WAAW,mBAAmB,KAAK,IAAI,MAAM,iBAAiB,WAAW,SAAS,IAAI,sBAAsB,iBAAiB,MAAM,MAAM,WAAW,SAAS,CAAC;AACpK,eAAO,mBAAmB,QAAQ,EAAE,SAAK,wBAAM,CAAC;AAAA,MAClD,CAAC;AACD,iBAAO,gBAAG,YAAY,EAAE,KAAK,sBAAsB,CAAC;AAAA,IACtD,CAAC;AAAA,EACH,CAAC;AACD,aAAO,gBAAG,4BAA4B,EAAE,KAAK,sBAAsB,CAAC;AACtE;AACA,SAAS,iBAAiB,WAAW,SAAS,SAAS,WAAW,UAAU;AAC1E,QAAM,gBAAgB,WAAW,QAAQ,cAAc,QAAQ,YAAY,gBAAgB;AAC3F,MAAI,CAAC,iBAAiB,cAAc,WAAW,EAAG,YAAO,gBAAG,IAAI;AAChE,QAAM,2BAA2B,cAAc,IAAI,OAAK;AACtD,UAAM,kBAAkB,wBAAwB,OAAO,KAAK;AAC5D,UAAM,QAAQ,2BAA2B,GAAG,eAAe;AAC3D,UAAM,WAAW,gBAAgB,KAAK,IAAI,MAAM,cAAc,WAAW,SAAS,SAAS,SAAS,IAAI,sBAAsB,iBAAiB,MAAM,MAAM,WAAW,SAAS,SAAS,SAAS,CAAC;AAClM,WAAO,mBAAmB,QAAQ,EAAE,SAAK,wBAAM,CAAC;AAAA,EAClD,CAAC;AACD,aAAO,gBAAG,wBAAwB,EAAE,KAAK,sBAAsB,CAAC;AAClE;AACA,SAAS,iBAAiB,UAAU,OAAO,UAAU,eAAe;AAClE,QAAM,UAAU,MAAM;AACtB,MAAI,YAAY,UAAa,QAAQ,WAAW,GAAG;AACjD,eAAO,gBAAG,IAAI;AAAA,EAChB;AACA,QAAM,qBAAqB,QAAQ,IAAI,oBAAkB;AACvD,UAAM,QAAQ,2BAA2B,gBAAgB,QAAQ;AACjE,UAAM,WAAW,UAAU,KAAK,IAAI,MAAM,QAAQ,OAAO,QAAQ,IAAI,sBAAsB,UAAU,MAAM,MAAM,OAAO,QAAQ,CAAC;AACjI,WAAO,mBAAmB,QAAQ;AAAA,EACpC,CAAC;AACD,aAAO,gBAAG,kBAAkB,EAAE,KAAK,sBAAsB,GAAG,kBAAkB,aAAa,CAAC;AAC9F;AACA,SAAS,kBAAkB,eAAe;AACxC,aAAO,sBAAK,sBAAI,YAAU;AACxB,QAAI,OAAO,WAAW,UAAW;AACjC,UAAM,2BAA2B,eAAe,MAAM;AAAA,EACxD,CAAC,OAAG,sBAAI,YAAU,WAAW,IAAI,CAAC;AACpC;AACA,SAAS,kBAAkB,UAAU,OAAO,UAAU,eAAe;AACnE,QAAM,WAAW,MAAM;AACvB,MAAI,CAAC,YAAY,SAAS,WAAW,EAAG,YAAO,gBAAG,IAAI;AACtD,QAAM,sBAAsB,SAAS,IAAI,oBAAkB;AACzD,UAAM,QAAQ,2BAA2B,gBAAgB,QAAQ;AACjE,UAAM,WAAW,WAAW,KAAK,IAAI,MAAM,SAAS,OAAO,QAAQ,IAAI,sBAAsB,UAAU,MAAM,MAAM,OAAO,QAAQ,CAAC;AACnI,WAAO,mBAAmB,QAAQ;AAAA,EACpC,CAAC;AACD,aAAO,gBAAG,mBAAmB,EAAE,KAAK,sBAAsB,GAAG,kBAAkB,aAAa,CAAC;AAC/F;AACA,IAAM,UAAN,MAAc;AAAA,EACZ;AAAA,EACA,YAAY,cAAc;AACxB,SAAK,eAAe,gBAAgB;AAAA,EACtC;AACF;AACA,IAAM,mBAAN,cAA+B,MAAM;AAAA,EACnC;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AACN,SAAK,UAAU;AAAA,EACjB;AACF;AACA,SAAS,UAAU,cAAc;AAC/B,aAAO,wBAAW,IAAI,QAAQ,YAAY,CAAC;AAC7C;AACA,SAAS,qBAAqB,YAAY;AACxC,aAAO,wBAAW,IAAI,aAAc,MAAoD,OAAO,cAAc,eAAe,cAAc,gEAAgE,UAAU,GAAG,CAAC;AAC1N;AACA,SAAS,aAAa,OAAO;AAC3B,aAAO,wBAAW,0BAA0B,OAAO,cAAc,eAAe,cAAc,+DAA+D,MAAM,IAAI,qBAAqB,2BAA2B,aAAa,CAAC;AACvO;AACA,IAAM,iBAAN,MAAqB;AAAA,EACnB;AAAA,EACA;AAAA,EACA,YAAY,eAAe,SAAS;AAClC,SAAK,gBAAgB;AACrB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,mBAAmB,OAAO,SAAS;AACjC,QAAI,MAAM,CAAC;AACX,QAAI,IAAI,QAAQ;AAChB,WAAO,MAAM;AACX,YAAM,IAAI,OAAO,EAAE,QAAQ;AAC3B,UAAI,EAAE,qBAAqB,GAAG;AAC5B,mBAAO,gBAAG,GAAG;AAAA,MACf;AACA,UAAI,EAAE,mBAAmB,KAAK,CAAC,EAAE,SAAS,cAAc,GAAG;AACzD,eAAO,qBAAqB,GAAG,MAAM,UAAU,EAAE;AAAA,MACnD;AACA,UAAI,EAAE,SAAS,cAAc;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,sBAAsB,UAAU,YAAY,WAAW,iBAAiB,UAAU;AAChF,QAAI,OAAO,eAAe,UAAU;AAClC,YAAM,eAAe;AACrB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,cAAc,sBAAsB,UAAU,MAAM,aAAa;AAAA,QACrE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC,CAAC;AACF,UAAI,uBAAuB,SAAS;AAClC,cAAM,IAAI,iBAAiB,WAAW;AAAA,MACxC;AACA,mBAAa;AAAA,IACf;AACA,UAAM,UAAU,KAAK,2BAA2B,YAAY,KAAK,cAAc,MAAM,UAAU,GAAG,UAAU,SAAS;AACrH,QAAI,WAAW,CAAC,MAAM,KAAK;AACzB,YAAM,IAAI,iBAAiB,OAAO;AAAA,IACpC;AACA,WAAO;AAAA,EACT;AAAA,EACA,2BAA2B,YAAY,SAAS,UAAU,WAAW;AACnE,UAAM,UAAU,KAAK,mBAAmB,YAAY,QAAQ,MAAM,UAAU,SAAS;AACrF,WAAO,IAAI,QAAQ,SAAS,KAAK,kBAAkB,QAAQ,aAAa,KAAK,QAAQ,WAAW,GAAG,QAAQ,QAAQ;AAAA,EACrH;AAAA,EACA,kBAAkB,kBAAkB,cAAc;AAChD,UAAM,MAAM,CAAC;AACb,WAAO,QAAQ,gBAAgB,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM;AACnD,YAAM,kBAAkB,OAAO,MAAM,YAAY,EAAE,CAAC,MAAM;AAC1D,UAAI,iBAAiB;AACnB,cAAM,aAAa,EAAE,UAAU,CAAC;AAChC,YAAI,CAAC,IAAI,aAAa,UAAU;AAAA,MAClC,OAAO;AACL,YAAI,CAAC,IAAI;AAAA,MACX;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB,YAAY,OAAO,UAAU,WAAW;AACzD,UAAM,kBAAkB,KAAK,eAAe,YAAY,MAAM,UAAU,UAAU,SAAS;AAC3F,QAAI,WAAW,CAAC;AAChB,WAAO,QAAQ,MAAM,QAAQ,EAAE,QAAQ,CAAC,CAAC,MAAM,KAAK,MAAM;AACxD,eAAS,IAAI,IAAI,KAAK,mBAAmB,YAAY,OAAO,UAAU,SAAS;AAAA,IACjF,CAAC;AACD,WAAO,IAAI,gBAAgB,iBAAiB,QAAQ;AAAA,EACtD;AAAA,EACA,eAAe,YAAY,oBAAoB,gBAAgB,WAAW;AACxE,WAAO,mBAAmB,IAAI,OAAK,EAAE,KAAK,CAAC,MAAM,MAAM,KAAK,aAAa,YAAY,GAAG,SAAS,IAAI,KAAK,aAAa,GAAG,cAAc,CAAC;AAAA,EAC3I;AAAA,EACA,aAAa,YAAY,sBAAsB,WAAW;AACxD,UAAM,MAAM,UAAU,qBAAqB,KAAK,UAAU,CAAC,CAAC;AAC5D,QAAI,CAAC,IAAK,OAAM,IAAI,aAAc,OAA+C,OAAO,cAAc,eAAe,cAAc,uBAAuB,UAAU,mBAAmB,qBAAqB,IAAI,IAAI;AACpN,WAAO;AAAA,EACT;AAAA,EACA,aAAa,sBAAsB,gBAAgB;AACjD,QAAI,MAAM;AACV,eAAW,KAAK,gBAAgB;AAC9B,UAAI,EAAE,SAAS,qBAAqB,MAAM;AACxC,uBAAe,OAAO,GAAG;AACzB,eAAO;AAAA,MACT;AACA;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,UAAU;AAAA,EACd,SAAS;AAAA,EACT,kBAAkB,CAAC;AAAA,EACnB,mBAAmB,CAAC;AAAA,EACpB,YAAY,CAAC;AAAA,EACb,yBAAyB,CAAC;AAC5B;AACA,SAAS,gBAAgB,cAAc,OAAO,UAAU,UAAU,eAAe;AAC/E,QAAM,SAAS,MAAM,cAAc,OAAO,QAAQ;AAClD,MAAI,CAAC,OAAO,SAAS;AACnB,eAAO,gBAAG,MAAM;AAAA,EAClB;AAGA,aAAW,iCAAiC,OAAO,QAAQ;AAC3D,SAAO,kBAAkB,UAAU,OAAO,UAAU,aAAa,EAAE,SAAK,sBAAI,OAAK,MAAM,OAAO,SAAS,mBAClG,QACJ,CAAC;AACJ;AACA,SAAS,MAAM,cAAc,OAAO,UAAU;AAC5C,MAAI,MAAM,SAAS,MAAM;AACvB,WAAO,0BAA0B,QAAQ;AAAA,EAC3C;AACA,MAAI,MAAM,SAAS,IAAI;AACrB,QAAI,MAAM,cAAc,WAAW,aAAa,YAAY,KAAK,SAAS,SAAS,IAAI;AACrF,aAAO,mBACF;AAAA,IAEP;AACA,WAAO;AAAA,MACL,SAAS;AAAA,MACT,kBAAkB,CAAC;AAAA,MACnB,mBAAmB;AAAA,MACnB,YAAY,CAAC;AAAA,MACb,yBAAyB,CAAC;AAAA,IAC5B;AAAA,EACF;AACA,QAAM,UAAU,MAAM,WAAW;AACjC,QAAM,MAAM,QAAQ,UAAU,cAAc,KAAK;AACjD,MAAI,CAAC,IAAK,QAAO,mBACZ;AAEL,QAAM,YAAY,CAAC;AACnB,SAAO,QAAQ,IAAI,aAAa,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM;AACtD,cAAU,CAAC,IAAI,EAAE;AAAA,EACnB,CAAC;AACD,QAAM,aAAa,IAAI,SAAS,SAAS,IAAI,kCACxC,YACA,IAAI,SAAS,IAAI,SAAS,SAAS,CAAC,EAAE,cACvC;AACJ,SAAO;AAAA,IACL,SAAS;AAAA,IACT,kBAAkB,IAAI;AAAA,IACtB,mBAAmB,SAAS,MAAM,IAAI,SAAS,MAAM;AAAA;AAAA,IAErD;AAAA,IACA,yBAAyB,IAAI,aAAa,CAAC;AAAA,EAC7C;AACF;AACA,SAAS,0BAA0B,UAAU;AAC3C,SAAO;AAAA,IACL,SAAS;AAAA,IACT,YAAY,SAAS,SAAS,IAAI,KAAK,QAAQ,EAAE,aAAa,CAAC;AAAA,IAC/D,kBAAkB;AAAA,IAClB,mBAAmB,CAAC;AAAA,IACpB,yBAAyB,CAAC;AAAA,EAC5B;AACF;AACA,SAAS,MAAM,cAAc,kBAAkB,gBAAgB,QAAQ;AACrE,MAAI,eAAe,SAAS,KAAK,yCAAyC,cAAc,gBAAgB,MAAM,GAAG;AAC/G,UAAMC,KAAI,IAAI,gBAAgB,kBAAkB,4BAA4B,QAAQ,IAAI,gBAAgB,gBAAgB,aAAa,QAAQ,CAAC,CAAC;AAC/I,WAAO;AAAA,MACL,cAAcA;AAAA,MACd,gBAAgB,CAAC;AAAA,IACnB;AAAA,EACF;AACA,MAAI,eAAe,WAAW,KAAK,yBAAyB,cAAc,gBAAgB,MAAM,GAAG;AACjG,UAAMA,KAAI,IAAI,gBAAgB,aAAa,UAAU,gCAAgC,cAAc,gBAAgB,QAAQ,aAAa,QAAQ,CAAC;AACjJ,WAAO;AAAA,MACL,cAAcA;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACA,QAAM,IAAI,IAAI,gBAAgB,aAAa,UAAU,aAAa,QAAQ;AAC1E,SAAO;AAAA,IACL,cAAc;AAAA,IACd;AAAA,EACF;AACF;AACA,SAAS,gCAAgC,cAAc,gBAAgB,QAAQ,UAAU;AACvF,QAAM,MAAM,CAAC;AACb,aAAW,KAAK,QAAQ;AACtB,QAAI,eAAe,cAAc,gBAAgB,CAAC,KAAK,CAAC,SAAS,UAAU,CAAC,CAAC,GAAG;AAC9E,YAAM,IAAI,IAAI,gBAAgB,CAAC,GAAG,CAAC,CAAC;AACpC,UAAI,UAAU,CAAC,CAAC,IAAI;AAAA,IACtB;AAAA,EACF;AACA,SAAO,kCACF,WACA;AAEP;AACA,SAAS,4BAA4B,QAAQ,gBAAgB;AAC3D,QAAM,MAAM,CAAC;AACb,MAAI,cAAc,IAAI;AACtB,aAAW,KAAK,QAAQ;AACtB,QAAI,EAAE,SAAS,MAAM,UAAU,CAAC,MAAM,gBAAgB;AACpD,YAAM,IAAI,IAAI,gBAAgB,CAAC,GAAG,CAAC,CAAC;AACpC,UAAI,UAAU,CAAC,CAAC,IAAI;AAAA,IACtB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,yCAAyC,cAAc,gBAAgB,QAAQ;AACtF,SAAO,OAAO,KAAK,OAAK,eAAe,cAAc,gBAAgB,CAAC,KAAK,UAAU,CAAC,MAAM,cAAc;AAC5G;AACA,SAAS,yBAAyB,cAAc,gBAAgB,QAAQ;AACtE,SAAO,OAAO,KAAK,OAAK,eAAe,cAAc,gBAAgB,CAAC,CAAC;AACzE;AACA,SAAS,eAAe,cAAc,gBAAgB,GAAG;AACvD,OAAK,aAAa,YAAY,KAAK,eAAe,SAAS,MAAM,EAAE,cAAc,QAAQ;AACvF,WAAO;AAAA,EACT;AACA,SAAO,EAAE,SAAS;AACpB;AACA,SAAS,iBAAiB,cAAc,UAAU,QAAQ;AACxD,SAAO,SAAS,WAAW,KAAK,CAAC,aAAa,SAAS,MAAM;AAC/D;AAOA,IAAM,mBAAN,MAAuB;AAAC;AACxB,SAAS,YAAY,UAAU,cAAc,mBAAmB,QAAQ,SAAS,eAAe,4BAA4B,aAAa;AACvI,SAAO,IAAI,WAAW,UAAU,cAAc,mBAAmB,QAAQ,SAAS,2BAA2B,aAAa,EAAE,UAAU;AACxI;AACA,IAAM,wBAAwB;AAC9B,IAAM,aAAN,MAAiB;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,wBAAwB;AAAA,EACxB,iBAAiB;AAAA,EACjB,YAAY,UAAU,cAAc,mBAAmB,QAAQ,SAAS,2BAA2B,eAAe;AAChH,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,oBAAoB;AACzB,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,4BAA4B;AACjC,SAAK,gBAAgB;AACrB,SAAK,iBAAiB,IAAI,eAAe,KAAK,eAAe,KAAK,OAAO;AAAA,EAC3E;AAAA,EACA,aAAa,GAAG;AACd,WAAO,IAAI,aAAc,MAAsC,OAAO,cAAc,eAAe,YAAY,0CAA0C,EAAE,YAAY,MAAM,IAAI,EAAE,YAAY,GAAG;AAAA,EACpM;AAAA,EACA,YAAY;AACV,UAAM,mBAAmB,MAAM,KAAK,QAAQ,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,MAAM,EAAE;AACvE,WAAO,KAAK,MAAM,gBAAgB,EAAE,SAAK,sBAAI,CAAC;AAAA,MAC5C;AAAA,MACA;AAAA,IACF,MAAM;AACJ,YAAM,WAAW,IAAI,SAAS,cAAc,QAAQ;AACpD,YAAM,aAAa,IAAI,oBAAoB,IAAI,QAAQ;AACvD,YAAMd,QAAO,0BAA0B,cAAc,CAAC,GAAG,KAAK,QAAQ,aAAa,KAAK,QAAQ,QAAQ;AAIxG,MAAAA,MAAK,cAAc,KAAK,QAAQ;AAChC,iBAAW,MAAM,KAAK,cAAc,UAAUA,KAAI;AAClD,aAAO;AAAA,QACL,OAAO;AAAA,QACP,MAAAA;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,MAAM,kBAAkB;AAGtB,UAAM,eAAe,IAAI,uBAAuB,CAAC,GAAG,OAAO,OAAO,CAAC,CAAC,GAAG,OAAO,OAAO,mBAChF,KAAK,QAAQ,YACjB,GAAG,KAAK,QAAQ,UAAU,OAAO,OAAO,CAAC,CAAC,GAAG,gBAAgB,KAAK,mBAAmB,MAAM,CAAC,CAAC;AAC9F,WAAO,KAAK,oBAAoB,KAAK,UAAU,KAAK,QAAQ,kBAAkB,gBAAgB,YAAY,EAAE,SAAK,sBAAI,cAAY;AAC/H,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC,OAAG,6BAAW,OAAK;AAClB,UAAI,aAAa,kBAAkB;AACjC,aAAK,UAAU,EAAE;AACjB,eAAO,KAAK,MAAM,EAAE,QAAQ,IAAI;AAAA,MAClC;AACA,UAAI,aAAa,SAAS;AACxB,cAAM,KAAK,aAAa,CAAC;AAAA,MAC3B;AACA,YAAM;AAAA,IACR,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,oBAAoB,UAAU,QAAQ,cAAc,QAAQ,aAAa;AACvE,QAAI,aAAa,SAAS,WAAW,KAAK,aAAa,YAAY,GAAG;AACpE,aAAO,KAAK,gBAAgB,UAAU,QAAQ,cAAc,WAAW;AAAA,IACzE;AACA,WAAO,KAAK,eAAe,UAAU,QAAQ,cAAc,aAAa,UAAU,QAAQ,MAAM,WAAW,EAAE,SAAK,sBAAI,WAAS,iBAAiB,WAAW,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;AAAA,EAC1K;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB,UAAU,QAAQ,cAAc,aAAa;AAG3D,UAAM,eAAe,CAAC;AACtB,eAAW,SAAS,OAAO,KAAK,aAAa,QAAQ,GAAG;AACtD,UAAI,UAAU,WAAW;AACvB,qBAAa,QAAQ,KAAK;AAAA,MAC5B,OAAO;AACL,qBAAa,KAAK,KAAK;AAAA,MACzB;AAAA,IACF;AACA,eAAO,kBAAK,YAAY,EAAE,SAAK,4BAAU,iBAAe;AACtD,YAAM,QAAQ,aAAa,SAAS,WAAW;AAI/C,YAAM,eAAe,sBAAsB,QAAQ,WAAW;AAC9D,aAAO,KAAK,oBAAoB,UAAU,cAAc,OAAO,aAAa,WAAW;AAAA,IACzF,CAAC,OAAG,uBAAK,CAAC,UAAU,mBAAmB;AACrC,eAAS,KAAK,GAAG,cAAc;AAC/B,aAAO;AAAA,IACT,CAAC,OAAG,iCAAe,IAAI,OAAG,iBAAAe,MAAO,OAAG,2BAAS,cAAY;AACvD,UAAI,aAAa,KAAM,QAAO,UAAU,YAAY;AAIpD,YAAM,iBAAiB,sBAAsB,QAAQ;AACrD,UAAI,OAAO,cAAc,eAAe,WAAW;AAGjD,kCAA0B,cAAc;AAAA,MAC1C;AACA,kCAA4B,cAAc;AAC1C,iBAAO,gBAAG,cAAc;AAAA,IAC1B,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,eAAe,UAAU,QAAQ,cAAc,UAAU,QAAQ,gBAAgB,aAAa;AAC5F,eAAO,kBAAK,MAAM,EAAE,SAAK,4BAAU,OAAK;AACtC,aAAO,KAAK,2BAA2B,EAAE,aAAa,UAAU,QAAQ,GAAG,cAAc,UAAU,QAAQ,gBAAgB,WAAW,EAAE,SAAK,6BAAW,OAAK;AAC3J,YAAI,aAAa,SAAS;AACxB,qBAAO,gBAAG,IAAI;AAAA,QAChB;AACA,cAAM;AAAA,MACR,CAAC,CAAC;AAAA,IACJ,CAAC,OAAG,wBAAM,OAAK,CAAC,CAAC,CAAC,OAAG,6BAAW,OAAK;AACnC,UAAI,aAAa,CAAC,GAAG;AACnB,YAAI,iBAAiB,cAAc,UAAU,MAAM,GAAG;AACpD,qBAAO,gBAAG,IAAI,iBAAiB,CAAC;AAAA,QAClC;AACA,eAAO,UAAU,YAAY;AAAA,MAC/B;AACA,YAAM;AAAA,IACR,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,2BAA2B,UAAU,QAAQ,OAAO,YAAY,UAAU,QAAQ,gBAAgB,aAAa;AAY7G,QAAI,UAAU,KAAK,MAAM,WAAW,WAAW,kBAAkB,CAAC,eAAe,YAAY,UAAU,KAAK,IAAI;AAC9G,aAAO,UAAU,UAAU;AAAA,IAC7B;AACA,QAAI,MAAM,eAAe,QAAW;AAClC,aAAO,KAAK,yBAAyB,UAAU,YAAY,OAAO,UAAU,QAAQ,WAAW;AAAA,IACjG;AACA,QAAI,KAAK,kBAAkB,gBAAgB;AACzC,aAAO,KAAK,uCAAuC,UAAU,YAAY,QAAQ,OAAO,UAAU,QAAQ,WAAW;AAAA,IACvH;AACA,WAAO,UAAU,UAAU;AAAA,EAC7B;AAAA,EACA,uCAAuC,UAAU,cAAc,QAAQ,OAAO,UAAU,QAAQ,aAAa;AAC3G,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,cAAc,OAAO,QAAQ;AACvC,QAAI,CAAC,QAAS,QAAO,UAAU,YAAY;AAG3C,QAAI,OAAO,MAAM,eAAe,YAAY,MAAM,WAAW,CAAC,MAAM,KAAK;AACvE,WAAK;AACL,UAAI,KAAK,wBAAwB,uBAAuB;AACtD,YAAI,WAAW;AACb,gBAAM,IAAI,aAAc,MAA+C,8DAA8D,KAAK,OAAO,SAAS,MAAM,UAAU;AAAA,kIAAiJ;AAAA,QAC7T;AACA,aAAK,iBAAiB;AAAA,MACxB;AAAA,IACF;AACA,UAAM,kBAAkB,IAAI,uBAAuB,UAAU,YAAY,OAAO,OAAO,mBAClF,KAAK,QAAQ,YACjB,GAAG,KAAK,QAAQ,UAAU,QAAQ,KAAK,GAAG,UAAU,KAAK,GAAG,MAAM,aAAa,MAAM,oBAAoB,MAAM,OAAO,WAAW,KAAK,CAAC;AACxI,UAAM,YAAY,aAAa,iBAAiB,aAAa,KAAK,yBAAyB;AAC3F,oBAAgB,SAAS,OAAO,OAAO,UAAU,MAAM;AACvD,oBAAgB,OAAO,OAAO,OAAO,UAAU,IAAI;AACnD,UAAM,UAAU,KAAK,eAAe,sBAAsB,kBAAkB,MAAM,YAAY,yBAAyB,iBAAiB,QAAQ;AAChJ,WAAO,KAAK,eAAe,mBAAmB,OAAO,OAAO,EAAE,SAAK,2BAAS,iBAAe;AACzF,aAAO,KAAK,eAAe,UAAU,QAAQ,cAAc,YAAY,OAAO,iBAAiB,GAAG,QAAQ,OAAO,WAAW;AAAA,IAC9H,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,yBAAyB,UAAU,YAAY,OAAO,UAAU,QAAQ,aAAa;AACnF,UAAM,cAAc,gBAAgB,YAAY,OAAO,UAAU,UAAU,KAAK,aAAa;AAC7F,QAAI,MAAM,SAAS,MAAM;AAKvB,iBAAW,WAAW,CAAC;AAAA,IACzB;AACA,WAAO,YAAY,SAAK,4BAAU,YAAU;AAC1C,UAAI,CAAC,OAAO,SAAS;AACnB,eAAO,UAAU,UAAU;AAAA,MAC7B;AAEA,iBAAW,MAAM,aAAa;AAC9B,aAAO,KAAK,eAAe,UAAU,OAAO,QAAQ,EAAE,SAAK,4BAAU,CAAC;AAAA,QACpE,QAAQ;AAAA,MACV,MAAM;AACJ,cAAM,gBAAgB,MAAM,mBAAmB;AAC/C,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,WAAW,IAAI,uBAAuB,kBAAkB,YAAY,OAAO,OAAO,mBACnF,KAAK,QAAQ,YACjB,GAAG,KAAK,QAAQ,UAAU,QAAQ,KAAK,GAAG,UAAU,KAAK,GAAG,MAAM,aAAa,MAAM,oBAAoB,MAAM,OAAO,WAAW,KAAK,CAAC;AACxI,cAAM,YAAY,aAAa,UAAU,aAAa,KAAK,yBAAyB;AACpF,iBAAS,SAAS,OAAO,OAAO,UAAU,MAAM;AAChD,iBAAS,OAAO,OAAO,OAAO,UAAU,IAAI;AAC5C,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,MAAM,YAAY,kBAAkB,mBAAmB,WAAW;AACtE,YAAI,eAAe,WAAW,KAAK,aAAa,YAAY,GAAG;AAC7D,iBAAO,KAAK,gBAAgB,eAAe,aAAa,cAAc,QAAQ,EAAE,SAAK,sBAAI,cAAY;AACnG,mBAAO,IAAI,SAAS,UAAU,QAAQ;AAAA,UACxC,CAAC,CAAC;AAAA,QACJ;AACA,YAAI,YAAY,WAAW,KAAK,eAAe,WAAW,GAAG;AAC3D,qBAAO,gBAAG,IAAI,SAAS,UAAU,CAAC,CAAC,CAAC;AAAA,QACtC;AACA,cAAM,kBAAkB,UAAU,KAAK,MAAM;AAS7C,eAAO,KAAK,eAAe,eAAe,aAAa,cAAc,gBAAgB,kBAAkB,iBAAiB,QAAQ,MAAM,QAAQ,EAAE,SAAK,sBAAI,WAAS;AAChK,iBAAO,IAAI,SAAS,UAAU,iBAAiB,WAAW,CAAC,KAAK,IAAI,CAAC,CAAC;AAAA,QACxE,CAAC,CAAC;AAAA,MACJ,CAAC,CAAC;AAAA,IACJ,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,eAAe,UAAU,OAAO,UAAU;AACxC,QAAI,MAAM,UAAU;AAElB,iBAAO,gBAAG;AAAA,QACR,QAAQ,MAAM;AAAA,QACd;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,MAAM,cAAc;AAEtB,UAAI,MAAM,kBAAkB,QAAW;AACrC,mBAAO,gBAAG;AAAA,UACR,QAAQ,MAAM;AAAA,UACd,UAAU,MAAM;AAAA,QAClB,CAAC;AAAA,MACH;AACA,aAAO,iBAAiB,UAAU,OAAO,UAAU,KAAK,aAAa,EAAE,SAAK,2BAAS,sBAAoB;AACvG,YAAI,kBAAkB;AACpB,iBAAO,KAAK,aAAa,aAAa,UAAU,KAAK,EAAE,SAAK,sBAAI,SAAO;AACrE,kBAAM,gBAAgB,IAAI;AAC1B,kBAAM,kBAAkB,IAAI;AAAA,UAC9B,CAAC,CAAC;AAAA,QACJ;AACA,eAAO,aAAa,KAAK;AAAA,MAC3B,CAAC,CAAC;AAAA,IACJ;AACA,eAAO,gBAAG;AAAA,MACR,QAAQ,CAAC;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,SAAS,4BAA4B,OAAO;AAC1C,QAAM,KAAK,CAAC,GAAG,MAAM;AACnB,QAAI,EAAE,MAAM,WAAW,eAAgB,QAAO;AAC9C,QAAI,EAAE,MAAM,WAAW,eAAgB,QAAO;AAC9C,WAAO,EAAE,MAAM,OAAO,cAAc,EAAE,MAAM,MAAM;AAAA,EACpD,CAAC;AACH;AACA,SAAS,mBAAmB,MAAM;AAChC,QAAM,SAAS,KAAK,MAAM;AAC1B,SAAO,UAAU,OAAO,SAAS;AACnC;AAMA,SAAS,sBAAsB,OAAO;AACpC,QAAM,SAAS,CAAC;AAEhB,QAAM,cAAc,oBAAI,IAAI;AAC5B,aAAW,QAAQ,OAAO;AACxB,QAAI,CAAC,mBAAmB,IAAI,GAAG;AAC7B,aAAO,KAAK,IAAI;AAChB;AAAA,IACF;AACA,UAAM,yBAAyB,OAAO,KAAK,gBAAc,KAAK,MAAM,gBAAgB,WAAW,MAAM,WAAW;AAChH,QAAI,2BAA2B,QAAW;AACxC,6BAAuB,SAAS,KAAK,GAAG,KAAK,QAAQ;AACrD,kBAAY,IAAI,sBAAsB;AAAA,IACxC,OAAO;AACL,aAAO,KAAK,IAAI;AAAA,IAClB;AAAA,EACF;AAKA,aAAW,cAAc,aAAa;AACpC,UAAM,iBAAiB,sBAAsB,WAAW,QAAQ;AAChE,WAAO,KAAK,IAAI,SAAS,WAAW,OAAO,cAAc,CAAC;AAAA,EAC5D;AACA,SAAO,OAAO,OAAO,OAAK,CAAC,YAAY,IAAI,CAAC,CAAC;AAC/C;AACA,SAAS,0BAA0B,OAAO;AACxC,QAAM,QAAQ,CAAC;AACf,QAAM,QAAQ,OAAK;AACjB,UAAM,0BAA0B,MAAM,EAAE,MAAM,MAAM;AACpD,QAAI,yBAAyB;AAC3B,YAAM,IAAI,wBAAwB,IAAI,IAAI,OAAK,EAAE,SAAS,CAAC,EAAE,KAAK,GAAG;AACrE,YAAM,IAAI,EAAE,MAAM,IAAI,IAAI,OAAK,EAAE,SAAS,CAAC,EAAE,KAAK,GAAG;AACrD,YAAM,IAAI,aAAc,OAA4D,OAAO,cAAc,eAAe,cAAc,mDAAmD,CAAC,UAAU,CAAC,IAAI;AAAA,IAC3M;AACA,UAAM,EAAE,MAAM,MAAM,IAAI,EAAE;AAAA,EAC5B,CAAC;AACH;AACA,SAAS,QAAQ,OAAO;AACtB,SAAO,MAAM,QAAQ,CAAC;AACxB;AACA,SAAS,WAAW,OAAO;AACzB,SAAO,MAAM,WAAW,CAAC;AAC3B;AACA,SAAS,UAAU,UAAU,cAAc,mBAAmB,QAAQ,YAAY,2BAA2B;AAC3G,aAAO,2BAAS,OAAK,YAAY,UAAU,cAAc,mBAAmB,QAAQ,EAAE,cAAc,YAAY,yBAAyB,EAAE,SAAK,sBAAI,CAAC;AAAA,IACnJ,OAAO;AAAA,IACP,MAAM;AAAA,EACR,MAAM;AACJ,WAAO,iCACF,IADE;AAAA,MAEL;AAAA,MACA;AAAA,IACF;AAAA,EACF,CAAC,CAAC,CAAC;AACL;AACA,SAAS,YAAY,2BAA2B,UAAU;AACxD,aAAO,2BAAS,OAAK;AACnB,UAAM;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACN;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,kBAAkB,QAAQ;AAC7B,iBAAO,gBAAG,CAAC;AAAA,IACb;AAIA,UAAM,2BAA2B,IAAI,IAAI,kBAAkB,IAAI,WAAS,MAAM,KAAK,CAAC;AACpF,UAAM,2BAA2B,oBAAI,IAAI;AACzC,eAAW,SAAS,0BAA0B;AAC5C,UAAI,yBAAyB,IAAI,KAAK,GAAG;AACvC;AAAA,MACF;AAEA,iBAAW,YAAY,iBAAiB,KAAK,GAAG;AAC9C,iCAAyB,IAAI,QAAQ;AAAA,MACvC;AAAA,IACF;AACA,QAAI,kBAAkB;AACtB,eAAO,kBAAK,wBAAwB,EAAE,SAAK,4BAAU,WAAS;AAC5D,UAAI,yBAAyB,IAAI,KAAK,GAAG;AACvC,eAAO,WAAW,OAAO,gBAAgB,2BAA2B,QAAQ;AAAA,MAC9E,OAAO;AACL,cAAM,OAAO,aAAa,OAAO,MAAM,QAAQ,yBAAyB,EAAE;AAC1E,mBAAO,gBAAG,MAAM;AAAA,MAClB;AAAA,IACF,CAAC,OAAG,sBAAI,MAAM,iBAAiB,OAAG,2BAAS,CAAC,OAAG,2BAAS,OAAK,oBAAoB,yBAAyB,WAAO,gBAAG,CAAC,IAAI,iBAAK,CAAC;AAAA,EACjI,CAAC;AACH;AAIA,SAAS,iBAAiB,OAAO;AAC/B,QAAM,cAAc,MAAM,SAAS,IAAI,WAAS,iBAAiB,KAAK,CAAC,EAAE,KAAK;AAC9E,SAAO,CAAC,OAAO,GAAG,WAAW;AAC/B;AACA,SAAS,WAAW,WAAW,WAAW,2BAA2B,UAAU;AAC7E,QAAM,SAAS,UAAU;AACzB,QAAM,UAAU,UAAU;AAC1B,MAAI,QAAQ,UAAU,UAAa,CAAC,eAAe,MAAM,GAAG;AAC1D,YAAQ,aAAa,IAAI,OAAO;AAAA,EAClC;AACA,SAAO,YAAY,SAAS,WAAW,WAAW,QAAQ,EAAE,SAAK,sBAAI,kBAAgB;AACnF,cAAU,gBAAgB;AAC1B,cAAU,OAAO,aAAa,WAAW,UAAU,QAAQ,yBAAyB,EAAE;AACtF,WAAO;AAAA,EACT,CAAC,CAAC;AACJ;AACA,SAAS,YAAY,SAAS,WAAW,WAAW,UAAU;AAC5D,QAAM,OAAO,YAAY,OAAO;AAChC,MAAI,KAAK,WAAW,GAAG;AACrB,eAAO,gBAAG,CAAC,CAAC;AAAA,EACd;AACA,QAAM,OAAO,CAAC;AACd,aAAO,kBAAK,IAAI,EAAE,SAAK,2BAAS,SAAO,YAAY,QAAQ,GAAG,GAAG,WAAW,WAAW,QAAQ,EAAE,SAAK,wBAAM,OAAG,sBAAI,WAAS;AAC1H,QAAI,iBAAiB,iBAAiB;AACpC,YAAM,2BAA2B,IAAI,qBAAqB,GAAG,KAAK;AAAA,IACpE;AACA,SAAK,GAAG,IAAI;AAAA,EACd,CAAC,CAAC,CAAC,OAAG,2BAAS,CAAC,OAAG,sBAAI,MAAM,IAAI,OAAG,6BAAW,OAAK,aAAa,CAAC,IAAI,wBAAQ,wBAAW,CAAC,CAAC,CAAC;AAC9F;AACA,SAAS,YAAY,gBAAgB,WAAW,WAAW,UAAU;AACnE,QAAM,kBAAkB,wBAAwB,SAAS,KAAK;AAC9D,QAAM,WAAW,2BAA2B,gBAAgB,eAAe;AAC3E,QAAM,gBAAgB,SAAS,UAAU,SAAS,QAAQ,WAAW,SAAS,IAAI,sBAAsB,iBAAiB,MAAM,SAAS,WAAW,SAAS,CAAC;AAC7J,SAAO,mBAAmB,aAAa;AACzC;AAQA,SAAS,UAAU,MAAM;AACvB,aAAO,4BAAU,OAAK;AACpB,UAAM,aAAa,KAAK,CAAC;AACzB,QAAI,YAAY;AACd,iBAAO,kBAAK,UAAU,EAAE,SAAK,sBAAI,MAAM,CAAC,CAAC;AAAA,IAC3C;AACA,eAAO,gBAAG,CAAC;AAAA,EACb,CAAC;AACH;AAyBA,IAAM,gBAAN,MAAM,eAAc;AAAA;AAAA;AAAA;AAAA,EAIlB,WAAW,UAAU;AACnB,QAAI;AACJ,QAAI,QAAQ,SAAS;AACrB,WAAO,UAAU,QAAW;AAC1B,kBAAY,KAAK,yBAAyB,KAAK,KAAK;AACpD,cAAQ,MAAM,SAAS,KAAK,WAAS,MAAM,WAAW,cAAc;AAAA,IACtE;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB,UAAU;AACjC,WAAO,SAAS,KAAK,aAAa;AAAA,EACpC;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,OAAO,MAAM,OAAO,oBAAoB,GAAG;AAAA,IACpD,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,YAAY,MAAM,OAAO,oBAAoB;AAAA,IAC/C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAIH,IAAM,uBAAN,MAAM,8BAA6B,cAAc;AAAA,EAC/C;AAAA,EACA,YAAY,OAAO;AACjB,UAAM;AACN,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,UAAU;AACpB,UAAM,QAAQ,KAAK,WAAW,QAAQ;AACtC,QAAI,UAAU,QAAW;AACvB,WAAK,MAAM,SAAS,KAAK;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAyB,SAAY,KAAK,CAAC;AAAA,EAC9E;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,sBAAqB;AAAA,IAC9B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAOH,IAAM,uBAAuB,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,kBAAkB,IAAI;AAAA,EACpH,YAAY;AAAA,EACZ,SAAS,OAAO,CAAC;AACnB,CAAC;AAYD,IAAM,SAAS,IAAI,eAAe,YAAY,WAAW,EAAE;AAC3D,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,mBAAmB,oBAAI,QAAQ;AAAA,EAC/B,kBAAkB,oBAAI,QAAQ;AAAA,EAC9B;AAAA,EACA;AAAA,EACA,WAAW,OAAO,QAAQ;AAAA,EAC1B,cAAc,OAAO;AACnB,QAAI,KAAK,iBAAiB,IAAI,KAAK,GAAG;AACpC,aAAO,KAAK,iBAAiB,IAAI,KAAK;AAAA,IACxC,WAAW,MAAM,kBAAkB;AACjC,iBAAO,gBAAG,MAAM,gBAAgB;AAAA,IAClC;AACA,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,KAAK;AAAA,IAChC;AACA,UAAM,aAAa,mBAAmB,MAAM,cAAc,CAAC,EAAE,SAAK,sBAAI,wBAAwB,OAAG,sBAAI,eAAa;AAChH,UAAI,KAAK,mBAAmB;AAC1B,aAAK,kBAAkB,KAAK;AAAA,MAC9B;AACA,OAAC,OAAO,cAAc,eAAe,cAAc,iBAAiB,MAAM,QAAQ,IAAI,SAAS;AAC/F,YAAM,mBAAmB;AAAA,IAC3B,CAAC,OAAG,2BAAS,MAAM;AACjB,WAAK,iBAAiB,OAAO,KAAK;AAAA,IACpC,CAAC,CAAC;AAEF,UAAM,SAAS,IAAI,kCAAsB,YAAY,MAAM,IAAI,oBAAQ,CAAC,EAAE,SAAK,2BAAS,CAAC;AACzF,SAAK,iBAAiB,IAAI,OAAO,MAAM;AACvC,WAAO;AAAA,EACT;AAAA,EACA,aAAa,gBAAgB,OAAO;AAClC,QAAI,KAAK,gBAAgB,IAAI,KAAK,GAAG;AACnC,aAAO,KAAK,gBAAgB,IAAI,KAAK;AAAA,IACvC,WAAW,MAAM,eAAe;AAC9B,iBAAO,gBAAG;AAAA,QACR,QAAQ,MAAM;AAAA,QACd,UAAU,MAAM;AAAA,MAClB,CAAC;AAAA,IACH;AACA,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,KAAK;AAAA,IAChC;AACA,UAAM,yBAAyB,aAAa,OAAO,KAAK,UAAU,gBAAgB,KAAK,iBAAiB;AACxG,UAAM,aAAa,uBAAuB,SAAK,2BAAS,MAAM;AAC5D,WAAK,gBAAgB,OAAO,KAAK;AAAA,IACnC,CAAC,CAAC;AAEF,UAAM,SAAS,IAAI,kCAAsB,YAAY,MAAM,IAAI,oBAAQ,CAAC,EAAE,SAAK,2BAAS,CAAC;AACzF,SAAK,gBAAgB,IAAI,OAAO,MAAM;AACtC,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,oBAAmB;AAAA,IAC5B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AASH,SAAS,aAAa,OAAO,UAAU,gBAAgB,mBAAmB;AACxE,SAAO,mBAAmB,MAAM,aAAa,CAAC,EAAE,SAAK,sBAAI,wBAAwB,OAAG,2BAAS,OAAK;AAChG,QAAI,aAAa,qBAAmB,MAAM,QAAQ,CAAC,GAAG;AACpD,iBAAO,gBAAG,CAAC;AAAA,IACb,OAAO;AACL,iBAAO,kBAAK,SAAS,mBAAmB,CAAC,CAAC;AAAA,IAC5C;AAAA,EACF,CAAC,OAAG,sBAAI,qBAAmB;AACzB,QAAI,mBAAmB;AACrB,wBAAkB,KAAK;AAAA,IACzB;AAGA,QAAI;AACJ,QAAI;AACJ,QAAI,8BAA8B;AAClC,QAAI,MAAM,QAAQ,eAAe,GAAG;AAClC,kBAAY;AACZ,oCAA8B;AAAA,IAChC,OAAO;AACL,iBAAW,gBAAgB,OAAO,cAAc,EAAE;AAKlD,kBAAY,SAAS,IAAI,QAAQ,CAAC,GAAG;AAAA,QACnC,UAAU;AAAA,QACV,MAAM;AAAA,MACR,CAAC,EAAE,KAAK;AAAA,IACV;AACA,UAAM,SAAS,UAAU,IAAI,iBAAiB;AAC9C,KAAC,OAAO,cAAc,eAAe,cAAc,eAAe,QAAQ,MAAM,MAAM,2BAA2B;AACjH,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF,CAAC,CAAC;AACJ;AACA,SAAS,uBAAuB,OAAO;AAIrC,SAAO,SAAS,OAAO,UAAU,YAAY,aAAa;AAC5D;AACA,SAAS,yBAAyBC,QAAO;AAGvC,SAAO,uBAAuBA,MAAK,IAAIA,OAAM,SAAS,IAAIA;AAC5D;AASA,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,OAAO,MAAM,OAAO,0BAA0B,GAAG;AAAA,IAC1D,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,YAAY,MAAM,OAAO,0BAA0B;AAAA,IACrD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAIH,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,iBAAiB,KAAK;AACpB,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,KAAK;AACX,WAAO;AAAA,EACT;AAAA,EACA,MAAM,YAAY,UAAU;AAC1B,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,mCAAmC,mBAAmB;AAC3E,WAAO,KAAK,qBAAqB,6BAA4B;AAAA,EAC/D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,4BAA2B;AAAA,IACpC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,yBAAyB,IAAI,eAAe,YAAY,2BAA2B,EAAE;AAC3F,IAAM,0BAA0B,IAAI,eAAe,YAAY,4BAA4B,EAAE;AAO7F,SAAS,qBAAqB,UAAUC,OAAM,IAAI;AAChD,QAAM,oBAAoB,SAAS,IAAI,uBAAuB;AAC9D,QAAM,WAAW,SAAS,IAAI,QAAQ;AAEtC,SAAO,SAAS,IAAI,MAAM,EAAE,kBAAkB,MAAM;AAClD,QAAI,CAAC,SAAS,uBAAuB,kBAAkB,oBAAoB;AACzE,wBAAkB,qBAAqB;AAIvC,aAAO,IAAI,QAAQ,aAAW,WAAW,OAAO,CAAC;AAAA,IACnD;AACA,QAAI;AACJ,UAAM,wBAAwB,IAAI,QAAQ,aAAW;AACnD,qCAA+B;AAAA,IACjC,CAAC;AACD,UAAM,aAAa,SAAS,oBAAoB,MAAM;AACpD,mCAA6B;AAK7B,aAAO,oBAAoB,QAAQ;AAAA,IAErC,CAAC;AACD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,yBAAyB;AAC3B,4BAAsB,UAAU,MAAM,wBAAwB;AAAA,QAC5D;AAAA,QACA,MAAAA;AAAA,QACA;AAAA,MACF,CAAC,CAAC;AAAA,IACJ;AACA,WAAO;AAAA,EACT,CAAC;AACH;AAIA,SAAS,oBAAoB,UAAU;AACrC,SAAO,IAAI,QAAQ,aAAW;AAI5B,oBAAgB;AAAA,MACd,MAAM,MAAM,WAAW,OAAO;AAAA,IAChC,GAAG;AAAA,MACD;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AACA,IAAM,2BAA2B,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,6BAA6B,EAAE;AACnI,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,2BAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3B,SAAS,IAAI,oBAAQ;AAAA;AAAA;AAAA;AAAA,EAIrB,yBAAyB,IAAI,oBAAQ;AAAA,EACrC,eAAe,OAAO,kBAAkB;AAAA,EACxC,sBAAsB,OAAO,mBAAmB;AAAA,EAChD,aAAa,OAAO,UAAU;AAAA,EAC9B,gBAAgB,OAAO,aAAa;AAAA,EACpC,eAAe,OAAO,sBAAsB;AAAA,EAC5C,WAAW,OAAO,QAAQ;AAAA,EAC1B,sBAAsB,OAAO,cAAc;AAAA,IACzC,UAAU;AAAA,EACZ,CAAC,MAAM;AAAA,EACP,gBAAgB,OAAO,aAAa;AAAA,EACpC,UAAU,OAAO,sBAAsB;AAAA,IACrC,UAAU;AAAA,EACZ,CAAC,KAAK,CAAC;AAAA,EACP,4BAA4B,KAAK,QAAQ,6BAA6B;AAAA,EACtE,sBAAsB,OAAO,mBAAmB;AAAA,EAChD,uBAAuB,OAAO,wBAAwB;AAAA,IACpD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,yBAAyB,OAAO,0BAA0B;AAAA,IACxD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,eAAe;AAAA,EACf,IAAI,yBAAyB;AAC3B,WAAO,KAAK,iBAAiB;AAAA,EAC/B;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,qBAAqB,UAAM,gBAAG,MAAM;AAAA;AAAA,EAEpC,oBAAoB;AAAA,EACpB,YAAY;AAAA,EACZ,cAAc;AACZ,UAAM,cAAc,OAAK,KAAK,OAAO,KAAK,IAAI,qBAAqB,CAAC,CAAC;AACrE,UAAM,YAAY,OAAK,KAAK,OAAO,KAAK,IAAI,mBAAmB,CAAC,CAAC;AACjE,SAAK,aAAa,oBAAoB;AACtC,SAAK,aAAa,sBAAsB;AACxC,SAAK,WAAW,UAAU,MAAM;AAC9B,WAAK,YAAY;AAAA,IACnB,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,SAAK,aAAa,SAAS;AAAA,EAC7B;AAAA,EACA,wBAAwB,SAAS;AAC/B,UAAM,KAAK,EAAE,KAAK;AAClB,SAAK,aAAa,KAAK,iCAClB,UADkB;AAAA,MAErB,cAAc,KAAK,oBAAoB,QAAQ,QAAQ,MAAM;AAAA,MAC7D,gBAAgB;AAAA,MAChB,mBAAmB;AAAA,MACnB,QAAQ;AAAA,QACN,mBAAmB,CAAC;AAAA,QACpB,qBAAqB,CAAC;AAAA,MACxB;AAAA,MACA,cAAc;AAAA,MACd;AAAA,IACF,EAAC;AAAA,EACH;AAAA,EACA,iBAAiB,QAAQ;AACvB,SAAK,cAAc,IAAI,4BAAgB,IAAI;AAC3C,WAAO,KAAK,YAAY;AAAA,UAAK,yBAAO,OAAK,MAAM,IAAI;AAAA;AAAA,UAEnD,4BAAU,4BAA0B;AAClC,YAAI,YAAY;AAChB,YAAI,UAAU;AACd,mBAAO,gBAAG,sBAAsB,EAAE;AAAA,cAAK,4BAAU,OAAK;AAKpD,gBAAI,KAAK,eAAe,uBAAuB,IAAI;AACjD,oBAAM,qBAAqB,OAAO,cAAc,eAAe,YAAY,iBAAiB,uBAAuB,EAAE,8CAA8C,KAAK,YAAY,KAAK;AACzL,mBAAK,2BAA2B,wBAAwB,oBAAoB,2BAA2B,yBAAyB;AAChI,qBAAO;AAAA,YACT;AACA,iBAAK,oBAAoB;AAEzB,iBAAK,oBAAoB;AAAA,cACvB,IAAI,EAAE;AAAA,cACN,YAAY,EAAE;AAAA,cACd,cAAc,EAAE;AAAA,cAChB,kBAAkB,OAAO,EAAE,OAAO,eAAe,WAAW,KAAK,cAAc,MAAM,EAAE,OAAO,UAAU,IAAI,EAAE,OAAO;AAAA,cACrH,SAAS,EAAE;AAAA,cACX,QAAQ,EAAE;AAAA,cACV,oBAAoB,CAAC,KAAK,2BAA2B,OAAO,iCACvD,KAAK,2BADkD;AAAA,gBAE1D,oBAAoB;AAAA,cACtB;AAAA,YACF;AACA,kBAAM,gBAAgB,CAAC,OAAO,aAAa,KAAK,wBAAwB,KAAK,KAAK,oBAAoB;AACtG,kBAAM,sBAAsB,EAAE,OAAO,uBAAuB,OAAO;AACnE,gBAAI,CAAC,iBAAiB,wBAAwB,UAAU;AACtD,oBAAM,SAAS,OAAO,cAAc,eAAe,YAAY,iBAAiB,EAAE,MAAM,mEAAmE;AAC3J,mBAAK,OAAO,KAAK,IAAI,kBAAkB,EAAE,IAAI,KAAK,cAAc,UAAU,EAAE,MAAM,GAAG,QAAQ,sBAAsB,wBAAwB,CAAC;AAC5I,gBAAE,QAAQ,KAAK;AACf,qBAAO;AAAA,YACT;AACA,gBAAI,KAAK,oBAAoB,iBAAiB,EAAE,MAAM,GAAG;AACvD,yBAAO,gBAAG,CAAC,EAAE;AAAA;AAAA,oBAEb,4BAAU,CAAAT,OAAK;AACb,uBAAK,OAAO,KAAK,IAAI,gBAAgBA,GAAE,IAAI,KAAK,cAAc,UAAUA,GAAE,YAAY,GAAGA,GAAE,QAAQA,GAAE,aAAa,CAAC;AACnH,sBAAIA,GAAE,OAAO,KAAK,cAAc;AAC9B,2BAAO;AAAA,kBACT;AAGA,yBAAO,QAAQ,QAAQA,EAAC;AAAA,gBAC1B,CAAC;AAAA;AAAA,gBAED,UAAU,KAAK,qBAAqB,KAAK,cAAc,KAAK,mBAAmB,OAAO,QAAQ,KAAK,eAAe,KAAK,yBAAyB;AAAA;AAAA,oBAEhJ,sBAAI,CAAAA,OAAK;AACP,yCAAuB,iBAAiBA,GAAE;AAC1C,yCAAuB,oBAAoBA,GAAE;AAC7C,uBAAK,oBAAoB,iCACpB,KAAK,oBADe;AAAA,oBAEvB,UAAUA,GAAE;AAAA,kBACd;AAEA,wBAAM,mBAAmB,IAAI,iBAAiBA,GAAE,IAAI,KAAK,cAAc,UAAUA,GAAE,YAAY,GAAG,KAAK,cAAc,UAAUA,GAAE,iBAAiB,GAAGA,GAAE,cAAc;AACrK,uBAAK,OAAO,KAAK,gBAAgB;AAAA,gBACnC,CAAC;AAAA,cAAC;AAAA,YACJ,WAAW,iBAAiB,KAAK,oBAAoB,iBAAiB,EAAE,aAAa,GAAG;AAItF,oBAAM;AAAA,gBACJ;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACF,IAAI;AACJ,oBAAM,WAAW,IAAI,gBAAgB,IAAI,KAAK,cAAc,UAAU,YAAY,GAAG,QAAQ,aAAa;AAC1G,mBAAK,OAAO,KAAK,QAAQ;AACzB,oBAAM,iBAAiB,iBAAiB,KAAK,iBAAiB,EAAE;AAChE,mBAAK,oBAAoB,yBAAyB,iCAC7C,IAD6C;AAAA,gBAEhD;AAAA,gBACA,mBAAmB;AAAA,gBACnB,QAAQ,iCACH,SADG;AAAA,kBAEN,oBAAoB;AAAA,kBACpB,YAAY;AAAA,gBACd;AAAA,cACF;AACA,mBAAK,kBAAkB,WAAW;AAClC,yBAAO,gBAAG,sBAAsB;AAAA,YAClC,OAAO;AAML,oBAAM,SAAS,OAAO,cAAc,eAAe,YAAY,4FAAiG,EAAE,aAAa,mBAAmB,EAAE,MAAM,0BAA0B;AACpO,mBAAK,OAAO,KAAK,IAAI,kBAAkB,EAAE,IAAI,KAAK,cAAc,UAAU,EAAE,YAAY,GAAG,QAAQ,sBAAsB,4BAA4B,CAAC;AACtJ,gBAAE,QAAQ,KAAK;AACf,qBAAO;AAAA,YACT;AAAA,UACF,CAAC;AAAA;AAAA,cAED,sBAAI,OAAK;AACP,kBAAM,cAAc,IAAI,iBAAiB,EAAE,IAAI,KAAK,cAAc,UAAU,EAAE,YAAY,GAAG,KAAK,cAAc,UAAU,EAAE,iBAAiB,GAAG,EAAE,cAAc;AAChK,iBAAK,OAAO,KAAK,WAAW;AAAA,UAC9B,CAAC;AAAA,cAAG,sBAAI,OAAK;AACX,iBAAK,oBAAoB,yBAAyB,iCAC7C,IAD6C;AAAA,cAEhD,QAAQ,kBAAkB,EAAE,gBAAgB,EAAE,iBAAiB,KAAK,YAAY;AAAA,YAClF;AACA,mBAAO;AAAA,UACT,CAAC;AAAA,UAAG,YAAY,KAAK,qBAAqB,SAAO,KAAK,OAAO,KAAK,GAAG,CAAC;AAAA,cAAG,sBAAI,OAAK;AAChF,mCAAuB,eAAe,EAAE;AACxC,gBAAI,EAAE,gBAAgB,OAAO,EAAE,iBAAiB,WAAW;AACzD,oBAAM,2BAA2B,KAAK,eAAe,EAAE,YAAY;AAAA,YACrE;AACA,kBAAM,YAAY,IAAI,eAAe,EAAE,IAAI,KAAK,cAAc,UAAU,EAAE,YAAY,GAAG,KAAK,cAAc,UAAU,EAAE,iBAAiB,GAAG,EAAE,gBAAgB,CAAC,CAAC,EAAE,YAAY;AAC9K,iBAAK,OAAO,KAAK,SAAS;AAAA,UAC5B,CAAC;AAAA,cAAG,yBAAO,OAAK;AACd,gBAAI,CAAC,EAAE,cAAc;AACnB,mBAAK,2BAA2B,GAAG,IAAI,2BAA2B,aAAa;AAC/E,qBAAO;AAAA,YACT;AACA,mBAAO;AAAA,UACT,CAAC;AAAA;AAAA,UAED,UAAU,OAAK;AACb,gBAAI,EAAE,OAAO,kBAAkB,WAAW,GAAG;AAC3C,qBAAO;AAAA,YACT;AACA,uBAAO,gBAAG,CAAC,EAAE,SAAK,sBAAI,CAAAA,OAAK;AACzB,oBAAM,eAAe,IAAI,aAAaA,GAAE,IAAI,KAAK,cAAc,UAAUA,GAAE,YAAY,GAAG,KAAK,cAAc,UAAUA,GAAE,iBAAiB,GAAGA,GAAE,cAAc;AAC7J,mBAAK,OAAO,KAAK,YAAY;AAAA,YAC/B,CAAC,OAAG,4BAAU,CAAAA,OAAK;AACjB,kBAAI,eAAe;AACnB,yBAAO,gBAAGA,EAAC,EAAE,KAAK,YAAY,KAAK,2BAA2B,KAAK,mBAAmB,OAAG,sBAAI;AAAA,gBAC3F,MAAM,MAAM,eAAe;AAAA,gBAC3B,UAAU,MAAM;AACd,sBAAI,CAAC,cAAc;AACjB,yBAAK,2BAA2BA,IAAG,OAAO,cAAc,eAAe,YAAY,uDAAuD,IAAI,2BAA2B,kBAAkB;AAAA,kBAC7L;AAAA,gBACF;AAAA,cACF,CAAC,CAAC;AAAA,YACJ,CAAC,OAAG,sBAAI,CAAAA,OAAK;AACX,oBAAM,aAAa,IAAI,WAAWA,GAAE,IAAI,KAAK,cAAc,UAAUA,GAAE,YAAY,GAAG,KAAK,cAAc,UAAUA,GAAE,iBAAiB,GAAGA,GAAE,cAAc;AACzJ,mBAAK,OAAO,KAAK,UAAU;AAAA,YAC7B,CAAC,CAAC;AAAA,UACJ,CAAC;AAAA;AAAA,UAED,UAAU,OAAK;AACb,kBAAM,iBAAiB,WAAS;AAC9B,oBAAM,UAAU,CAAC;AACjB,kBAAI,MAAM,aAAa,iBAAiB,CAAC,MAAM,YAAY,kBAAkB;AAC3E,wBAAQ,KAAK,KAAK,aAAa,cAAc,MAAM,WAAW,EAAE,SAAK,sBAAI,qBAAmB;AAC1F,wBAAM,YAAY;AAAA,gBACpB,CAAC,OAAG,sBAAI,MAAM,MAAM,CAAC,CAAC;AAAA,cACxB;AACA,yBAAW,SAAS,MAAM,UAAU;AAClC,wBAAQ,KAAK,GAAG,eAAe,KAAK,CAAC;AAAA,cACvC;AACA,qBAAO;AAAA,YACT;AACA,uBAAO,2BAAc,eAAe,EAAE,eAAe,IAAI,CAAC,EAAE,SAAK,iCAAe,IAAI,OAAG,uBAAK,CAAC,CAAC;AAAA,UAChG,CAAC;AAAA,UAAG,UAAU,MAAM,KAAK,mBAAmB,CAAC;AAAA,cAAG,4BAAU,MAAM;AAC9D,kBAAM;AAAA,cACJ;AAAA,cACA;AAAA,YACF,IAAI;AACJ,kBAAM,wBAAwB,KAAK,uBAAuB,KAAK,qBAAqB,gBAAgB,MAAM,eAAe,IAAI;AAG7H,mBAAO,4BAAwB,kBAAK,qBAAqB,EAAE,SAAK,sBAAI,MAAM,sBAAsB,CAAC,QAAI,gBAAG,sBAAsB;AAAA,UAChI,CAAC;AAAA,cAAG,sBAAI,OAAK;AACX,kBAAM,oBAAoB,kBAAkB,OAAO,oBAAoB,EAAE,gBAAgB,EAAE,kBAAkB;AAC7G,iBAAK,oBAAoB,yBAAyB,iCAC7C,IAD6C;AAAA,cAEhD;AAAA,YACF;AACA,iBAAK,kBAAkB,oBAAoB;AAC3C,mBAAO;AAAA,UACT,CAAC;AAAA,cAAG,sBAAI,MAAM;AACZ,iBAAK,OAAO,KAAK,IAAI,qBAAqB,CAAC;AAAA,UAC7C,CAAC;AAAA,UAAG,eAAe,KAAK,cAAc,OAAO,oBAAoB,SAAO,KAAK,OAAO,KAAK,GAAG,GAAG,KAAK,mBAAmB;AAAA;AAAA;AAAA;AAAA,cAIvH,uBAAK,CAAC;AAAA,cAAG,sBAAI;AAAA,YACX,MAAM,OAAK;AACT,0BAAY;AACZ,mBAAK,2BAA2B,KAAK;AACrC,mBAAK,OAAO,KAAK,IAAI,cAAc,EAAE,IAAI,KAAK,cAAc,UAAU,EAAE,YAAY,GAAG,KAAK,cAAc,UAAU,EAAE,iBAAiB,CAAC,CAAC;AACzI,mBAAK,eAAe,YAAY,EAAE,kBAAkB,QAAQ;AAC5D,gBAAE,QAAQ,IAAI;AAAA,YAChB;AAAA,YACA,UAAU,MAAM;AACd,0BAAY;AAAA,YACd;AAAA,UACF,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAQD,4BAAU,KAAK,uBAAuB,SAAK,sBAAI,SAAO;AACpD,kBAAM;AAAA,UACR,CAAC,CAAC,CAAC;AAAA,cAAG,2BAAS,MAAM;AAOnB,gBAAI,CAAC,aAAa,CAAC,SAAS;AAC1B,oBAAM,oBAAoB,OAAO,cAAc,eAAe,YAAY,iBAAiB,uBAAuB,EAAE,8CAA8C,KAAK,YAAY,KAAK;AACxL,mBAAK,2BAA2B,wBAAwB,mBAAmB,2BAA2B,yBAAyB;AAAA,YACjI;AAGA,gBAAI,KAAK,mBAAmB,OAAO,uBAAuB,IAAI;AAC5D,mBAAK,oBAAoB;AACzB,mBAAK,oBAAoB;AAAA,YAC3B;AAAA,UACF,CAAC;AAAA,cAAG,6BAAW,OAAK;AAIlB,gBAAI,KAAK,WAAW;AAClB,qCAAuB,QAAQ,KAAK;AACpC,qBAAO;AAAA,YACT;AACA,sBAAU;AAGV,gBAAI,2BAA2B,CAAC,GAAG;AACjC,mBAAK,OAAO,KAAK,IAAI,iBAAiB,uBAAuB,IAAI,KAAK,cAAc,UAAU,uBAAuB,YAAY,GAAG,EAAE,SAAS,EAAE,gBAAgB,CAAC;AAGlK,kBAAI,CAAC,sCAAsC,CAAC,GAAG;AAC7C,uCAAuB,QAAQ,KAAK;AAAA,cACtC,OAAO;AACL,qBAAK,OAAO,KAAK,IAAI,gBAAgB,EAAE,KAAK,EAAE,yBAAyB,CAAC;AAAA,cAC1E;AAAA,YAGF,OAAO;AACL,oBAAM,kBAAkB,IAAI,gBAAgB,uBAAuB,IAAI,KAAK,cAAc,UAAU,uBAAuB,YAAY,GAAG,GAAG,uBAAuB,kBAAkB,MAAS;AAC/L,kBAAI;AACF,sBAAM,+BAA+B,sBAAsB,KAAK,qBAAqB,MAAM,KAAK,yBAAyB,eAAe,CAAC;AACzI,oBAAI,wCAAwC,iBAAiB;AAC3D,wBAAM;AAAA,oBACJ;AAAA,oBACA;AAAA,kBACF,IAAI,2BAA2B,KAAK,eAAe,4BAA4B;AAC/E,uBAAK,OAAO,KAAK,IAAI,iBAAiB,uBAAuB,IAAI,KAAK,cAAc,UAAU,uBAAuB,YAAY,GAAG,SAAS,gBAAgB,CAAC;AAC9J,uBAAK,OAAO,KAAK,IAAI,gBAAgB,6BAA6B,YAAY,6BAA6B,yBAAyB,CAAC;AAAA,gBACvI,OAAO;AACL,uBAAK,OAAO,KAAK,eAAe;AAChC,wBAAM;AAAA,gBACR;AAAA,cACF,SAAS,IAAI;AAUX,oBAAI,KAAK,QAAQ,iCAAiC;AAChD,yCAAuB,QAAQ,KAAK;AAAA,gBACtC,OAAO;AACL,yCAAuB,OAAO,EAAE;AAAA,gBAClC;AAAA,cACF;AAAA,YACF;AACA,mBAAO;AAAA,UACT,CAAC;AAAA,QAAC;AAAA,MAEJ,CAAC;AAAA,IAAC;AAAA,EACJ;AAAA,EACA,2BAA2B,GAAG,QAAQ,MAAM;AAC1C,UAAM,YAAY,IAAI,iBAAiB,EAAE,IAAI,KAAK,cAAc,UAAU,EAAE,YAAY,GAAG,QAAQ,IAAI;AACvG,SAAK,OAAO,KAAK,SAAS;AAC1B,MAAE,QAAQ,KAAK;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,0BAA0B;AAOxB,WAAO,KAAK,mBAAmB,aAAa,SAAS,MAAM,KAAK,mBAAmB,eAAe,SAAS;AAAA,EAC7G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB;AAIpB,UAAM,oBAAoB,KAAK,oBAAoB,QAAQ,KAAK,cAAc,MAAM,KAAK,SAAS,KAAK,IAAI,CAAC,CAAC;AAC7G,UAAM,mBAAmB,KAAK,mBAAmB,oBAAoB,KAAK,mBAAmB;AAC7F,WAAO,kBAAkB,SAAS,MAAM,kBAAkB,SAAS,KAAK,CAAC,KAAK,mBAAmB,OAAO;AAAA,EAC1G;AAAA,EACA,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,uBAAsB;AAAA,IAC/B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,SAAS,6BAA6B,QAAQ;AAC5C,SAAO,WAAW;AACpB;AASA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,OAAO,MAAM,OAAO,yBAAyB,GAAG;AAAA,IACzD,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,YAAY,MAAM,OAAO,yBAAyB;AAAA,IACpD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAkBH,IAAM,yBAAN,MAA6B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B,aAAa,OAAO;AAClB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,OAAO,cAAc;AAAA,EAAC;AAAA;AAAA,EAE5B,aAAa,OAAO;AAClB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,SAAS,OAAO;AACd,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,QAAQ,MAAM;AAC7B,WAAO,OAAO,gBAAgB,KAAK;AAAA,EACrC;AACF;AACA,IAAM,4BAAN,MAAM,mCAAkC,uBAAuB;AAAA,EAC7D,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,kCAAkC,mBAAmB;AACnE,cAAQ,2CAA2C,yCAA4C,sBAAsB,0BAAyB,IAAI,qBAAqB,0BAAyB;AAAA,IAClM;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,2BAA0B;AAAA,IACnC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,gBAAgB,OAAO,aAAa;AAAA,EACpC,UAAU,OAAO,sBAAsB;AAAA,IACrC,UAAU;AAAA,EACZ,CAAC,KAAK,CAAC;AAAA,EACP,+BAA+B,KAAK,QAAQ,gCAAgC;AAAA,EAC5E,WAAW,OAAO,QAAQ;AAAA,EAC1B,sBAAsB,OAAO,mBAAmB;AAAA,EAChD,oBAAoB,KAAK,QAAQ,qBAAqB;AAAA,EACtD,iBAAiB,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU7B,oBAAoB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0BlB,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM,SAAS,aAAa,SAAY,KAAK,oBAAoB,MAAM,UAAU,UAAU,IAAI;AAC/F,UAAM,MAAM,oBAAoB;AAChC,UAAM,OAAO,eAAe,UAAU,KAAK,cAAc,UAAU,GAAG,IAAI;AAC1E,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AAID,QAAI,YAAY,mBAAmB;AACjC,WAAK,iBAAiB;AACtB,WAAK,aAAa,KAAK,oBAAoB,MAAM,UAAU,UAAU;AACrE,WAAK,cAAc;AAAA,IACrB,OAAO;AACL,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,cAAc,iBAAiB,IAAI;AAAA;AAAA,EAEnC,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe,KAAK,mBAAmB;AAAA,EACvC,qBAAqB;AACnB,SAAK,eAAe,KAAK,mBAAmB;AAAA,EAC9C;AAAA,EACA,qBAAqB;AACnB,WAAO;AAAA,MACL,YAAY,KAAK;AAAA,MACjB,gBAAgB,KAAK;AAAA,MACrB,aAAa,KAAK;AAAA,IACpB;AAAA,EACF;AAAA,EACA,mBAAmB;AAAA,IACjB;AAAA,EACF,GAAG;AACD,SAAK,cAAc,KAAK,aAAa;AACrC,SAAK,iBAAiB,KAAK,aAAa;AAMxC,SAAK,aAAa,KAAK,oBAAoB,MAAM,KAAK,gBAAgB,YAAY,KAAK,UAAU;AAAA,EACnG;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,OAAO,MAAM,OAAO,mBAAmB,GAAG;AAAA,IACnD,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,YAAY,MAAM,OAAO,mBAAmB;AAAA,IAC9C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,sBAAN,MAAM,6BAA4B,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS7C,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,EACnB,gBAAgB;AACd,WAAO,KAAK,SAAS,SAAS;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,gBAAgB;AAClB,QAAI,KAAK,iCAAiC,YAAY;AACpD,aAAO,KAAK;AAAA,IACd;AACA,WAAO,KAAK,cAAc,GAAG,iBAAiB,KAAK;AAAA,EACrD;AAAA,EACA,4CAA4C,UAAU;AACpD,WAAO,KAAK,SAAS,UAAU,WAAS;AACtC,UAAI,MAAM,MAAM,MAAM,YAAY;AAGhC,mBAAW,MAAM;AACf,mBAAS,MAAM,KAAK,GAAG,MAAM,OAAO,UAAU;AAAA,QAChD,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB,GAAG,mBAAmB;AACtC,QAAI,aAAa,iBAAiB;AAChC,WAAK,mBAAmB;AAAA,IAC1B,WAAW,aAAa,mBAAmB;AACzC,WAAK,iBAAiB,iBAAiB;AAAA,IACzC,WAAW,aAAa,kBAAkB;AACxC,UAAI,KAAK,sBAAsB,SAAS;AACtC,YAAI,CAAC,kBAAkB,OAAO,oBAAoB;AAChD,eAAK,cAAc,KAAK,kBAAkB,iBAAiB,GAAG,iBAAiB;AAAA,QACjF;AAAA,MACF;AAAA,IACF,WAAW,aAAa,sBAAsB;AAC5C,WAAK,iBAAiB,iBAAiB;AACvC,UAAI,KAAK,sBAAsB,cAAc,CAAC,kBAAkB,OAAO,oBAAoB;AACzF,aAAK,cAAc,KAAK,kBAAkB,iBAAiB,GAAG,iBAAiB;AAAA,MACjF;AAAA,IACF,WAAW,aAAa,qBAAqB,EAAE,SAAS,2BAA2B,iBAAiB,EAAE,SAAS,2BAA2B,qBAAqB;AAC7J,WAAK,eAAe,iBAAiB;AAAA,IACvC,WAAW,aAAa,iBAAiB;AACvC,WAAK,eAAe,mBAAmB,IAAI;AAAA,IAC7C,WAAW,aAAa,eAAe;AACrC,WAAK,mBAAmB,EAAE;AAC1B,WAAK,gBAAgB,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,cAAc,MAAM;AAAA,IAClB;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,KAAK,SAAS,qBAAqB,IAAI,KAAK,CAAC,CAAC,YAAY;AAE5D,YAAM,uBAAuB,KAAK;AAClC,YAAM,WAAW,kCACZ,QACA,KAAK,sBAAsB,IAAI,oBAAoB;AAExD,WAAK,SAAS,aAAa,MAAM,IAAI,QAAQ;AAAA,IAC/C,OAAO;AACL,YAAM,WAAW,kCACZ,QACA,KAAK,sBAAsB,IAAI,KAAK,gBAAgB,CAAC;AAE1D,WAAK,SAAS,GAAG,MAAM,IAAI,QAAQ;AAAA,IACrC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,YAAY,2BAA2B,OAAO;AAC3D,QAAI,KAAK,iCAAiC,YAAY;AACpD,YAAM,uBAAuB,KAAK;AAClC,YAAM,qBAAqB,KAAK,gBAAgB;AAChD,UAAI,uBAAuB,GAAG;AAC5B,aAAK,SAAS,UAAU,kBAAkB;AAAA,MAC5C,WAAW,KAAK,kBAAkB,MAAM,WAAW,YAAY,uBAAuB,GAAG;AAIvF,aAAK,mBAAmB,UAAU;AAClC,aAAK,yBAAyB;AAAA,MAChC,MAAO;AAAA,IACT,WAAW,KAAK,iCAAiC,WAAW;AAK1D,UAAI,0BAA0B;AAC5B,aAAK,mBAAmB,UAAU;AAAA,MACpC;AACA,WAAK,yBAAyB;AAAA,IAChC;AAAA,EACF;AAAA,EACA,2BAA2B;AACzB,SAAK,SAAS,aAAa,KAAK,cAAc,UAAU,KAAK,cAAc,CAAC,GAAG,IAAI,KAAK,sBAAsB,KAAK,kBAAkB,KAAK,aAAa,CAAC;AAAA,EAC1J;AAAA,EACA,sBAAsB,cAAc,cAAc;AAChD,QAAI,KAAK,iCAAiC,YAAY;AACpD,aAAO;AAAA,QACL;AAAA,QACA,eAAe;AAAA,MACjB;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,4BAA4B,mBAAmB;AAC7D,cAAQ,qCAAqC,mCAAsC,sBAAsB,oBAAmB,IAAI,qBAAqB,oBAAmB;AAAA,IAC1K;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,qBAAoB;AAAA,IAC7B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAWH,SAAS,oBAAoB,QAAQ,QAAQ;AAC3C,SAAO,OAAO,SAAK,yBAAO,OAAK,aAAa,iBAAiB,aAAa,oBAAoB,aAAa,mBAAmB,aAAa,iBAAiB,OAAG,sBAAI,OAAK;AACtK,QAAI,aAAa,iBAAiB,aAAa,mBAAmB;AAChE,aAAO;AAAA,IACT;AACA,UAAM,cAAc,aAAa,mBAAmB,EAAE,SAAS,2BAA2B,YAAY,EAAE,SAAS,2BAA2B,4BAA4B;AACxK,WAAO,cAAc,IAAuC;AAAA,EAC9D,CAAC,OAAG;AAAA,IAAO,YAAU,WAAW;AAAA;AAAA,EAAoC,OAAG,uBAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AAC9F,WAAO;AAAA,EACT,CAAC;AACH;AAMA,IAAM,oBAAoB;AAAA,EACxB,OAAO;AAAA,EACP,UAAU;AAAA,EACV,cAAc;AAAA,EACd,aAAa;AACf;AAKA,IAAM,qBAAqB;AAAA,EACzB,OAAO;AAAA,EACP,UAAU;AAAA,EACV,cAAc;AAAA,EACd,aAAa;AACf;AAeA,IAAM,SAAN,MAAM,QAAO;AAAA,EACX,IAAI,iBAAiB;AACnB,WAAO,KAAK,aAAa,kBAAkB;AAAA,EAC7C;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,aAAa,cAAc;AAAA,EACzC;AAAA,EACA,WAAW;AAAA,EACX;AAAA,EACA,UAAU,OAAO,OAAQ;AAAA,EACzB,eAAe,OAAO,YAAY;AAAA,EAClC,UAAU,OAAO,sBAAsB;AAAA,IACrC,UAAU;AAAA,EACZ,CAAC,KAAK,CAAC;AAAA,EACP,eAAe,OAAO,oBAAqB;AAAA,EAC3C,oBAAoB,KAAK,QAAQ,qBAAqB;AAAA,EACtD,wBAAwB,OAAO,qBAAqB;AAAA,EACpD,gBAAgB,OAAO,aAAa;AAAA,EACpC,WAAW,OAAO,QAAQ;AAAA,EAC1B,sBAAsB,OAAO,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhD,UAAU,IAAI,oBAAQ;AAAA;AAAA;AAAA;AAAA,EAItB,IAAI,SAAS;AAKX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,cAAc;AAChB,WAAO,KAAK,aAAa,eAAe;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOZ,qBAAqB,OAAO,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU9C,sBAAsB,KAAK,QAAQ,uBAAuB;AAAA,EAC1D,SAAS,OAAO,QAAQ;AAAA,IACtB,UAAU;AAAA,EACZ,CAAC,GAAG,KAAK,KAAK,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOf,+BAA+B,CAAC,CAAC,OAAO,cAAc;AAAA,IACpD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,cAAc;AACZ,SAAK,YAAY,KAAK,MAAM;AAC5B,SAAK,sBAAsB,iBAAiB,IAAI,EAAE,UAAU;AAAA,MAC1D,OAAO,OAAK;AACV,aAAK,QAAQ,KAAK,YAAY,+BAA+B,CAAC,KAAK,CAAC;AAAA,MACtE;AAAA,IACF,CAAC;AACD,SAAK,4BAA4B;AAAA,EACnC;AAAA,EACA,qBAAqB,IAAI,yBAAa;AAAA,EACtC,8BAA8B;AAC5B,UAAM,eAAe,KAAK,sBAAsB,OAAO,UAAU,OAAK;AACpE,UAAI;AACF,cAAM,oBAAoB,KAAK,sBAAsB;AACrD,cAAM,oBAAoB,KAAK,sBAAsB;AACrD,YAAI,sBAAsB,QAAQ,sBAAsB,MAAM;AAC5D,eAAK,aAAa,kBAAkB,GAAG,iBAAiB;AACxD,cAAI,aAAa,oBAAoB,EAAE,SAAS,2BAA2B,YAAY,EAAE,SAAS,2BAA2B,2BAA2B;AAItJ,iBAAK,YAAY;AAAA,UACnB,WAAW,aAAa,eAAe;AACrC,iBAAK,YAAY;AAAA,UACnB,WAAW,aAAa,iBAAiB;AACvC,kBAAM,OAAO,EAAE;AACf,kBAAM,aAAa,KAAK,oBAAoB,MAAM,EAAE,KAAK,kBAAkB,aAAa;AACxF,kBAAM,SAAS;AAAA,cACb,YAAY,kBAAkB,OAAO;AAAA,cACrC,MAAM,kBAAkB,OAAO;AAAA,cAC/B,oBAAoB,kBAAkB,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,cAK7C,YAAY,kBAAkB,OAAO,cAAc,KAAK,sBAAsB,WAAW,6BAA6B,kBAAkB,MAAM;AAAA,eAE3I;AAEL,iBAAK,mBAAmB,YAAY,uBAAuB,MAAM,QAAQ;AAAA,cACvE,SAAS,kBAAkB;AAAA,cAC3B,QAAQ,kBAAkB;AAAA,cAC1B,SAAS,kBAAkB;AAAA,YAC7B,CAAC;AAAA,UACH;AAAA,QACF;AAIA,YAAI,oBAAoB,CAAC,GAAG;AAC1B,eAAK,QAAQ,KAAK,CAAC;AAAA,QACrB;AAAA,MACF,SAASU,IAAG;AACV,aAAK,sBAAsB,uBAAuB,KAAKA,EAAC;AAAA,MAC1D;AAAA,IACF,CAAC;AACD,SAAK,mBAAmB,IAAI,YAAY;AAAA,EAC1C;AAAA;AAAA,EAEA,uBAAuB,mBAAmB;AAGxC,SAAK,YAAY,KAAK,YAAY;AAClC,SAAK,sBAAsB,oBAAoB;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAClB,SAAK,4BAA4B;AACjC,QAAI,CAAC,KAAK,sBAAsB,wBAAwB;AACtD,WAAK,0BAA0B,KAAK,SAAS,KAAK,IAAI,GAAG,uBAAuB,KAAK,aAAa,cAAc,CAAC;AAAA,IACnH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,8BAA8B;AAI5B,SAAK,4CAA4C,KAAK,aAAa,4CAA4C,CAAC,KAAK,OAAO,WAAW;AACrI,WAAK,0BAA0B,KAAK,QAAQ,KAAK;AAAA,IACnD,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,0BAA0B,KAAK,QAAQ,OAAO;AAC5C,UAAM,SAAS;AAAA,MACb,YAAY;AAAA,IACd;AAQA,UAAM,gBAAgB,OAAO,eAAe,QAAQ;AAGpD,QAAI,OAAO;AACT,YAAM,YAAY,mBACb;AAEL,aAAO,UAAU;AACjB,aAAO,UAAU;AACjB,UAAI,OAAO,KAAK,SAAS,EAAE,WAAW,GAAG;AACvC,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF;AACA,UAAM,UAAU,KAAK,SAAS,GAAG;AACjC,SAAK,mBAAmB,SAAS,QAAQ,eAAe,MAAM;AAAA,EAChE;AAAA;AAAA,EAEA,IAAI,MAAM;AACR,WAAO,KAAK,aAAa,KAAK,cAAc;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,WAAO,KAAK,sBAAsB;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,2BAA2B;AAC7B,WAAO,KAAK,sBAAsB;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,YAAY,QAAQ;AAClB,KAAC,OAAO,cAAc,eAAe,cAAc,eAAe,MAAM;AACxE,SAAK,SAAS,OAAO,IAAI,iBAAiB;AAC1C,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA,EAEA,UAAU;AAMR,SAAK,QAAQ,YAAY;AACzB,SAAK,sBAAsB,SAAS;AACpC,QAAI,KAAK,yCAAyC;AAChD,WAAK,wCAAwC,YAAY;AACzD,WAAK,0CAA0C;AAAA,IACjD;AACA,SAAK,WAAW;AAChB,SAAK,mBAAmB,YAAY;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiDA,cAAc,UAAU,mBAAmB,CAAC,GAAG;AAC7C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,IAAI,mBAAmB,KAAK,eAAe,WAAW;AAC5D,QAAI,IAAI;AACR,YAAQ,uBAAuB,KAAK,QAAQ,4BAA4B;AAAA,MACtE,KAAK;AACH,YAAI,kCACC,KAAK,eAAe,cACpB;AAEL;AAAA,MACF,KAAK;AACH,YAAI,KAAK,eAAe;AACxB;AAAA,MACF;AACE,YAAI,eAAe;AAAA,IACvB;AACA,QAAI,MAAM,MAAM;AACd,UAAI,KAAK,iBAAiB,CAAC;AAAA,IAC7B;AACA,QAAI;AACJ,QAAI;AACF,YAAM,qBAAqB,aAAa,WAAW,WAAW,KAAK,YAAY,SAAS;AACxF,kCAA4B,4BAA4B,kBAAkB;AAAA,IAC5E,SAAS,GAAG;AAMV,UAAI,OAAO,SAAS,CAAC,MAAM,YAAY,SAAS,CAAC,EAAE,CAAC,MAAM,KAAK;AAQ7D,mBAAW,CAAC;AAAA,MACd;AACA,kCAA4B,KAAK,eAAe;AAAA,IAClD;AACA,WAAO,8BAA8B,2BAA2B,UAAU,GAAG,KAAK,IAAI;AAAA,EACxF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyBA,cAAc,KAAK,SAAS;AAAA,IAC1B,oBAAoB;AAAA,EACtB,GAAG;AACD,UAAM,UAAU,UAAU,GAAG,IAAI,MAAM,KAAK,SAAS,GAAG;AACxD,UAAM,aAAa,KAAK,oBAAoB,MAAM,SAAS,KAAK,UAAU;AAC1E,WAAO,KAAK,mBAAmB,YAAY,uBAAuB,MAAM,MAAM;AAAA,EAChF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+BA,SAAS,UAAU,SAAS;AAAA,IAC1B,oBAAoB;AAAA,EACtB,GAAG;AACD,qBAAiB,QAAQ;AACzB,WAAO,KAAK,cAAc,KAAK,cAAc,UAAU,MAAM,GAAG,MAAM;AAAA,EACxE;AAAA;AAAA,EAEA,aAAa,KAAK;AAChB,WAAO,KAAK,cAAc,UAAU,GAAG;AAAA,EACzC;AAAA;AAAA,EAEA,SAAS,KAAK;AACZ,QAAI;AACF,aAAO,KAAK,cAAc,MAAM,GAAG;AAAA,IACrC,QAAQ;AACN,aAAO,KAAK,cAAc,MAAM,GAAG;AAAA,IACrC;AAAA,EACF;AAAA,EACA,SAAS,KAAK,cAAc;AAC1B,QAAI;AACJ,QAAI,iBAAiB,MAAM;AACzB,gBAAU,mBACL;AAAA,IAEP,WAAW,iBAAiB,OAAO;AACjC,gBAAU,mBACL;AAAA,IAEP,OAAO;AACL,gBAAU;AAAA,IACZ;AACA,QAAI,UAAU,GAAG,GAAG;AAClB,aAAO,aAAa,KAAK,gBAAgB,KAAK,OAAO;AAAA,IACvD;AACA,UAAM,UAAU,KAAK,SAAS,GAAG;AACjC,WAAO,aAAa,KAAK,gBAAgB,SAAS,OAAO;AAAA,EAC3D;AAAA,EACA,iBAAiB,QAAQ;AACvB,WAAO,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,QAAQ,CAAC,KAAK,KAAK,MAAM;AAC7D,UAAI,UAAU,QAAQ,UAAU,QAAW;AACzC,eAAO,GAAG,IAAI;AAAA,MAChB;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AAAA,EACA,mBAAmB,QAAQ,QAAQ,eAAe,QAAQ,cAAc;AACtE,QAAI,KAAK,UAAU;AACjB,aAAO,QAAQ,QAAQ,KAAK;AAAA,IAC9B;AACA,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,cAAc;AAChB,gBAAU,aAAa;AACvB,eAAS,aAAa;AACtB,gBAAU,aAAa;AAAA,IACzB,OAAO;AACL,gBAAU,IAAI,QAAQ,CAAC,KAAK,QAAQ;AAClC,kBAAU;AACV,iBAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,UAAM,SAAS,KAAK,aAAa,IAAI;AACrC,wBAAoB,MAAM,MAAM;AAG9B,qBAAe,MAAM,KAAK,aAAa,OAAO,MAAM,CAAC;AAAA,IACvD,CAAC;AACD,SAAK,sBAAsB,wBAAwB;AAAA,MACjD;AAAA,MACA;AAAA,MACA,gBAAgB,KAAK;AAAA,MACrB,eAAe,KAAK;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,iBAAiB,KAAK,YAAY;AAAA,MAClC,oBAAoB,KAAK;AAAA,IAC3B,CAAC;AAGD,WAAO,QAAQ,MAAM,OAAK;AACxB,aAAO,QAAQ,OAAO,CAAC;AAAA,IACzB,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,eAAe,mBAAmB;AACvD,WAAO,KAAK,qBAAqB,SAAQ;AAAA,EAC3C;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,QAAO;AAAA,IAChB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,QAAQ,CAAC;AAAA,IAC/E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,SAAS,iBAAiB,UAAU;AAClC,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,UAAM,MAAM,SAAS,CAAC;AACtB,QAAI,OAAO,MAAM;AACf,YAAM,IAAI,aAAc,OAA8C,OAAO,cAAc,eAAe,cAAc,+BAA+B,GAAG,qBAAqB,CAAC,EAAE;AAAA,IACpL;AAAA,EACF;AACF;AACA,SAAS,oBAAoB,GAAG;AAC9B,SAAO,EAAE,aAAa,yBAAyB,EAAE,aAAa;AAChE;;;ACt4LA,IAAAC,eAAkC;AAClC,IAAAC,oBAAkE;AAsGlE,IAAM,aAAN,MAAM,YAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA;AAAA,EAEA,YAAY,IAAI,qBAAQ;AAAA,EACxB,YAAY,QAAQ,OAAO,mBAAmB,UAAU,IAAI,kBAAkB;AAC5E,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,oBAAoB;AACzB,SAAK,WAAW;AAChB,SAAK,KAAK;AACV,SAAK,mBAAmB;AACxB,UAAM,UAAU,GAAG,cAAc,SAAS,YAAY;AACtD,SAAK,kBAAkB,YAAY,OAAO,YAAY;AACtD,QAAI,KAAK,iBAAiB;AACxB,WAAK,eAAe,OAAO,OAAO,UAAU,OAAK;AAC/C,YAAI,aAAa,eAAe;AAC9B,eAAK,WAAW;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,WAAK,2BAA2B,GAAG;AAAA,IACrC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOnB,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOrB,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,2BAA2B,aAAa;AACtC,QAAI,KAAK,qBAAqB,QAA0C,KAAK,iBAAiB;AAC5F;AAAA,IACF;AACA,SAAK,oBAAoB,YAAY,WAAW;AAAA,EAClD;AAAA;AAAA;AAAA,EAGA,YAAY,SAAS;AACnB,QAAI,aAAa,UAAU,KAAK,eAAe,MAAM,KAAK,aAAa,UAAa,KAAK,eAAe,KAAK,uBAAuB,KAAK,oBAAoB,KAAK,aAAa;AAC7K,YAAM,IAAI,aAAc,MAAwD,8FAA8F;AAAA,IAChL;AACA,QAAI,KAAK,iBAAiB;AACxB,WAAK,WAAW;AAAA,IAClB;AAGA,SAAK,UAAU,KAAK,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUlB,IAAI,WAAW,mBAAmB;AAChC,QAAI,qBAAqB,MAAM;AAC7B,WAAK,kBAAkB;AACvB,WAAK,2BAA2B,IAAI;AAAA,IACtC,OAAO;AACL,UAAI,UAAU,iBAAiB,GAAG;AAChC,aAAK,kBAAkB;AAAA,MACzB,OAAO;AACL,aAAK,kBAAkB,MAAM,QAAQ,iBAAiB,IAAI,oBAAoB,CAAC,iBAAiB;AAAA,MAClG;AACA,WAAK,2BAA2B,GAAG;AAAA,IACrC;AAAA,EACF;AAAA;AAAA,EAEA,QAAQ,QAAQ,SAAS,UAAU,QAAQ,SAAS;AAClD,UAAM,UAAU,KAAK;AACrB,QAAI,YAAY,MAAM;AACpB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,iBAAiB;AACxB,UAAI,WAAW,KAAK,WAAW,YAAY,UAAU,SAAS;AAC5D,eAAO;AAAA,MACT;AACA,UAAI,OAAO,KAAK,WAAW,YAAY,KAAK,UAAU,SAAS;AAC7D,eAAO;AAAA,MACT;AAAA,IACF;AACA,UAAM,SAAS;AAAA,MACb,oBAAoB,KAAK;AAAA,MACzB,YAAY,KAAK;AAAA,MACjB,OAAO,KAAK;AAAA,MACZ,MAAM,KAAK;AAAA,IACb;AACA,SAAK,OAAO,cAAc,SAAS,MAAM;AAIzC,WAAO,CAAC,KAAK;AAAA,EACf;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,cAAc,YAAY;AAAA,EACjC;AAAA,EACA,aAAa;AACX,UAAM,UAAU,KAAK;AACrB,SAAK,OAAO,YAAY,QAAQ,KAAK,mBAAmB,KAAK,kBAAkB,mBAAmB,KAAK,OAAO,aAAa,OAAO,CAAC,IAAI;AACvI,UAAM,iBAAiB,KAAK,SAAS,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAW5C,2BAA2B,KAAK,MAAM,KAAK,GAAG,cAAc,QAAQ,YAAY,GAAG,MAAM;AAAA;AACzF,SAAK,oBAAoB,QAAQ,cAAc;AAAA,EACjD;AAAA,EACA,oBAAoB,UAAU,WAAW;AACvC,UAAM,WAAW,KAAK;AACtB,UAAM,gBAAgB,KAAK,GAAG;AAC9B,QAAI,cAAc,MAAM;AACtB,eAAS,aAAa,eAAe,UAAU,SAAS;AAAA,IAC1D,OAAO;AACL,eAAS,gBAAgB,eAAe,QAAQ;AAAA,IAClD;AAAA,EACF;AAAA,EACA,IAAI,UAAU;AACZ,QAAI,KAAK,oBAAoB,MAAM;AACjC,aAAO;AAAA,IACT,WAAW,UAAU,KAAK,eAAe,GAAG;AAC1C,aAAO,KAAK;AAAA,IACd;AACA,WAAO,KAAK,OAAO,cAAc,KAAK,iBAAiB;AAAA;AAAA;AAAA,MAGrD,YAAY,KAAK,eAAe,SAAY,KAAK,aAAa,KAAK;AAAA,MACnE,aAAa,KAAK;AAAA,MAClB,UAAU,KAAK;AAAA,MACf,qBAAqB,KAAK;AAAA,MAC1B,kBAAkB,KAAK;AAAA,IACzB,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAe,kBAAkB,MAAM,GAAM,kBAAkB,cAAc,GAAM,kBAAkB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,gBAAgB,CAAC;AAAA,EACvQ;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,IAClC,UAAU;AAAA,IACV,cAAc,SAAS,wBAAwB,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,oCAAoC,QAAQ;AAC1E,iBAAO,IAAI,QAAQ,OAAO,QAAQ,OAAO,SAAS,OAAO,UAAU,OAAO,QAAQ,OAAO,OAAO;AAAA,QAClG,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,UAAU,IAAI,MAAM;AAAA,MACrC;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,kBAAkB,CAAC,GAAG,oBAAoB,oBAAoB,gBAAgB;AAAA,MAC9E,oBAAoB,CAAC,GAAG,sBAAsB,sBAAsB,gBAAgB;AAAA,MACpF,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,YAAY;AAAA,IACd;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,CAAC,iBAAiB,kBAAkB,mBAAmB,iBAAiB,gBAAgB,CAAC;AAAA,IAC3G,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAuEH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU,CAAC;AAAA,EACX;AAAA,EACA;AAAA,EACA,YAAY;AAAA,EACZ,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,0BAA0B;AAAA,IACxB,OAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,iBAAiB,IAAI,aAAa;AAAA,EAClC,YAAY,QAAQ,SAAS,UAAU,KAAK,MAAM;AAChD,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,2BAA2B,OAAO,OAAO,UAAU,OAAK;AAC3D,UAAI,aAAa,eAAe;AAC9B,aAAK,OAAO;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,qBAAqB;AAEnB,yBAAG,KAAK,MAAM,aAAS,iBAAG,IAAI,CAAC,EAAE,SAAK,4BAAS,CAAC,EAAE,UAAU,OAAK;AAC/D,WAAK,OAAO;AACZ,WAAK,6BAA6B;AAAA,IACpC,CAAC;AAAA,EACH;AAAA,EACA,+BAA+B;AAC7B,SAAK,8BAA8B,YAAY;AAC/C,UAAM,iBAAiB,CAAC,GAAG,KAAK,MAAM,QAAQ,GAAG,KAAK,IAAI,EAAE,OAAO,UAAQ,CAAC,CAAC,IAAI,EAAE,IAAI,UAAQ,KAAK,SAAS;AAC7G,SAAK,mCAA+B,mBAAK,cAAc,EAAE,SAAK,4BAAS,CAAC,EAAE,UAAU,UAAQ;AAC1F,UAAI,KAAK,cAAc,KAAK,aAAa,KAAK,MAAM,EAAE,IAAI,GAAG;AAC3D,aAAK,OAAO;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,IAAI,iBAAiB,MAAM;AACzB,UAAM,UAAU,MAAM,QAAQ,IAAI,IAAI,OAAO,KAAK,MAAM,GAAG;AAC3D,SAAK,UAAU,QAAQ,OAAO,OAAK,CAAC,CAAC,CAAC;AAAA,EACxC;AAAA;AAAA,EAEA,YAAY,SAAS;AACnB,SAAK,OAAO;AAAA,EACd;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,yBAAyB,YAAY;AAC1C,SAAK,8BAA8B,YAAY;AAAA,EACjD;AAAA,EACA,SAAS;AACP,QAAI,CAAC,KAAK,SAAS,CAAC,KAAK,OAAO,UAAW;AAC3C,mBAAe,MAAM;AACnB,YAAM,iBAAiB,KAAK,eAAe;AAC3C,WAAK,QAAQ,QAAQ,OAAK;AACxB,YAAI,gBAAgB;AAClB,eAAK,SAAS,SAAS,KAAK,QAAQ,eAAe,CAAC;AAAA,QACtD,OAAO;AACL,eAAK,SAAS,YAAY,KAAK,QAAQ,eAAe,CAAC;AAAA,QACzD;AAAA,MACF,CAAC;AACD,UAAI,kBAAkB,KAAK,0BAA0B,QAAW;AAC9D,aAAK,SAAS,aAAa,KAAK,QAAQ,eAAe,gBAAgB,KAAK,sBAAsB,SAAS,CAAC;AAAA,MAC9G,OAAO;AACL,aAAK,SAAS,gBAAgB,KAAK,QAAQ,eAAe,cAAc;AAAA,MAC1E;AAEA,UAAI,KAAK,cAAc,gBAAgB;AACrC,aAAK,YAAY;AACjB,aAAK,IAAI,aAAa;AAEtB,aAAK,eAAe,KAAK,cAAc;AAAA,MACzC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,aAAa,QAAQ;AACnB,UAAM,UAAU,qBAAqB,KAAK,uBAAuB,IAAI,KAAK;AAAA;AAAA,MAE1E,KAAK,wBAAwB,SAAS;AAAA;AACtC,WAAO,UAAQ;AACb,YAAM,UAAU,KAAK;AACrB,aAAO,UAAU,OAAO,SAAS,SAAS,OAAO,IAAI;AAAA,IACvD;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,UAAM,kBAAkB,KAAK,aAAa,KAAK,MAAM;AACrD,WAAO,KAAK,QAAQ,gBAAgB,KAAK,IAAI,KAAK,KAAK,MAAM,KAAK,eAAe;AAAA,EACnF;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAqB,kBAAkB,MAAM,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,YAAY,CAAC,CAAC;AAAA,EAC3O;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,IACxC,gBAAgB,SAAS,gCAAgC,IAAI,KAAK,UAAU;AAC1E,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,YAAY,CAAC;AAAA,MAC3C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ;AAAA,MAC3D;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,kBAAkB;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,MACP,gBAAgB;AAAA,IAClB;AAAA,IACA,UAAU,CAAC,kBAAkB;AAAA,IAC7B,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,QACjB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAIH,SAAS,qBAAqB,SAAS;AACrC,SAAO,CAAC,CAAC,QAAQ;AACnB;AASA,IAAM,qBAAN,MAAyB;AAAC;AAY1B,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,QAAQ,OAAO,IAAI;AACjB,WAAO,GAAG,EAAE,SAAK,8BAAW,UAAM,iBAAG,IAAI,CAAC,CAAC;AAAA,EAC7C;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,mBAAkB;AAAA,IAC3B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,QAAQ,OAAO,IAAI;AACjB,eAAO,iBAAG,IAAI;AAAA,EAChB;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,cAAa;AAAA,IACtB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAaH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,QAAQ,UAAU,oBAAoB,QAAQ;AACxD,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,qBAAqB;AAC1B,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,kBAAkB;AAChB,SAAK,eAAe,KAAK,OAAO,OAAO,SAAK,0BAAO,OAAK,aAAa,aAAa,OAAG,6BAAU,MAAM,KAAK,QAAQ,CAAC,CAAC,EAAE,UAAU,MAAM;AAAA,IAAC,CAAC;AAAA,EAC1I;AAAA,EACA,UAAU;AACR,WAAO,KAAK,cAAc,KAAK,UAAU,KAAK,OAAO,MAAM;AAAA,EAC7D;AAAA;AAAA,EAEA,cAAc;AACZ,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,YAAY;AAAA,IAChC;AAAA,EACF;AAAA,EACA,cAAc,UAAU,QAAQ;AAC9B,UAAM,MAAM,CAAC;AACb,eAAW,SAAS,QAAQ;AAC1B,UAAI,MAAM,aAAa,CAAC,MAAM,WAAW;AACvC,cAAM,YAAY,0BAA0B,MAAM,WAAW,UAAU,UAAU,MAAM,IAAI,EAAE;AAAA,MAC/F;AACA,YAAM,0BAA0B,MAAM,aAAa;AACnD,YAAM,sBAAsB,MAAM,mBAAmB;AASrD,UAAI,MAAM,gBAAgB,CAAC,MAAM,iBAAiB,MAAM,YAAY,UAAa,MAAM,iBAAiB,CAAC,MAAM,kBAAkB;AAC/H,YAAI,KAAK,KAAK,cAAc,yBAAyB,KAAK,CAAC;AAAA,MAC7D;AACA,UAAI,MAAM,YAAY,MAAM,eAAe;AACzC,YAAI,KAAK,KAAK,cAAc,qBAAqB,MAAM,YAAY,MAAM,aAAa,CAAC;AAAA,MACzF;AAAA,IACF;AACA,eAAO,mBAAK,GAAG,EAAE,SAAK,4BAAS,CAAC;AAAA,EAClC;AAAA,EACA,cAAc,UAAU,OAAO;AAC7B,WAAO,KAAK,mBAAmB,QAAQ,OAAO,MAAM;AAClD,UAAI;AACJ,UAAI,MAAM,gBAAgB,MAAM,YAAY,QAAW;AACrD,0BAAkB,KAAK,OAAO,aAAa,UAAU,KAAK;AAAA,MAC5D,OAAO;AACL,8BAAkB,iBAAG,IAAI;AAAA,MAC3B;AACA,YAAM,yBAAyB,gBAAgB,SAAK,4BAAS,YAAU;AACrE,YAAI,WAAW,MAAM;AACnB,qBAAO,iBAAG,MAAM;AAAA,QAClB;AACA,cAAM,gBAAgB,OAAO;AAC7B,cAAM,kBAAkB,OAAO;AAG/B,eAAO,KAAK,cAAc,OAAO,YAAY,UAAU,OAAO,MAAM;AAAA,MACtE,CAAC,CAAC;AACF,UAAI,MAAM,iBAAiB,CAAC,MAAM,kBAAkB;AAClD,cAAM,iBAAiB,KAAK,OAAO,cAAc,KAAK;AACtD,mBAAO,mBAAK,CAAC,wBAAwB,cAAc,CAAC,EAAE,SAAK,4BAAS,CAAC;AAAA,MACvE,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAoB,SAAS,MAAM,GAAM,SAAY,mBAAmB,GAAM,SAAS,kBAAkB,GAAM,SAAS,kBAAkB,CAAC;AAAA,EAC9K;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iBAAgB;AAAA,IACzB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,kBAAkB,IAAI,eAAe,EAAE;AAC7C,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT,aAAa;AAAA,EACb,aAAa;AAAA,EACb,QAAQ,CAAC;AAAA;AAAA,EAET,YAAY,eAAe,aAAa,kBAAkB,MAAM,UAAU,CAAC,GAAG;AAC5E,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,mBAAmB;AACxB,SAAK,OAAO;AACZ,SAAK,UAAU;AAEf,YAAQ,8BAA8B;AACtC,YAAQ,oBAAoB;AAAA,EAC9B;AAAA,EACA,OAAO;AAIL,QAAI,KAAK,QAAQ,8BAA8B,YAAY;AACzD,WAAK,iBAAiB,4BAA4B,QAAQ;AAAA,IAC5D;AACA,SAAK,2BAA2B,KAAK,mBAAmB;AACxD,SAAK,2BAA2B,KAAK,oBAAoB;AAAA,EAC3D;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK,YAAY,OAAO,UAAU,OAAK;AAC5C,UAAI,aAAa,iBAAiB;AAEhC,aAAK,MAAM,KAAK,MAAM,IAAI,KAAK,iBAAiB,kBAAkB;AAClE,aAAK,aAAa,EAAE;AACpB,aAAK,aAAa,EAAE,gBAAgB,EAAE,cAAc,eAAe;AAAA,MACrE,WAAW,aAAa,eAAe;AACrC,aAAK,SAAS,EAAE;AAChB,aAAK,oBAAoB,GAAG,KAAK,cAAc,MAAM,EAAE,iBAAiB,EAAE,QAAQ;AAAA,MACpF,WAAW,aAAa,qBAAqB,EAAE,SAAS,sBAAsB,0BAA0B;AACtG,aAAK,aAAa;AAClB,aAAK,aAAa;AAClB,aAAK,oBAAoB,GAAG,KAAK,cAAc,MAAM,EAAE,GAAG,EAAE,QAAQ;AAAA,MACtE;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,YAAY,OAAO,UAAU,OAAK;AAC5C,UAAI,EAAE,aAAa,QAAS;AAE5B,UAAI,EAAE,UAAU;AACd,YAAI,KAAK,QAAQ,8BAA8B,OAAO;AACpD,eAAK,iBAAiB,iBAAiB,CAAC,GAAG,CAAC,CAAC;AAAA,QAC/C,WAAW,KAAK,QAAQ,8BAA8B,WAAW;AAC/D,eAAK,iBAAiB,iBAAiB,EAAE,QAAQ;AAAA,QACnD;AAAA,MAEF,OAAO;AACL,YAAI,EAAE,UAAU,KAAK,QAAQ,oBAAoB,WAAW;AAC1D,eAAK,iBAAiB,eAAe,EAAE,MAAM;AAAA,QAC/C,WAAW,KAAK,QAAQ,8BAA8B,YAAY;AAChE,eAAK,iBAAiB,iBAAiB,CAAC,GAAG,CAAC,CAAC;AAAA,QAC/C;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB,aAAa,QAAQ;AACvC,SAAK,KAAK,kBAAkB,MAAM;AAIhC,iBAAW,MAAM;AACf,aAAK,KAAK,IAAI,MAAM;AAClB,eAAK,YAAY,OAAO,KAAK,IAAI,OAAO,aAAa,KAAK,eAAe,aAAa,KAAK,MAAM,KAAK,UAAU,IAAI,MAAM,MAAM,CAAC;AAAA,QACnI,CAAC;AAAA,MACH,GAAG,CAAC;AAAA,IACN,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,0BAA0B,YAAY;AAC3C,SAAK,0BAA0B,YAAY;AAAA,EAC7C;AAAA,EACA,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,IAAG,iBAAiB;AAAA,EACtB;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,gBAAe;AAAA,EAC1B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AAsCH,SAAS,cAAc,WAAW,UAAU;AAC1C,SAAO,yBAAyB,CAAC;AAAA,IAC/B,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU;AAAA,EACZ,GAAG,OAAO,cAAc,eAAe,YAAY;AAAA,IACjD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,IAAI,CAAC,GAAG;AAAA,IACN,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,MAAM,CAAC,MAAM;AAAA,EACf,GAAG;AAAA,IACD,SAAS;AAAA,IACT,OAAO;AAAA,IACP,YAAY;AAAA,EACd,GAAG,SAAS,IAAI,aAAW,QAAQ,UAAU,CAAC,CAAC;AACjD;AACA,SAAS,UAAU,QAAQ;AACzB,SAAO,OAAO,YAAY;AAC5B;AAIA,SAAS,cAAc,MAAM,WAAW;AACtC,SAAO;AAAA,IACL,OAAO;AAAA,IACP,YAAY;AAAA,EACd;AACF;AAKA,IAAM,qBAAqB,IAAI,eAAe,IAAI;AAAA,EAChD,YAAY;AAAA,EACZ,SAAS,MAAM;AACjB,CAAC;AACD,IAAM,+BAA+B;AAAA,EACnC,SAAS;AAAA,EACT,OAAO;AAAA,EACP,aAAa;AACX,WAAO,MAAM;AACX,UAAI,CAAC,OAAO,kBAAkB,GAAG;AAC/B,gBAAQ,KAAK,yGAA8G;AAAA,MAC7H;AAAA,IACF;AAAA,EACF;AACF;AAkBA,SAAS,cAAc,QAAQ;AAC7B,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU;AAAA,EACZ,GAAG,OAAO,cAAc,eAAe,YAAY,+BAA+B,CAAC,CAAC;AACtF;AA0BA,SAAS,sBAAsB,UAAU,CAAC,GAAG;AAC3C,QAAM,YAAY,CAAC;AAAA,IACjB,SAAS;AAAA,IACT,YAAY,MAAM;AAChB,YAAM,mBAAmB,OAAO,gBAAgB;AAChD,YAAM,OAAO,OAAO,MAAM;AAC1B,YAAM,cAAc,OAAO,qBAAqB;AAChD,YAAM,gBAAgB,OAAO,aAAa;AAC1C,aAAO,IAAI,eAAe,eAAe,aAAa,kBAAkB,MAAM,OAAO;AAAA,IACvF;AAAA,EACF,CAAC;AACD,SAAO,cAAc,GAAoD,SAAS;AACpF;AACA,SAAS,uBAAuB;AAC9B,QAAM,WAAW,OAAO,QAAQ;AAChC,SAAO,8BAA4B;AACjC,UAAM,MAAM,SAAS,IAAI,cAAc;AACvC,QAAI,6BAA6B,IAAI,WAAW,CAAC,GAAG;AAClD;AAAA,IACF;AACA,UAAM,SAAS,SAAS,IAAI,MAAM;AAClC,UAAM,gBAAgB,SAAS,IAAI,cAAc;AACjD,QAAI,SAAS,IAAI,kBAAkB,MAAM,GAA8C;AACrF,aAAO,kBAAkB;AAAA,IAC3B;AACA,aAAS,IAAI,kBAAkB,MAAM,YAAY,QAAQ,GAAG,gBAAgB;AAC5E,aAAS,IAAI,iBAAiB,MAAM,YAAY,QAAQ,GAAG,KAAK;AAChE,WAAO,uBAAuB,IAAI,eAAe,CAAC,CAAC;AACnD,QAAI,CAAC,cAAc,QAAQ;AACzB,oBAAc,KAAK;AACnB,oBAAc,SAAS;AACvB,oBAAc,YAAY;AAAA,IAC5B;AAAA,EACF;AACF;AAMA,IAAM,iBAAiB,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,6BAA6B,IAAI;AAAA,EACzH,SAAS,MAAM;AACb,WAAO,IAAI,qBAAQ;AAAA,EACrB;AACF,CAAC;AACD,IAAM,qBAAqB,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,uBAAuB,IAAI;AAAA,EACvH,YAAY;AAAA,EACZ,SAAS,MAAM;AAAA;AACjB,CAAC;AA2BD,SAAS,uCAAuC;AAC9C,QAAM,YAAY,CAAC;AAAA,IACjB,SAAS;AAAA,IACT,UAAU;AAAA;AAAA,EACZ,GAAG,sBAAsB,MAAM;AAC7B,UAAM,WAAW,OAAO,QAAQ;AAChC,UAAM,sBAAsB,SAAS,IAAI,sBAAsB,QAAQ,QAAQ,CAAC;AAChF,WAAO,oBAAoB,KAAK,MAAM;AACpC,aAAO,IAAI,QAAQ,aAAW;AAC5B,cAAM,SAAS,SAAS,IAAI,MAAM;AAClC,cAAM,gBAAgB,SAAS,IAAI,cAAc;AACjD,4BAAoB,QAAQ,MAAM;AAGhC,kBAAQ,IAAI;AAAA,QACd,CAAC;AACD,iBAAS,IAAI,qBAAqB,EAAE,qBAAqB,MAAM;AAI7D,kBAAQ,IAAI;AACZ,iBAAO,cAAc,aAAS,iBAAG,MAAM,IAAI;AAAA,QAC7C;AACA,eAAO,kBAAkB;AAAA,MAC3B,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,CAAC;AACF,SAAO,cAAc,GAAmE,SAAS;AACnG;AA2BA,SAAS,gCAAgC;AACvC,QAAM,YAAY,CAAC,sBAAsB,MAAM;AAC7C,WAAO,MAAM,EAAE,4BAA4B;AAAA,EAC7C,CAAC,GAAG;AAAA,IACF,SAAS;AAAA,IACT,UAAU;AAAA;AAAA,EACZ,CAAC;AACD,SAAO,cAAc,GAA4D,SAAS;AAC5F;AAyBA,SAAS,mBAAmB;AAC1B,MAAI,YAAY,CAAC;AACjB,MAAI,OAAO,cAAc,eAAe,WAAW;AACjD,gBAAY,CAAC;AAAA,MACX,SAAS;AAAA,MACT,OAAO;AAAA,MACP,YAAY,MAAM;AAChB,cAAM,SAAS,OAAO,MAAM;AAC5B,eAAO,MAAM,OAAO,OAAO,UAAU,OAAK;AAExC,kBAAQ,QAAQ,iBAAiB,EAAE,YAAY,IAAI,EAAE;AACrD,kBAAQ,IAAI,eAAe,CAAC,CAAC;AAC7B,kBAAQ,IAAI,CAAC;AACb,kBAAQ,WAAW;AAAA,QAErB,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH,OAAO;AACL,gBAAY,CAAC;AAAA,EACf;AACA,SAAO,cAAc,GAA+C,SAAS;AAC/E;AACA,IAAM,mBAAmB,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,qBAAqB,EAAE;AA2BnH,SAAS,eAAe,oBAAoB;AAC1C,QAAM,YAAY,CAAC;AAAA,IACjB,SAAS;AAAA,IACT,aAAa;AAAA,EACf,GAAG;AAAA,IACD,SAAS;AAAA,IACT,aAAa;AAAA,EACf,CAAC;AACD,SAAO,cAAc,GAA6C,SAAS;AAC7E;AA4BA,SAAS,iBAAiB,SAAS;AACjC,QAAM,YAAY,CAAC;AAAA,IACjB,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC;AACD,SAAO,cAAc,GAAsD,SAAS;AACtF;AAyBA,SAAS,mBAAmB;AAC1B,QAAM,YAAY,CAAC;AAAA,IACjB,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC;AACD,SAAO,cAAc,GAAqD,SAAS;AACrF;AAoCA,SAAS,2BAA2B,SAAS;AAC3C,QAAM,YAAY,CAAC;AAAA,IACjB,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC;AACD,SAAO,cAAc,GAAyD,SAAS;AACzF;AAuCA,SAAS,4BAA4B;AACnC,QAAM,YAAY,CAAC,4BAA4B;AAAA,IAC7C,SAAS;AAAA,IACT,aAAa;AAAA,EACf,CAAC;AACD,SAAO,cAAc,GAAwD,SAAS;AACxF;AA4BA,SAAS,oBAAoB,SAAS;AACpC,yBAAwB,yBAAyB;AACjD,QAAM,YAAY,CAAC;AAAA,IACjB,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,MACR,oBAAoB,CAAC,CAAC,SAAS;AAAA,OAC5B;AAAA,EAEP,CAAC;AACD,SAAO,cAAc,GAAkD,SAAS;AAClF;AAKA,IAAM,oBAAoB,CAAC,cAAc,YAAY,kBAAkB,qBAAqB;AAI5F,IAAM,uBAAuB,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,mCAAmC,EAAE;AAKrI,IAAM,mBAAmB;AAAA,EAAC;AAAA,EAAU;AAAA,IAClC,SAAS;AAAA,IACT,UAAU;AAAA,EACZ;AAAA,EAAG;AAAA,EAAQ;AAAA,EAAwB;AAAA,IACjC,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,MAAM,CAAC,MAAM;AAAA,EACf;AAAA,EAAG;AAAA;AAAA;AAAA,EAGH,OAAO,cAAc,eAAe,YAAY;AAAA,IAC9C,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,IAAI,CAAC;AAAC;AAsBN,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,cAAc;AACZ,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,aAAO,sBAAsB;AAAA,QAC3B,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,OAAO,QAAQ,QAAQ,QAAQ;AAC7B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,kBAAkB,OAAO,cAAc,eAAe,YAAY,QAAQ,gBAAgB,iBAAiB,EAAE,aAAa,CAAC,IAAI,CAAC,GAAG;AAAA,QAC7I,SAAS;AAAA,QACT,OAAO;AAAA,QACP,UAAU;AAAA,MACZ,GAAG,OAAO,cAAc,eAAe,YAAY;AAAA,QACjD,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,MAAM,CAAC,CAAC,QAAQ,IAAI,SAAS,GAAG,IAAI,SAAS,CAAC,CAAC;AAAA,MACjD,IAAI,CAAC,GAAG,QAAQ,eAAe;AAAA,QAC7B,SAAS;AAAA,QACT,UAAU,OAAO;AAAA,MACnB,IAAI,CAAC,GAAG;AAAA,QACN,SAAS;AAAA,QACT,UAAU,SAAS,SAAS,CAAC;AAAA,MAC/B,GAAG,QAAQ,UAAU,4BAA4B,IAAI,4BAA4B,GAAG,sBAAsB,GAAG,QAAQ,qBAAqB,eAAe,OAAO,kBAAkB,EAAE,aAAa,CAAC,GAAG,QAAQ,oBAAoB,yBAAyB,MAAM,IAAI,CAAC,GAAG,QAAQ,wBAAwB,0BAA0B,EAAE,aAAa,CAAC,GAAG,QAAQ,wBAAwB,oBAAoB,EAAE,aAAa,CAAC,GAAG,yBAAyB,CAAC;AAAA,IACxb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,OAAO,SAAS,QAAQ;AACtB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,OAAO;AAAA,QACP,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,cAAc,YAAY,kBAAkB,qBAAqB;AAAA,IAC3E,SAAS,CAAC,cAAc,YAAY,kBAAkB,qBAAqB;AAAA,EAC7E,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,SAAS,wBAAwB;AAC/B,SAAO;AAAA,IACL,SAAS;AAAA,IACT,YAAY,MAAM;AAChB,YAAM,mBAAmB,OAAO,gBAAgB;AAChD,YAAM,OAAO,OAAO,MAAM;AAC1B,YAAM,SAAS,OAAO,oBAAoB;AAC1C,YAAM,cAAc,OAAO,qBAAqB;AAChD,YAAM,gBAAgB,OAAO,aAAa;AAC1C,UAAI,OAAO,cAAc;AACvB,yBAAiB,UAAU,OAAO,YAAY;AAAA,MAChD;AACA,aAAO,IAAI,eAAe,eAAe,aAAa,kBAAkB,MAAM,MAAM;AAAA,IACtF;AAAA,EACF;AACF;AAGA,SAAS,8BAA8B;AACrC,SAAO;AAAA,IACL,SAAS;AAAA,IACT,UAAU;AAAA,EACZ;AACF;AAGA,SAAS,8BAA8B;AACrC,SAAO;AAAA,IACL,SAAS;AAAA,IACT,UAAU;AAAA,EACZ;AACF;AACA,SAAS,oBAAoB,QAAQ;AACnC,MAAI,QAAQ;AACV,UAAM,IAAI,aAAc,MAAmD,4KAAiL;AAAA,EAC9P;AACA,SAAO;AACT;AAGA,SAAS,yBAAyB,QAAQ;AACxC,SAAO,CAAC,OAAO,sBAAsB,aAAa,8BAA8B,EAAE,aAAa,CAAC,GAAG,OAAO,sBAAsB,oBAAoB,qCAAqC,EAAE,aAAa,CAAC,CAAC;AAC5M;AAQA,IAAM,qBAAqB,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,uBAAuB,EAAE;AACvH,SAAS,2BAA2B;AAClC,SAAO;AAAA;AAAA;AAAA,IAGP;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,IAAG;AAAA,MACD,SAAS;AAAA,MACT,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,EAAC;AACH;;;AChuDA,IAAAC,eAAO;AACP,IAAAC,oBAAO;AAYP,SAAS,cAAc,WAAW;AAChC,SAAO,UAAU,IAAI,cAAY,IAAI,WAAW,OAAO,QAAQ,EAAE,SAAS,GAAG,MAAM,CAAC;AACtF;AAUA,SAAS,iBAAiB,WAAW;AACnC,SAAO,UAAU,IAAI,cAAY,IAAI,WAAW,OAAO,QAAQ,EAAE,YAAY,GAAG,MAAM,CAAC;AACzF;AAUA,SAAS,sBAAsB,WAAW;AACxC,SAAO,UAAU,IAAI,cAAY,IAAI,WAAW,OAAO,QAAQ,EAAE,iBAAiB,GAAG,MAAM,CAAC;AAC9F;AAUA,SAAS,mBAAmB,WAAW;AACrC,SAAO,UAAU,IAAI,cAAY,IAAI,WAAW,OAAO,QAAQ,EAAE,cAAc,GAAG,MAAM,CAAC;AAC3F;AAUA,SAAS,aAAa,UAAU;AAC9B,SAAO,IAAI,WAAW,OAAO,QAAQ,EAAE,QAAQ,GAAG,MAAM;AAC1D;AAUA,IAAM,UAAU,IAAI,QAAQ,SAAS;", "names": ["tree", "match", "res", "commands", "noMatch", "EventType", "NavigationCancellationCode", "NavigationSkippedCode", "t", "c", "node", "map", "contexts", "canActivate", "s", "last$1", "input", "from", "e", "import_rxjs", "import_operators", "import_rxjs", "import_operators"]}